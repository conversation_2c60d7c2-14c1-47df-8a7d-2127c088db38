package modelutil

// import (
// 	"encoding/json"
// 	"strings"
// 	"time"

// 	"futures-asset/internal/delivery/http/payload"
// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/entity"
// 	"futures-asset/internal/domain/repository"
// 	"futures-asset/internal/domain/usecase"
// 	"futures-asset/util"

// 	"github.com/shopspring/decimal"
// )

// func NewBillSwapWithAsset(param *payload.IncrParam, contractCode string, _billType int) *repository.BillAssetSync {
// 	bill := entity.BillAsset{
// 		UID:          param.UID,
// 		OperateId:    param.OrderId,
// 		BillId:       util.GenerateId(),
// 		ContractCode: contractCode,
// 		Currency:     strings.ToUpper(param.Currency),
// 		BillType:     _billType,
// 		FromPair:     param.FromPair,
// 		ToPair:       param.ToPair,
// 		FromAccount:  param.FromAccount,
// 		ToAccount:    param.ToAccount,
// 		Amount:       param.Amount,
// 		OperateTime:  time.Now().UnixNano(),
// 	}
// 	if _billType == domain.BillTypeInnerOut {
// 		bill.Amount = bill.Amount.Neg()
// 	}
// 	billSwap := repository.BillAssetSync{BillAsset: bill}
// 	return &billSwap
// }

// // NewDbPosSwapSync 创建实体-同步更新DB中 pos_swap 表中记录实体
// // @param _pos 推送仓位变化
// func NewDbPosSwapSync(_pos repository.PosSwap, _operateTime int64) *repository.LogPosSync {
// 	logPosSync := repository.LogPosSync{
// 		Pos: repository.PosSwap{
// 			PosId: _pos.PosId,
// 			UID:   _pos.UID, ContractCode: _pos.ContractCode,
// 			Currency: _pos.Currency, PosAvailable: _pos.PosAvailable,
// 			PosSide: _pos.PosSide, Leverage: _pos.Leverage,
// 			Pos:            _pos.Pos,
// 			IsolatedMargin: _pos.IsolatedMargin,
// 			TrialMargin:    _pos.TrialMargin,
// 			MarginMode:     _pos.MarginMode,
// 			OpenPriceAvg:   _pos.OpenPriceAvg,
// 			OpenTime:       _pos.OpenTime,
// 			ProfitReal:     _pos.ProfitReal,
// 			AwardOpIds:     _pos.AwardOpIds,
// 		},
// 		PosSwap: entity.Position{
// 			Id:              _pos.PosId,
// 			UID:             _pos.UID,
// 			UserType:        int(_pos.UserType),
// 			ContractCode:    _pos.ContractCode,
// 			Currency:        _pos.Currency,
// 			AccountType:     _pos.AccountType,
// 			PosAvailable:    _pos.PosAvailable,
// 			PosSide:         _pos.PosSide,
// 			Leverage:        _pos.Leverage,
// 			Pos:             _pos.Pos,
// 			IsolatedMargin:  _pos.IsolatedMargin,
// 			MarginMode:      _pos.MarginMode,
// 			OpenPriceAvg:    _pos.OpenPriceAvg,
// 			OpenTime:        _pos.OpenTime,
// 			ProfitReal:      _pos.ProfitReal,
// 			Subsidy:         _pos.Subsidy,
// 			PosStatus:       int32(_pos.PosStatus),
// 			LiquidationType: int32(_pos.LiquidationType),
// 			CreateTime:      _operateTime,
// 			UpdateTime:      _operateTime,
// 		},
// 	}
// 	return &logPosSync
// }

// func NewLogAssetSync(asset *repository.AssetSwap, currency string, optTime int64) *repository.LogAssetSync {
// 	frozen, _ := json.Marshal(asset.Frozen)
// 	assetSync := repository.LogAssetSync{
// 		AssetSwap: entity.Wallet{
// 			UID:        asset.UID,
// 			Currency:   strings.ToUpper(currency),
// 			Balance:    asset.CBalance(currency),
// 			Frozen:     string(frozen),
// 			CreateTime: optTime,
// 			UpdateTime: optTime,
// 		},
// 	}
// 	return &assetSync
// }

// func LoadTrialAssetByBase(_trialBase *repository.TrialBase, _trialAsset *entity.TrialAsset) {
// 	_trialAsset.UID = _trialBase.UID
// 	_trialAsset.Type = _trialBase.Type
// 	_trialAsset.MaxLeverage = _trialBase.MaxLeverage
// 	_trialAsset.MinHoldTime = _trialBase.MinHoldTime
// 	_trialAsset.Currency = _trialBase.Currency
// 	_trialAsset.ActivityId = _trialBase.ActivityId
// 	_trialAsset.AwardOpId = _trialBase.AwardOpId
// 	_trialAsset.InvalidTime = _trialBase.InvalidTime
// 	_trialAsset.EffectiveTime = _trialBase.EffectiveTime
// 	_trialAsset.WarningTime = _trialBase.WarningTime
// 	_trialAsset.AwardTime = _trialBase.AwardTime
// 	_trialAsset.TrialAssetStatus = _trialBase.TrialAssetStatus
// 	_trialAsset.AwardAmount = _trialBase.AwardAmount
// 	_trialAsset.RecycleAmount = _trialBase.AwardAmount
// }

// func NewTrialMqCmsAsset(trialAsset *entity.TrialAsset) *repository.MqCmsAsset {
// 	mqCmsAsset := repository.MqCmsAsset{
// 		Currency:    trialAsset.Currency,
// 		OrderId:     trialAsset.AwardOpId,
// 		UID:         trialAsset.UID,
// 		OperateTime: time.Now().UnixNano(),
// 		OperateType: trialAsset.OpType,
// 	}
// 	mqCmsAsset.TAsset = repository.CmsTrialAsset{
// 		TAmount: trialAsset.Amount,
// 	}
// 	mqCmsAsset.TAsset.TDetail = append(mqCmsAsset.TAsset.TDetail, trialAsset)
// 	return &mqCmsAsset
// }

// func NewOptionRecord(_req payload.OptionTradeParam) ([]*repository.MqCmsAsset, []repository.BillOptionSync) {
// 	assets := make([]*repository.MqCmsAsset, 0)
// 	bills := make([]repository.BillOptionSync, 0)

// 	for _, order := range _req.Orders {
// 		premium := &repository.MqCmsAsset{
// 			ContractCode: util.ContractCode(_req.Base, _req.Quote),
// 			Currency:     strings.ToUpper(_req.Quote),
// 			OrderId:      order.OrderId,
// 			UID:          _req.UID,
// 			UserType:     _req.UserType,
// 			Platform:     _req.Platform,
// 			DealAmount:   order.Amount,
// 			DealPrice:    order.Price,
// 			Amount:       order.Premium,
// 			OperateType:  domain.BillTypeOptionPremium,
// 			OperateTime:  _req.OperateTime,
// 		}
// 		// 权利金
// 		assets = append(assets, premium)
// 		bills = append(bills, *repository.NewBillOptionSync(_req.UID, _req.Base, _req.Quote, domain.BillTypeOptionPremium, util.AccountType(_req.AccountType), _req.OptionType, order.Premium, order.OptionId, order.OrderId))
// 		// 手续费
// 		fee := *premium
// 		fee.ChannelCode = _req.ChannelCode
// 		fee.AgentUserId = _req.AgentUserId
// 		fee.AgentStatus = _req.AgentStatus
// 		fee.AgentChannelCode = _req.AgentChannelCode
// 		fee.Amount = order.Fee
// 		fee.OperateType = domain.BillTypeOptionFee
// 		assets = append(assets, &fee)
// 		bills = append(bills, *repository.NewBillOptionSync(_req.UID, _req.Base, _req.Quote, domain.BillTypeOptionFee, util.AccountType(_req.AccountType), _req.OptionType, order.Fee, order.OptionId, order.OrderId))
// 	}
// 	return assets, bills
// }

// func NewExerciseRecord(_req usecase.ExerciseBase, op repository.Option, optionType int, amount decimal.Decimal, order usecase.Exercise) *repository.MqCmsAsset {
// 	premium := &repository.MqCmsAsset{
// 		ContractCode:     util.ContractCode(_req.Base, _req.Quote),
// 		Currency:         strings.ToUpper(_req.Quote),
// 		OrderId:          op.OrderId,
// 		UID:              _req.UID,
// 		UserType:         order.UserType,
// 		Platform:         order.Platform,
// 		DealAmount:       order.Amount,
// 		DealPrice:        order.Price,
// 		Amount:           amount,
// 		OptionId:         _req.OptionId,
// 		Premium:          op.Premium,
// 		ChannelCode:      order.ChannelCode,
// 		AgentUserId:      order.AgentUserId,
// 		AgentStatus:      order.AgentStatus,
// 		AgentChannelCode: order.AgentChannelCode,
// 		Period:           order.Period,
// 		OperateType:      optionType,
// 		OperateTime:      _req.OperateTime,
// 	}
// 	return premium
// }
