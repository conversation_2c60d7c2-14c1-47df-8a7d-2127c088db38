package util

import (
	"fmt"
	"github.com/shopspring/decimal"
	"sort"
	"strconv"
)

type comparableDecimalList []decimal.Decimal

func (c comparableDecimalList) Len() int {
	return len(c)
}

func (c comparableDecimalList) Less(i, j int) bool {
	return c[i].<PERSON><PERSON>han(c[j])
}

func (c comparableDecimalList) Swap(i, j int) {
	c[i], c[j] = c[j], c[i]
}

func RoundCeil(value decimal.Decimal, places int32) (decimal.Decimal, error) {
	truncateValue := value.Truncate(places)
	if value.IsPositive() {
		formatStr := "0.%0" + strconv.Itoa(int(places)) + "d"
		addValue, err := decimal.NewFromString(fmt.Sprintf(formatStr, 1))
		if err != nil {
			return decimal.Zero, err
		}

		if value.Sub(truncateValue).IsPositive() {
			truncateValue = truncateValue.Add(addValue)
		}
	}

	return truncateValue, nil
}

func SortDecimals(isReverse bool, a ...decimal.Decimal) []decimal.Decimal {
	var temp comparableDecimalList = a
	if isReverse {
		sort.Sort(sort.Reverse(temp))
	} else {
		sort.Sort(temp)
	}
	return temp
}
