package util

import "sync"

var TableNames map[string]struct{}
var tableNameMutex sync.Mutex

func init() {
	TableNames = make(map[string]struct{}, 0)
}
func TableIsExit(tableName string) (ok bool) {
	tableNameMutex.Lock()
	defer tableNameMutex.Unlock()
	_, ok = TableNames[tableName]
	return
}

func SetNewTableName(_tableName string) {
	tableNameMutex.Lock()
	defer tableNameMutex.Unlock()
	TableNames[_tableName] = struct{}{}
}
