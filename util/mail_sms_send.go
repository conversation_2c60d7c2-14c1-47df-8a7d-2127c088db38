package util

// import (
// 	"encoding/json"
// 	"fmt"
// 	"log"
// 	"strings"
// 	"time"

// 	"futures-asset/internal/domain"
// 	"futures-asset/pkg/mqlib"
// 	"futures-asset/pkg/user"

// 	"github.com/shopspring/decimal"
// 	"github.com/sirupsen/logrus"
// )

// type (
// 	UserCommon struct {
// 		UID               string `json:"uid"`               // 用户ID
// 		Platform          string `json:"platform"`          // 平台 (WEB, APP, H5)
// 		SensitiveReceiver string `json:"sensitiveReceiver"` // 脱敏的收件人名称
// 		// 钱包充提币相关
// 		Currency     string          `json:"currency"`     // 币种
// 		TotalAmount  string          `json:"totalAmount"`  // 数量
// 		StrategyName string          `json:"strategyName"` // 策略名称
// 		ReceiveTime  string          `json:"receiveTime"`  // 体验金领取时间
// 		InvalidTime  string          `json:"invalidTime"`  // 体验金失效时间
// 		MarkPrice    decimal.Decimal `json:"markPrice"`    // 标记价格
// 	}

// 	// 请求公有参数
// 	MailCommon struct {
// 		MailType          string `json:"mailType"`          // 邮件类型
// 		MailAddr          string `json:"mailAddr"`          // 邮箱
// 		SensitiveMailAddr string `json:"sensitiveMailAddr"` // 脱敏的邮箱
// 	}

// 	// 请求公有参数
// 	SmsCommon struct {
// 		SmsType        string `json:"smsType"`        // 短信类型
// 		Phone          string `json:"phone"`          // 手机号
// 		PhonePrefix    string `json:"phonePrefix"`    // 手机号前缀 (中国: 86)
// 		SensitivePhone string `json:"sensitivePhone"` // 脱敏的手机号
// 	}
// )

// type (
// 	ReqMailParam struct {
// 		UserCommon        // 请求公有参数
// 		MailCommon        // 请求公有参数
// 		CoinPair   string `json:"coinPair"` // 币对
// 	}
// 	ReqSmsParam struct {
// 		UserCommon
// 		SmsCommon
// 		CoinPair string `json:"coinPair"` // 币对
// 	}
// )

// // MailSmsTrialWarnRecycle 体验金预警回收短信通知
// func MailSmsTrialWarnRecycle(_userId, _receiveTime, _invalidTime, _codeType string) {
// 	userBasic := user.Service.GetUserBasic(_userId)
// 	if userBasic.UID != _userId || (userBasic.Phone == "" && userBasic.MailAddr == "") {
// 		logrus.Error(fmt.Sprintf("user basic data err.userBasic:%+v,_userId:%s", userBasic, _userId))
// 		return
// 	}

// 	userCommon := UserCommon{
// 		UID:         _userId,
// 		ReceiveTime: _receiveTime,
// 		InvalidTime: _invalidTime,
// 	}
// 	if !strings.EqualFold(userBasic.MailAddr, "") {
// 		mailData := ReqMailParam{
// 			UserCommon: userCommon,
// 			MailCommon: MailCommon{
// 				MailAddr: userBasic.MailAddr,
// 				MailType: _codeType,
// 			},
// 		}
// 		bytes, _ := json.Marshal(mailData)
// 		err := mqlib.CommonAmqp.Send(domain.NotifyExchange, domain.BindKeyMailGeneral, bytes)
// 		if err != nil {
// 			log.Printf("sent mail err:%v data:%v\n", err, string(bytes))
// 		}
// 	}

// 	if !strings.EqualFold(userBasic.Phone, "") {
// 		smsData := ReqSmsParam{
// 			UserCommon: userCommon,
// 			SmsCommon: SmsCommon{
// 				PhonePrefix: userBasic.PhonePrefix,
// 				Phone:       userBasic.Phone,
// 				SmsType:     _codeType,
// 			},
// 		}
// 		bytes, _ := json.Marshal(smsData)
// 		err := mqlib.CommonAmqp.Send(domain.NotifyExchange, domain.BindKeySmsGeneral, bytes)
// 		if err != nil {
// 			log.Printf("sent sms err:%v data:%v\n", err, bytes)
// 		}
// 	}
// }

// /**
//  * @Description: 合约爆仓预警与爆仓发送邮件及短信通知
//  * @param uid 用户ID
//  * @param coinPair 币种类型
//  * @param codeType 邮件短信编码
//  */
// func SendMailAndSmsToNoticeBurst(_userId, _coinPair, _codeType, _riskRate string, _markPrice decimal.Decimal) {
// 	redis := redislib.Redis()
// 	smsLockKey := domain.GetBurstSMSLockRedisKey(_userId, _coinPair, _codeType)
// 	if redis.Exist(smsLockKey) {
// 		return
// 	}
// 	redis.SetString(smsLockKey, fmt.Sprintf("%d", time.Now().Unix()))
// 	redis.SetExpire(smsLockKey, "30m")
// 	// return
// 	userBasic := user.Service.GetUserBasic(_userId)
// 	if userBasic.UID != _userId || (userBasic.Phone == "" && userBasic.MailAddr == "") {
// 		logrus.Error(fmt.Sprintf("user basic data err.userBasic:%+v,_userId:%s", userBasic, _userId))
// 		return
// 	}

// 	mailData := ReqMailParam{
// 		UserCommon: UserCommon{
// 			UID: _userId,
// 		},
// 		MailCommon: MailCommon{
// 			MailAddr: userBasic.MailAddr,
// 			MailType: _codeType,
// 		},
// 		CoinPair: _coinPair,
// 	}
// 	smsData := ReqSmsParam{
// 		UserCommon: UserCommon{
// 			UID: _userId,
// 		},
// 		SmsCommon: SmsCommon{
// 			PhonePrefix: userBasic.PhonePrefix,
// 			Phone:       userBasic.Phone,
// 			SmsType:     _codeType,
// 		},
// 		CoinPair: _coinPair,
// 	}
// 	// 爆仓短信增加标记价格
// 	if _codeType == domain.MSBurst {
// 		mailData.MarkPrice = _markPrice
// 		smsData.MarkPrice = _markPrice
// 	}

// 	if !strings.EqualFold(userBasic.MailAddr, "") {
// 		bytes, _ := json.Marshal(mailData)
// 		err := mqlib.CommonAmqp.Send(domain.NotifyExchange, domain.BindKeyMailGeneral, bytes)
// 		if err != nil {
// 			log.Printf("sent mail err:%v data:%v\n", err, bytes)
// 		}
// 	}

// 	if !strings.EqualFold(userBasic.Phone, "") {
// 		bytes, _ := json.Marshal(smsData)
// 		err := mqlib.CommonAmqp.Send(domain.NotifyExchange, domain.BindKeySmsGeneral, bytes)
// 		if err != nil {
// 			log.Printf("sent sms err:%v data:%v\n", err, bytes)
// 		}
// 	}
// }
