package util

import (
	"time"
)

const layoutDay = "20060102"
const layoutMonth = "200601"
const layoutYear = "2006"
const layoutSecond = "2006-01-02 15:04:05" // 邮件用的格式到秒，其他地方可能也用

const (
	EnumSecond      = iota + 1 // 秒
	EnumMillisecond            // 毫秒
	EnumMicrosecond            // 微秒
	EnumNanosecond             // 纳秒
)

func DtLayout(nsec int64, tu uint) string {
	switch tu {
	case EnumSecond:
		nsec = nsec * 1e9
	case EnumMillisecond:
		nsec = nsec * 1e6
	case EnumMicrosecond:
		nsec = nsec * 1e3
	}
	t := time.Unix(0, nsec)
	return t.Format(layoutDay)
}

func SecondLayout(nsec int64, tu uint) string {
	return layout(nsec, tu).Format(layoutSecond)
}

func DayLayout(nsec int64, tu uint) string {
	return layout(nsec, tu).Format(layoutDay)
}

func MonthLayout(nsec int64, tu uint) string {
	return layout(nsec, tu).Format(layoutMonth)
}

func YearLayout(nsec int64, tu uint) string {
	return layout(nsec, tu).Format(layoutYear)
}

func layout(nsec int64, tu uint) time.Time {
	switch tu {
	case EnumSecond:
		nsec = nsec * 1e9
	case EnumMillisecond:
		nsec = nsec * 1e6
	case EnumMicrosecond:
		nsec = nsec * 1e3
	}
	t := time.Unix(0, nsec)
	return t
}

// OneDayBeginAndEndTimeStamp 获取某天的起始时间
func OneDayBeginAndEndTimeStamp(_oneDayTime time.Time) int64 {
	timeBegin := time.Date(_oneDayTime.Year(), _oneDayTime.Month(), _oneDayTime.Day(), 0, 0, 0, 0, _oneDayTime.Location())
	return timeBegin.Unix()
}

// GetSettlementTime 获取下次结算时间
func GetSettlementTime() int64 {
	nowTime := time.Now()
	hour := time.Now().Hour()
	if hour < 8 {
		return time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 8, 0, 0, 0, nowTime.Location()).Unix()
	} else if hour < 16 {
		return time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 16, 0, 0, 0, nowTime.Location()).Unix()
	} else {
		return time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), 24, 0, 0, 0, nowTime.Location()).Unix()
	}
}
