package isolation

// import (
// 	"fmt"
// 	"net/http"
// 	"time"

// 	"futures-asset/cache/swapcache"

// 	"futures-asset/internal/delivery/http/payload"
// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/repository"
// 	"futures-asset/pkg/setting"
// 	"futures-asset/service/persist"
// 	"futures-asset/util/modelutil"

// 	"github.com/pkg/errors"
// 	"github.com/samber/lo"
// 	"github.com/shopspring/decimal"
// 	"github.com/sirupsen/logrus"
// )

// type Trial struct {
// 	req *payload.LockParam
// }

// func (t *Trial) Lock() (reply payload.LockRes, err error) {
// 	// 必须是市价单
// 	if t.req.IsLimitOrder == domain.Limit && t.req.LiquidationType == int(domain.LiquidationTypeNone) {
// 		reply.Code = domain.TrialOrderTypeErr
// 		err = errors.New("limit order is not supported")
// 		return
// 	}
// 	if t.req.PositionMode == domain.HoldModeBoth {
// 		reply.Code = domain.TrialHoldModeErr
// 		err = errors.New("trial is not supported both pos")
// 		return
// 	}

// 	// 上锁
// 	mutex := redislib.NewMutex(domain.MutexSwapPosLock+t.req.UID, 30*time.Second)
// 	if mutex.Lock() != nil {
// 		reply.Code = domain.Code251101
// 		err = domain.ErrLockPos
// 		return
// 	}
// 	defer mutex.Unlock()

// 	// 获取用户资产
// 	userCache := swapcache.NewPosCache(swapcache.CacheParam{
// 		TradeCommon: repository.TradeCommon{Base: t.req.Base, Quote: t.req.Quote},
// 	}, t.req.UID)
// 	asset, err := userCache.Load()
// 	if err != nil {
// 		reply.Code = domain.Code252404
// 		err = errors.New("load asset err")
// 		return
// 	}

// 	// 如果是平仓
// 	if t.req.IsClose() && t.req.PositionMode != domain.HoldModeBoth {
// 		// 锁定仓位
// 		// posSide := util.RealPosSide(t.req.Side, t.req.Offset, t.req.PositionMode)
// 		posSide := t.req.PosSide
// 		pos := asset.GetPos(posSide, true)
// 		insufficientCode := domain.Code251111
// 		if posSide == domain.ShortPos {
// 			insufficientCode = domain.Code251110
// 		}
// 		if pos.PosAvailable.Sign() <= 0 {
// 			reply.Code = insufficientCode
// 			err = errors.New("pos available is zero")
// 			return
// 		}
// 		diffPos := pos.PosAvailable.Sub(t.req.Amount)
// 		if diffPos.IsNegative() {
// 			switch t.req.OrderType {
// 			// 37-部分止损 36-全部止损 32-部分止盈 31-全部止盈
// 			case domain.OrderTypeStopLossPart, domain.OrderTypeStopLossAll, domain.OrderTypeTakeProfitPart, domain.OrderTypeTakeProfitAll:
// 				t.req.Amount = pos.PosAvailable
// 			default:
// 				reply.Code = insufficientCode
// 				err = errors.New("insufficient pos available")

// 			}
// 		}

// 		// 体验金每次只能全部平仓
// 		if !t.req.Amount.Equal(pos.PosAvailable) {
// 			reply.Code = insufficientCode
// 			err = errors.New("pos available is not equal amount")
// 			return
// 		}

// 		// TODO: pos.AwardOpIds != t.req.AwardOpIds

// 		trialList := asset.TrialDetail.Gets(pos.AwardOpIds...)
// 		if len(trialList) == 0 {
// 			reply.Code = domain.TrialOpIdErr
// 			err = errors.New("trial list is empty")
// 			return
// 		}
// 		// 体验金持仓时间不足
// 		holdTime := (time.Now().UnixNano() - pos.OpenTime) / 1e9
// 		if trialList.GetHoldTime() > holdTime {
// 			reply.Code = domain.TrialMinHoldTimeErr
// 			logrus.Error(fmt.Sprintf("trial min hold time is %d, %d", trialList.GetHoldTime(), holdTime))
// 			err = fmt.Errorf("%v", trialList.GetHoldTime()-holdTime)
// 			return
// 		}

// 		pos.PosAvailable = pos.PosAvailable.Sub(t.req.Amount)
// 		asset.SetPos(posSide, pos, true)
// 		if posSide == domain.ShortPos {
// 			err = userCache.UpdateTrialShortPos(asset)
// 		} else if posSide == domain.LongPos {
// 			err = userCache.UpdateTrialLongPos(asset)
// 		} else {
// 			err = userCache.UpdateTrialBothPos(asset)
// 		}
// 		if err != nil {
// 			reply.Code = domain.Code251104
// 			err = errors.New("update trial pos err")
// 			return
// 		}

// 		go func() {
// 			pos := userCache.NewLogPosSync(pos, t.req.OperateTime, "", t.req.OrderId, t.req.Side, t.req.PosSide, t.req.Amount, decimal.Zero)
// 			// wallet资产异步存库
// 			if err := persist.SyncPos(redislib.Redis(), t.req.ContractCode(), pos); err != nil {
// 				logrus.Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
// 			}
// 		}()

// 		reply.Code = http.StatusOK
// 		reply.Amount = t.req.Amount
// 		reply.HaveTrial = 1
// 		return
// 	}

// 	trialList := asset.TrialDetail.Gets(t.req.AwardOpIds...)
// 	if len(trialList) <= 0 {
// 		reply.Code = domain.TrialOpIdErr
// 		err = errors.New("trial list is empty")
// 		return
// 	}
// 	if int(trialList.GetLeverage()) < t.req.Leverage {
// 		reply.Code = domain.TrialMaxLeverageErr
// 		logrus.Error("trial max leverage is %d, %d", trialList.GetLeverage(), t.req.Leverage)
// 		err = fmt.Errorf("%v", trialList.GetLeverage())
// 		return
// 	}

// 	if trialList.GetAvailableAmount().LessThanOrEqual(decimal.Zero) {
// 		reply.Code = domain.TrialInsufficientErr
// 		err = errors.New("trial detail is locked")
// 		return
// 	}

// 	// 判断保证金模式
// 	leverage := asset.GetLeverage(t.req.ContractCode())
// 	if leverage.MarginMode != domain.MarginModeNone && int32(leverage.MarginMode) != t.req.MarginMode {
// 		reply.Code = domain.Code251117
// 		err = errors.New("margin mode is not equal")
// 		return
// 	}

// 	// 判断杠杆
// 	realLeverage := t.req.GetLeverage(leverage)
// 	if realLeverage <= 0 {
// 		logrus.Errorf("user(%d) contractCode(%s) get leverage(%d) error", t.req.UID, t.req.ContractCode(), realLeverage)
// 		reply.Code = domain.Code251114
// 		err = errors.New("get leverage err")
// 		return
// 	}
// 	if realLeverage != t.req.Leverage {
// 		reply.Code = domain.Code251115
// 		err = errors.New("leverage is not equal")
// 		return
// 	}

// 	// 判断最大开仓仓位是不是够用
// 	userLeverage := decimal.NewFromInt(int64(realLeverage))
// 	maxPosValue, err := swapcache.GetMaxPosValueWithLeverage(t.req.Base, t.req.Quote, leverage.BLeverage)
// 	if err != nil {
// 		reply.Code = domain.ErrGetAllCfg
// 		err = errors.New(domain.ErrMsg[domain.ErrGetAllCfg])
// 		return
// 	}
// 	if maxPosValue.IsZero() {
// 		return payload.LockRes{Code: domain.ErrMaxPos}, errors.New(domain.ErrMsg[domain.ErrMaxPos])
// 	}
// 	if maxPosValue.IsPositive() {
// 		frozenValue := asset.GetFrozenByCode(t.req.ContractCode(), true).Mul(userLeverage)
// 		posValue := asset.TrialShortPos.CalcPosValue(pCache).Add(asset.TrialLongPos.CalcPosValue(pCache)).Add(asset.TrialBothPos.CalcPosValue(pCache))
// 		if maxPosValue.LessThan(posValue.Add(t.req.Value().Add(frozenValue))) {
// 			reply.Code = domain.ErrMaxPos
// 			err = errors.New(domain.ErrMsg[domain.ErrMaxPos])
// 			return
// 		}
// 	}

// 	// 获取配置
// 	codeSetting, err := setting.Service.GetCachePair(t.req.Base, t.req.Quote)
// 	if err != nil {
// 		logrus.Errorf("open sell get contract pair base: %s quote: %s setting err: %s", t.req.Base, t.req.Quote, err)
// 		reply.Code = domain.Code250002
// 		err = errors.New("get contract pair setting err")
// 		return
// 	}

// 	avaibleAmount := trialList.GetAvailableAmount() // 直接冻结能锁定的数量
// 	if t.req.Price.IsPositive() {
// 		t.req.Amount = avaibleAmount.Mul(userLeverage).Div(t.req.Price).Truncate(codeSetting.AmountPrecision)
// 	}

// 	if t.req.Amount.IsZero() {
// 		reply.Code = domain.Code251105
// 		err = errors.New("amount is zero")
// 		return
// 	}

// 	// 开仓时候，不能有对向的仓位
// 	// posSide := util.RealPosSide(t.req.Side, t.req.Offset, t.req.PositionMode)
// 	if (t.req.PosSide == domain.LongPos && asset.TrialShortPos.Pos.IsPositive()) || (t.req.PosSide == domain.ShortPos && asset.TrialLongPos.Pos.IsPositive()) {
// 		reply.Code = domain.TrialHaveLongShortPositionErr
// 		err = errors.New("have long short position")
// 		return
// 	}

// 	if avaibleAmount.IsPositive() {
// 		trialList.Lock()
// 		// asset.IncrFrozen(util.PosSide(t.req.Side, t.req.Offset, int32(asset.PositionMode)), t.req.ContractCode(), avaibleAmount, true)
// 		asset.IncrFrozen(t.req.PosSide, t.req.ContractCode(), avaibleAmount, true)
// 	}
// 	err = repo.trialRepo.UpdateTrialAsset(ctx, asset)
// 	if err != nil {
// 		reply.Code = domain.Code252405
// 		err = errors.New("lock or unlock err")
// 		return
// 	}

// 	assetSync := modelutil.NewLogAssetSync(asset, t.req.Quote, t.req.OperateTime)
// 	go func() {
// 		redis := redislib.Redis()
// 		AddAssetLogs(redis, assetSync) // wallet资产异步存库
// 		if err := redis.LPush(domain.SyncTrialAssetSwap, lo.ToAnySlice(trialList)...); err != nil {
// 			logrus.Error(fmt.Sprintf("sql lpush trial asset add err:%v,swapTrialAsset:%+v", err, trialList))
// 		}
// 	}()

// 	reply.Code = http.StatusOK
// 	reply.PositionMode = asset.PositionMode
// 	reply.Amount = t.req.Amount
// 	reply.FrozenMargin = avaibleAmount
// 	reply.HaveTrial = 1
// 	return
// }

// // 保证金解冻
// // 考虑渣渣问题，是不是要添加到持仓里面去
// // 目前解冻不是走这个逻辑，走的批量里面
// func (t *Trial) UnLock() (code domain.Code, err error) {
// 	mutex := redislib.NewMutex(domain.MutexSwapPosLock+t.req.UID, 15*time.Second)
// 	if mutex.Lock() != nil {
// 		return domain.Code251101, nil
// 	}
// 	defer mutex.Unlock()

// 	userCache := swapcache.NewPosCache(swapcache.CacheParam{
// 		TradeCommon: repository.TradeCommon{
// 			Base:  t.req.Base,
// 			Quote: t.req.Quote,
// 		},
// 	}, t.req.UID)
// 	asset, err := userCache.Load()
// 	if err != nil {
// 		return domain.Code252404, nil
// 	}
// 	trialList := asset.TrialDetail.Gets(t.req.AwardOpIds...)
// 	if len(trialList) <= 0 {
// 		code = domain.TrialOpIdErr
// 		err = errors.New("trial detail is empty")
// 		return
// 	}
// 	lockAmount := trialList.GetLockAmount()
// 	if lockAmount.LessThan(t.req.UnfrozenMargin) {
// 		code = domain.TrialInsufficientErr
// 		err = errors.New("unfrozen margin is not enough")
// 		return
// 	}

// 	trialList.UnlockAmount(t.req.UnfrozenMargin)
// 	// asset.DecrFrozen(util.PosSide(t.req.Side, t.req.Offset, int32(asset.PositionMode)), t.req.ContractCode(), t.req.UnfrozenMargin, true)
// 	asset.DecrFrozen(t.req.PosSide, t.req.ContractCode(), t.req.UnfrozenMargin, true)
// 	err = repo.trialRepo.UpdateTrialAsset(ctx, asset)
// 	if err != nil {
// 		return domain.Code252405, nil
// 	}

// 	assetSync := modelutil.NewLogAssetSync(asset, t.req.Quote, t.req.OperateTime)
// 	go func() {
// 		redis := redislib.Redis()
// 		AddAssetLogs(redis, assetSync) // wallet资产异步存库
// 		if err := redis.LPush(domain.SyncTrialAssetSwap, lo.ToAnySlice(trialList)...); err != nil {
// 			logrus.Error(fmt.Sprintf("sql lpush trial asset add err:%v,swapTrialAsset:%+v", err, trialList))
// 		}
// 	}()

// 	return http.StatusOK, nil
// }
