package isolation

// import (
// 	"futures-asset/cache/swapcache"
// 	"futures-asset/internal/delivery/http/payload"
// 	"futures-asset/internal/domain"
// 	"futures-asset/util"
// )

// type ILock interface {
// 	Lock() (payload.LockRes, error)
// }

// type IUnLock interface {
// 	UnLock() (domain.Code, error)
// }

// type IIsolation interface {
// 	ILock
// 	IUnLock
// }

// func GetOperater(req *payload.TradeParam) (IIsolation, error) {
// 	// 获取用户资产信息
// 	base, quote := util.BaseQuote(req.ContractCode)
// 	_, assetInfo, err := swapcache.GetUserCacheData(base, quote, req.UID)
// 	if err != nil {
// 		return nil, err
// 	}

// 	req.PositionMode = int32(assetInfo.PositionMode)
// 	// 体验金仓位
// 	if len(req.AwardOpIds) > 0 {
// 		return &Trial{req: req}, nil
// 	}

// 	if req.PositionMode == domain.HoldModeBoth {
// 		// 单向持仓模式
// 		return &Both{req: req}, nil
// 	}

// 	// 看涨 (买盘)
// 	// 开多
// 	if req.Side == domain.Buy && req.PosSide == domain.Long {
// 		return &OpenLong{req: req}, nil
// 	}
// 	// 看跌 (卖盘)
// 	// 开空
// 	if req.Side == domain.Sell && req.PosSide == domain.Short {
// 		return &OpenShort{req: req}, nil
// 	}

// 	// 平空
// 	if req.Side == domain.Buy && req.PosSide == domain.Short {
// 		return &CloseShort{req: req}, nil
// 	}
// 	// 平多
// 	if req.Side == domain.Sell && req.PosSide == domain.Long {
// 		return &CloseLong{req: req}, nil
// 	}

// 	return nil, nil
// }
