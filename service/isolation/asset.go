package isolation

import (
	// "fmt"
	// "log"
	"net/http"
	// "strings"
	// "time"

	// "futures-asset/cache/swapcache"

	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/domain"
	// "futures-asset/internal/message"
	// "futures-asset/service/coupling"
	// "futures-asset/util/modelutil"
	// "github.com/pkg/errors"
	// "github.com/shopspring/decimal"
	// "github.com/sirupsen/logrus"
)

type AssetService struct{}

func NewAsset() *AssetService {
	return &AssetService{}
}

func (slf *AssetService) Increase(req *payload.IncrParam) (domain.Code, payload.AssetReply, error) {
	return http.StatusOK, payload.AssetReply{}, nil
	// reply := payload.AssetReply{
	// 	OrderId: req.OrderId,
	// }

	// if len(req.UID) == 0 || len(req.Currency) == 0 || len(req.OrderId) == 0 || req.Amount.Sign() <= 0 {
	// 	logrus.Errorf("param err: %+v", *req)
	// 	return domain.CodeParamInvalid, reply, fmt.Errorf("param err: %+v", *req)
	// }

	// // req user
	// mutex := redislib.NewMutex(domain.MutexSwapPosLock+req.UID, 30*time.Second)
	// if mutex.Lock() != nil {
	// 	return domain.Code251101, reply, domain.ErrLockPos
	// }
	// defer mutex.Unlock()

	// userCache := swapcache.NewPosCache(swapcache.CacheParam{}, req.UID)
	// cachedAsset, err := userCache.Load()
	// if err != nil {
	// 	return domain.Code252404, reply, err
	// }

	// cachedAsset.AddBalance(req.Currency, req.Amount)
	// err = userCache.IncrOrDecr(cachedAsset)
	// if err != nil {
	// 	return domain.Code252405, reply, err
	// }

	// assetMsg := message.AssetMsg{
	// 	Currency: req.Currency,
	// 	Balance:  cachedAsset.CBalance(req.Currency),
	// 	Frozen:   cachedAsset.Frozen,
	// }
	// go message.New(req.UID, message.AssetQueueIndex, mqlib.CommonAmqp).TopicAsset(message.SwapAccount).Push(message.AccountMsg{
	// 	MsgType: message.AccountMsgTypeAsset,
	// 	MsgData: assetMsg,
	// })

	// incrBill := modelutil.NewBillSwapWithAsset(req, "", domain.BillTypeInnerIn)
	// go func() {
	// 	coupling.AddBills(nil, *incrBill)
	// }()
	// return http.StatusOK, reply, nil
}

func (slf *AssetService) Decrease(req *payload.IncrParam) (domain.Code, payload.AssetReply, error) {
	return http.StatusOK, payload.AssetReply{}, nil
	// reply := payload.AssetReply{
	// 	OrderId: req.OrderId,
	// }

	// if len(req.UID) == 0 || len(req.Currency) == 0 || len(req.OrderId) == 0 || req.Amount.Sign() <= 0 {
	// 	logrus.Errorf("param err: %+v", *req)
	// 	return domain.CodeParamInvalid, reply, fmt.Errorf("param err: %+v", *req)
	// }

	// // req user
	// mutex := redislib.NewMutex(domain.MutexSwapPosLock+req.UID, 30*time.Second)
	// if mutex.Lock() != nil {
	// 	return domain.Code251101, reply, domain.ErrLockPos
	// }
	// defer mutex.Unlock()

	// userCache := swapcache.NewPosCache(swapcache.CacheParam{}, req.UID)
	// asset, err := userCache.Load()
	// if err != nil {
	// 	return domain.Code252404, reply, err
	// }

	// pCache := price.New()
	// maxOut, err := userCache.CanTransfer(pCache, asset, req.Currency)
	// if err != nil {
	// 	log.Println("Decrease CanTransfer error:", err)
	// 	return domain.Code252407, reply, err
	// }
	// if maxOut.LessThan(req.Amount) {
	// 	return domain.Code252002, reply, errors.New("insufficient funds available")
	// }
	// asset.AddBalance(req.Currency, req.Amount.Neg())
	// if asset.CBalance(req.Currency).Sign() < 0 {
	// 	return domain.Code252003, reply, errors.New("account balance is negative")
	// }

	// err = userCache.IncrOrDecr(asset)
	// if err != nil {
	// 	return domain.Code252405, reply, err
	// }

	// assetMsg := message.AssetMsg{
	// 	Currency: req.Currency,
	// 	Balance:  asset.CBalance(req.Currency),
	// 	Frozen:   asset.Frozen,
	// }
	// go message.New(req.UID, message.AssetQueueIndex, mqlib.CommonAmqp).TopicAsset(message.SwapAccount).Push(message.AccountMsg{
	// 	MsgType: message.AccountMsgTypeAsset,
	// 	MsgData: assetMsg,
	// })

	// // 转出 账单里面需要记成负的
	// decrBill := modelutil.NewBillSwapWithAsset(req, "", domain.BillTypeInnerOut)
	// go func() {
	// 	coupling.AddBills(nil, *decrBill)
	// }()

	// return http.StatusOK, reply, nil
}

func (slf *AssetService) MaxWithdraw(req *payload.ReqAsset) (domain.Code, payload.ContractTransAmount, error) {
	return http.StatusOK, payload.ContractTransAmount{}, nil
	// data := payload.ContractTransAmount{
	// 	Currency: strings.ToUpper(req.Currency),
	// }
	// if len(req.UID) == 0 {
	// 	logrus.Errorf("param err: %+v", *req)
	// 	return domain.CodeParamInvalid, data, fmt.Errorf("param err: %+v", *req)
	// }

	// userCache := swapcache.NewPosCache(swapcache.CacheParam{}, req.UID)
	// asset, err := userCache.Load()
	// if err != nil {
	// 	return domain.Code252404, data, err
	// }

	// pCache := price.New()
	// data.TransAmount, err = userCache.CanTransfer(pCache, asset, req.Currency)
	// if err != nil {
	// 	log.Println("MaxWithdraw CanTransfer error:", err)
	// 	return domain.Code252407, data, err
	// }
	// if data.TransAmount.Sign() < 0 {
	// 	data.TransAmount = decimal.Zero
	// }

	// return http.StatusOK, data, nil
}

func (slf *AssetService) AccountMaxWithdraw(req *payload.ReqAsset) (domain.Code, []payload.ContractTransAmount, error) {
	return http.StatusOK, nil, nil
	// var data []payload.ContractTransAmount
	// if len(req.UID) == 0 {
	// 	logrus.Errorf("param err: %+v", *req)
	// 	return domain.CodeParamInvalid, data, fmt.Errorf("param err: %+v", *req)
	// }

	// userCache := swapcache.NewPosCache(swapcache.CacheParam{}, req.UID)
	// asset, err := userCache.Load()
	// if err != nil {
	// 	return domain.Code252404, data, err
	// }

	// pCache := price.New()
	// for _, currency := range domain.CurrencyList {
	// 	temp := payload.ContractTransAmount{
	// 		Currency: strings.ToUpper(currency),
	// 	}
	// 	temp.TransAmount, err = userCache.CanTransfer(pCache, asset, currency)
	// 	if err != nil {
	// 		log.Println("MaxWithdraw CanTransfer error:", err)
	// 		return domain.Code252407, data, err
	// 	}
	// 	if temp.TransAmount.Sign() < 0 {
	// 		temp.TransAmount = decimal.Zero
	// 	}
	// 	data = append(data, temp)
	// }

	// return http.StatusOK, data, nil
}

func (slf *AssetService) InnerTransfer(req payload.InnerTransfer) (domain.Code, error) {
	return http.StatusOK, nil

	// // lock from user & to user
	// fromMutex := redislib.NewMutex(domain.MutexSwapPosLock+req.FromUserId, 30*time.Second)
	// if fromMutex.Lock() != nil {
	// 	return domain.Code251101, domain.ErrLockPos
	// }
	// defer fromMutex.Unlock()
	// toMutex := redislib.NewMutex(domain.MutexSwapPosLock+req.ToUserId, 30*time.Second)
	// if toMutex.Lock() != nil {
	// 	return domain.Code251101, domain.ErrLockPos
	// }
	// defer toMutex.Unlock()

	// fromCache := swapcache.NewPosCache(swapcache.CacheParam{}, req.FromUserId)
	// toCache := swapcache.NewPosCache(swapcache.CacheParam{}, req.ToUserId)
	// // 获取资产
	// fromAsset, err := fromCache.Load()
	// if err != nil {
	// 	return domain.Code252404, err
	// }
	// toAsset, err := toCache.Load()
	// if err != nil {
	// 	return domain.Code252404, err
	// }

	// // 转出金额判断
	// maxOut, err := fromCache.CanTransfer(fromAsset, req.Currency)
	// if err != nil {
	// 	log.Println("InnerTransfer CanTransfer error:", err)
	// 	return domain.Code252407, err
	// }
	// if maxOut.LessThan(req.Amount) {
	// 	return domain.Code252002, errors.New("inner transfer from account insufficient funds available")
	// }
	// fromAsset.AddBalance(req.Currency, req.Amount.Neg())
	// if fromAsset.CBalance(req.Currency).Sign() < 0 {
	// 	return domain.Code252003, errors.New("inner transfer from account balance is negative")
	// }

	// err = fromCache.IncrOrDecr(fromAsset)
	// if err != nil {
	// 	return domain.Code252405, errors.Wrap(err, "inner transfer from user: "+req.FromUserId)
	// }
	// toAsset.AddBalance(req.Currency, req.Amount)
	// err = toCache.IncrOrDecr(toAsset)
	// if err != nil {
	// 	fromAsset.AddBalance(req.Currency, req.Amount)
	// 	_ = fromCache.IncrOrDecr(fromAsset)
	// 	return domain.Code252405, errors.Wrap(err, "inner transfer to user: "+req.ToUserId)
	// }

	// go func() {
	// 	redis := redislib.Redis()
	// 	fromParam := &payload.IncrParam{
	// 		UID:         req.FromUserId,
	// 		Currency:    req.Currency,
	// 		FromAccount: domain.AccountSwapInt,
	// 		ToAccount:   domain.AccountSwapInt,
	// 		Amount:      req.Amount,
	// 	}
	// 	toParam := &payload.IncrParam{
	// 		UID:         req.ToUserId,
	// 		Currency:    req.Currency,
	// 		FromAccount: domain.AccountSwapInt,
	// 		ToAccount:   domain.AccountSwapInt,
	// 		Amount:      req.Amount,
	// 	}
	// 	fromBill := modelutil.NewBillSwapWithAsset(fromParam, "", domain.BillTypeInnerOut)
	// 	toBill := modelutil.NewBillSwapWithAsset(toParam, "", domain.BillTypeInnerIn)
	// 	coupling.AddBills(redis, *fromBill, *toBill) // 前台合约划转账单记账
	// }()

	// return http.StatusOK, nil
}
