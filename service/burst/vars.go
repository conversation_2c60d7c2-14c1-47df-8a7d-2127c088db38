package burst

import (
	"context"
	"sync"

	"futures-asset/internal/domain/repository"
)

var (
	RunningServices struct {
		sync.RWMutex
		Map map[string]*workingBurstService
	}

	snapCronContracts struct {
		sync.RWMutex
		List []string
	}
	snapCronContext struct {
		sync.RWMutex
		Map map[string][]context.CancelFunc
	}

	snapScannerContracts struct {
		sync.RWMutex
		List []string
	}
	snapScannerContext struct {
		sync.RWMutex
		Map map[string][]context.CancelFunc
	}

	snapProcessorContracts struct {
		sync.RWMutex
		List []string
	}
	snapProcessorContext struct {
		sync.RWMutex
		Map map[string][]context.CancelFunc
	}

	cacheBurstLevels struct {
		sync.RWMutex
		Map map[string]repository.LevelHoldSortList
	}
	ContractSettings struct {
		sync.RWMutex
		Map map[string]repository.ContractPair
	}
	PlatformUserLevelRate struct {
		sync.RWMutex
		Map map[string]repository.LevelRate
	}
	DefaultUserLevelRate struct {
		sync.RWMutex
		Map map[int]repository.UserLevelInfo
	}
)
