package burst

import (
	"sync"
	"time"

	"futures-asset/internal/domain/repository"

	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
)

type (
	service struct{}

	swapMirrorTicker struct {
		*sync.RWMutex
		LastPrice decimal.Decimal
	}

	workingBurstService struct {
		sync.RWMutex
		coinPairInfo     repository.ContractPair
		closeFlag        bool
		base             string
		quote            string
		contractCode     string
		currency         string
		lastTicker       swapMirrorTicker
		lastTickTime     time.Time
		ticker           chan interface{}
		userGroup        []chan []string
		working          bool
		tickerSubCli     *redis.PubSub
		tickerSubStop    bool
		markPriceSubCli  *redis.PubSub
		markPriceSubStop bool
		burstStop        bool
	}

	SideNumber interface {
		int | int32 | int64
	}
)
