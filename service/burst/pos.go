package burst

// import (
// 	"fmt"
// 	"strings"

// 	"futures-asset/cache/price"
// 	"futures-asset/cache/sharedcache"
// 	"futures-asset/cache/swapcache"
// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/repository"
// 	"futures-asset/pkg/setting"
// 	"futures-asset/pkg/user"
// 	"futures-asset/util"

// 	"github.com/shopspring/decimal"
// 	"github.com/sirupsen/logrus"
// )

// type burstPosInfo struct {
// 	BurstId              string                 `json:"burst_id"`
// 	PosId                string                 `json:"pos_id"`
// 	PosSide              int32                  `json:"pos_side"` // 仓位方向 (0:单向持仓仓位信息 1:多仓 2:空仓)
// 	UID                  string                 `json:"user_id"`
// 	UserType             int                    `json:"user_type"`
// 	AccountType          string                 `json:"account_type"`
// 	Base                 string                 `json:"base"`
// 	Quote                string                 `json:"quote"`
// 	Currency             string                 `json:"currency"`
// 	MarginBalance        decimal.Decimal        `json:"margin_balance"`
// 	TotalMargin          decimal.Decimal        `json:"total_margin"`
// 	CoinPairSetting      setting.ContractPair   `json:"coin_pair_setting"`
// 	MarginMode           domain.MarginMode      `json:"marginMode"`
// 	BurstPrice           decimal.Decimal        `json:"burst_price"`
// 	MarkPrice            decimal.Decimal        `json:"mark_price"`
// 	CollapsePrice        decimal.Decimal        `json:"collapse_price"`
// 	CollapsePriceFormula string                 `json:"collapse_price_formula"`
// 	LongPosPrice         decimal.Decimal        `json:"long_pos_price"`
// 	ShortPosPrice        decimal.Decimal        `json:"short_pos_price"`
// 	LongPosAmount        decimal.Decimal        `json:"long_pos_amount"`
// 	ShortPosAmount       decimal.Decimal        `json:"short_pos_amount"`
// 	LongPosValue         decimal.Decimal        `json:"long_pos_value"`
// 	ShortPosValue        decimal.Decimal        `json:"short_pos_value"`
// 	LongPosUnReal        decimal.Decimal        `json:"long_pos_un_real"`
// 	ShortPosUnReal       decimal.Decimal        `json:"short_pos_un_real"`
// 	FrozenMargin         decimal.Decimal        `json:"frozen_margin"`
// 	PosMargin            decimal.Decimal        `json:"pos_margin"`
// 	PNL                  decimal.Decimal        `json:"pnl"`
// 	FrozenPos            decimal.Decimal        `json:"frozen_pos"`
// 	Leverage             int                    `json:"leverage"`
// 	LiquidationPrice     decimal.Decimal        `json:"liquidation_price"`
// 	LiquidationType      domain.LiquidationType `json:"liquidation_type"`
// 	OpenTime             int64                  `json:"open_time"`
// 	BurstLevel           int                    `json:"burst_level"`
// 	BurstTime            int64                  `json:"burst_time"`
// 	IsBurstToZero        bool                   `json:"is_burst_to_zero"`
// 	UserLevelRate        user.LevelRate         `json:"user_level_rate"`
// 	IsTrialPos           bool                   `json:"is_trial_pos"` // 是否体验金仓位
// }

// func (bpi *burstPosInfo) ContractCode() string {
// 	return fmt.Sprintf("%s-%s", bpi.Base, bpi.Quote)
// }

// func (bpi *burstPosInfo) _updatePos(_burstId string, _assetInfo *repository.AssetSwap, _userCache *swapcache.PosCache,
// 	_settingInfo setting.ContractPair, _posInfo repository.PosSwap, _crossPosList []repository.PosSwap, _filter repository.LevelFilter,
// 	_burstTime int64, pCache *price.PCache, userLevelRateInfo user.LevelRate,
// ) error {
// 	var err error = nil

// 	bpi.BurstId = _burstId
// 	bpi.UID = _userCache.UID
// 	bpi.UserType = int(_posInfo.UserType)
// 	bpi.AccountType = _posInfo.AccountType
// 	bpi.PosId = _posInfo.PosId
// 	bpi.Currency = _posInfo.Currency
// 	bpi.Base, bpi.Quote = util.BaseQuote(_posInfo.ContractCode)
// 	bpi.MarginMode = domain.MarginMode(_posInfo.MarginMode)
// 	bpi.Leverage = _posInfo.Leverage
// 	bpi.PosSide = _posInfo.PosSide
// 	bpi.CoinPairSetting = _settingInfo
// 	bpi.IsTrialPos = _posInfo.IsTrial()

// 	bpi.LongPosAmount = decimal.Zero
// 	bpi.ShortPosAmount = decimal.Zero
// 	bpi.LongPosValue = decimal.Zero
// 	bpi.ShortPosValue = decimal.Zero
// 	bpi.FrozenPos = decimal.Zero
// 	bpi.FrozenMargin = decimal.Zero
// 	bpi.CollapsePrice = decimal.Zero

// 	switch bpi.MarginMode {
// 	case domain.MarginModeCross:
// 		if len(_crossPosList) < 1 {
// 			_crossPosList = []repository.PosSwap{_posInfo}
// 		}
// 		for _, pos := range _crossPosList {
// 			if pos.ContractCode == bpi.ContractCode() {
// 				if bpi.FrozenMargin.IsZero() {
// 					bpi.FrozenMargin = _assetInfo.GetFrozenByCode(pos.ContractCode)
// 				}

// 				switch pos.PosSide {
// 				case domain.LongPos:
// 					bpi.LongPosAmount = bpi.LongPosAmount.Add(pos.Pos)
// 					bpi.LongPosValue = bpi.LongPosValue.Add(pos.CalcPosValue(pCache))

// 				case domain.ShortPos:
// 					bpi.ShortPosAmount = bpi.ShortPosAmount.Add(pos.Pos)
// 					bpi.ShortPosValue = bpi.ShortPosValue.Add(pos.CalcPosValue(pCache))

// 				case domain.BothPos:
// 					if pos.Pos.GreaterThan(decimal.Zero) {
// 						bpi.LongPosAmount = bpi.LongPosAmount.Add(pos.Pos.Abs())
// 						bpi.LongPosValue = bpi.LongPosValue.Add(pos.CalcPosValue(pCache))
// 					} else if pos.Pos.LessThan(decimal.Zero) {
// 						bpi.ShortPosAmount = bpi.ShortPosAmount.Add(pos.Pos.Abs())
// 						bpi.ShortPosValue = bpi.ShortPosValue.Add(pos.CalcPosValue(pCache))
// 					}

// 				default:

// 				}

// 				bpi.FrozenPos = bpi.FrozenPos.Add(pos.Pos.Abs().Sub(pos.PosAvailable.Abs()))
// 				// bpi.MarginBalance = _assetInfo.Balance.Sub(_userCache.TotalIsolatedMargin())

// 				if pos.PosId == bpi.PosId {
// 					bpi.MarkPrice = pCache.GetMarkPrice(pos.ContractCode)
// 					bpi.BurstPrice = pCache.GetMarkPrice(pos.ContractCode)

// 					switch pos.PosSide {
// 					case domain.LongPos:
// 						bpi.LongPosPrice = pos.OpenPriceAvg
// 						bpi.LongPosUnReal = pos.ProfitUnreal

// 					case domain.ShortPos:
// 						bpi.ShortPosPrice = pos.OpenPriceAvg
// 						bpi.ShortPosUnReal = pos.ProfitUnreal

// 					case domain.BothPos:
// 						if pos.Pos.GreaterThan(decimal.Zero) {
// 							bpi.LongPosPrice = pos.OpenPriceAvg
// 							bpi.LongPosUnReal = pos.ProfitUnreal
// 						} else if pos.Pos.LessThan(decimal.Zero) {
// 							bpi.ShortPosPrice = pos.OpenPriceAvg
// 							bpi.ShortPosUnReal = pos.ProfitUnreal
// 						}

// 					default:

// 					}

// 					bpi.BurstTime = _burstTime
// 					bpi.BurstLevel = _filter.Level
// 					bpi.MarginBalance, err = _userCache.CrossMarginBalance(_assetInfo, pCache)
// 					if err != nil {
// 						logrus.Error(0, pos.ContractCode, pos.UID, "updatePos MarginModeCross CrossMarginBalance error:", err)
// 						return err
// 					}
// 					bpi.CollapsePrice, bpi.CollapsePriceFormula, err = pos.CrossCollapsePrice(_assetInfo,
// 						_userCache.HoldCostTotalIsolated(pos.Currency, true), _userCache.OtherCrossUnreal(pCache),
// 						_userCache.OtherCrossMaintainMargin(pCache), userLevelRateInfo.ContractTaker, userLevelRateInfo.ContractMaker, pCache)
// 					if err != nil {
// 						logrus.Error(0, pos.ContractCode, pos.UID, "updatePos MarginModeCross CrossCollapsePrice error:", err)
// 						return err
// 					}
// 					if bpi.CollapsePrice.LessThanOrEqual(decimal.Zero) {
// 						bpi.CollapsePrice, err = sharedcache.GetContractLastPrice(util.BaseQuote(strings.ToUpper(pos.ContractCode)))
// 						if err != nil {
// 							logrus.Error(0, pos.ContractCode, pos.UID, "updatePos MarginModeCross GetContractLastPrice error:", err)
// 							return err
// 						}
// 					}

// 					// 预估强评价
// 					totalBalance := _assetInfo.CBalance(pos.Currency)
// 					rate := decimal.NewFromInt(1)
// 					if _assetInfo.AssetMode == domain.AssetMode {
// 						totalBalance, err = _assetInfo.TotalJoinBalance(pCache)
// 						if err != nil {
// 							logrus.Error(0, "updatePos TotalBalance crossed error:", err)
// 							return err
// 						}
// 						rate = pCache.SpotURate(pos.Currency)
// 					}
// 					bpi.LiquidationPrice = pos.CalcLiquidationPrice(pCache, _assetInfo.LongPos, _assetInfo.ShortPos, _assetInfo.BothPos,
// 						_userCache.HoldCostTotalIsolated(pos.Currency, true).Mul(rate),
// 						totalBalance, _userCache.OtherCrossUnreal(pCache).Mul(rate),
// 						_userCache.OtherCrossMaintainMargin(pCache).Mul(rate))
// 					// 保证金
// 					bpi.TotalMargin = _userCache.HoldCostTotalCross(pCache, _assetInfo.AssetMode, pos.Currency)
// 					// 开仓时间
// 					bpi.OpenTime = pos.OpenTime
// 				}

// 			}
// 		}

// 	case domain.MarginModeIsolated:
// 		switch _posInfo.PosSide {
// 		case domain.LongPos:
// 			if frozenMargin, ok := _assetInfo.Frozen[util.FrozenKey(_posInfo.ContractCode, int(domain.LongPos))]; ok {
// 				bpi.FrozenMargin = bpi.FrozenMargin.Add(frozenMargin)
// 			}
// 			bpi.LongPosAmount = bpi.LongPosAmount.Add(_posInfo.Pos)
// 			bpi.LongPosValue = bpi.LongPosValue.Add(_posInfo.CalcPosValue(pCache))

// 		case domain.ShortPos:
// 			if frozenMargin, ok := _assetInfo.Frozen[util.FrozenKey(_posInfo.ContractCode, int(domain.ShortPos))]; ok {
// 				bpi.FrozenMargin = bpi.FrozenMargin.Add(frozenMargin)
// 			}
// 			bpi.ShortPosAmount = bpi.ShortPosAmount.Add(_posInfo.Pos)
// 			bpi.ShortPosValue = bpi.ShortPosValue.Add(_posInfo.CalcPosValue(pCache))

// 		case domain.BothPos:
// 			if _posInfo.Pos.GreaterThan(decimal.Zero) {
// 				if frozenMargin, ok := _assetInfo.Frozen[util.FrozenKey(_posInfo.ContractCode, int(domain.LongPos))]; ok {
// 					bpi.FrozenMargin = bpi.FrozenMargin.Add(frozenMargin)
// 				}
// 				bpi.LongPosAmount = bpi.LongPosAmount.Add(_posInfo.Pos.Abs())
// 				bpi.LongPosValue = bpi.LongPosValue.Add(_posInfo.CalcPosValue(pCache))
// 			} else if _posInfo.Pos.GreaterThan(decimal.Zero) {
// 				if frozenMargin, ok := _assetInfo.Frozen[util.FrozenKey(_posInfo.ContractCode, int(domain.ShortPos))]; ok {
// 					bpi.FrozenMargin = bpi.FrozenMargin.Add(frozenMargin)
// 				}
// 				bpi.ShortPosAmount = bpi.ShortPosAmount.Add(_posInfo.Pos.Abs())
// 				bpi.ShortPosValue = bpi.ShortPosValue.Add(_posInfo.CalcPosValue(pCache))
// 			}

// 		default:

// 		}

// 		bpi.FrozenPos = bpi.FrozenPos.Add(_posInfo.Pos.Abs().Sub(_posInfo.PosAvailable.Abs()))
// 		bpi.MarkPrice = pCache.GetMarkPrice(_posInfo.ContractCode)
// 		bpi.BurstPrice = pCache.GetMarkPrice(_posInfo.ContractCode)

// 		switch _posInfo.PosSide {
// 		case domain.LongPos:
// 			bpi.LongPosPrice = _posInfo.OpenPriceAvg
// 			bpi.LongPosUnReal = _posInfo.ProfitUnreal

// 		case domain.ShortPos:
// 			bpi.ShortPosPrice = _posInfo.OpenPriceAvg
// 			bpi.ShortPosUnReal = _posInfo.ProfitUnreal

// 		case domain.BothPos:
// 			if _posInfo.Pos.GreaterThan(decimal.Zero) {
// 				bpi.LongPosPrice = _posInfo.OpenPriceAvg
// 				bpi.LongPosUnReal = _posInfo.ProfitUnreal
// 			} else if _posInfo.Pos.LessThan(decimal.Zero) {
// 				bpi.ShortPosPrice = _posInfo.OpenPriceAvg
// 				bpi.ShortPosUnReal = _posInfo.ProfitUnreal
// 			}

// 		default:

// 		}

// 		bpi.BurstTime = _burstTime
// 		bpi.BurstLevel = _filter.Level
// 		bpi.MarginBalance = _posInfo.IsolatedMarginBalance(pCache)
// 		bpi.CollapsePrice, bpi.CollapsePriceFormula = _posInfo.IsolatedCollapsePrice(userLevelRateInfo.ContractTaker, userLevelRateInfo.ContractMaker)
// 		if bpi.CollapsePrice.LessThanOrEqual(decimal.Zero) {
// 			base, quote := util.BaseQuote(strings.ToUpper(_posInfo.ContractCode))
// 			bpi.CollapsePrice, err = sharedcache.GetContractLastPrice(base, quote)
// 			if err != nil {
// 				logrus.Error(0, _posInfo.ContractCode, _posInfo.UID, "updatePos MarginModeIsolated GetContractLastPrice error:", err)
// 			}
// 		}

// 		// 预估强评价
// 		totalBalance := _assetInfo.CBalance(_posInfo.Currency)
// 		rate := decimal.NewFromInt(1)
// 		if _assetInfo.AssetMode == domain.AssetMode {
// 			totalBalance, err = _assetInfo.TotalJoinBalance(pCache)
// 			if err != nil {
// 				logrus.Error(0, "updatePos TotalBalance isolated error:", err)
// 				return err
// 			}
// 			rate = pCache.SpotURate(_posInfo.Currency)
// 		}

// 		bpi.LiquidationPrice = _posInfo.CalcLiquidationPrice(pCache, _assetInfo.LongPos, _assetInfo.ShortPos, _assetInfo.BothPos,
// 			_userCache.HoldCostTotalIsolated(_posInfo.Currency, false).Mul(rate),
// 			totalBalance, _userCache.OtherCrossUnreal(pCache).Mul(rate),
// 			_userCache.OtherCrossMaintainMargin(pCache).Mul(rate))
// 		// 保证金
// 		// bpi.TotalMargin = _userCache.HoldCostTotalCross(pCache, _assetInfo.AssetMode, _posInfo.Currency)
// 		bpi.TotalMargin = _userCache.HoldCostTotalIsolated(_posInfo.Currency, false)
// 		// 开仓时间
// 		bpi.OpenTime = _posInfo.OpenTime

// 	default:

// 	}

// 	return nil
// }
