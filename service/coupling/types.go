package coupling

// import (
// 	"fmt"

// 	"futures-asset/internal/delivery/http/payload"
// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/entity"
// 	"futures-asset/internal/domain/repository"
// 	"futures-asset/service/persist"

// 	"github.com/sirupsen/logrus"
// )

// type ICoupling interface {
// 	Trade(_req *payload.TradeParam) (int32, payload.TradeReply, error)
// 	SelfTrade(_req *payload.TradeParam) (int32, payload.TradeReply, error)
// 	TradeTaker(_req *payload.TradeParam) (int32, payload.TradeReply, error) // Maker错误单处理
// 	TradeMaker(_req *payload.TradeParam) (int32, payload.TradeReply, error) // Taker错误单处理
// }

// func TradeAssetPosToMq(_redis *redislib.Config, assetChanges []*repository.LogAssetSync, posChanges []*repository.LogPosSync) {
// 	if _redis == nil {
// 		_redis = redislib.Redis()
// 	}
// 	for _, asset := range assetChanges {
// 		if asset != nil {
// 			if asset.AssetSwap.UID == "" {
// 				logrus.Error(fmt.Sprintf("asset log not have user id, asset:%+v", asset))
// 				continue
// 			}
// 			if err := _redis.LPush(domain.SyncListSwapAsset+asset.AssetSwap.UID[len(asset.AssetSwap.UID)-1:], asset); err != nil {
// 				logrus.Error(fmt.Sprintf("lpush asset change log err:%v, asset:%+v", err, asset))
// 			}
// 		}
// 	}

// 	for _, pos := range posChanges {
// 		if pos != nil {
// 			if err := persist.SyncPos(_redis, pos.LogPos.ContractCode, pos); err != nil {
// 				logrus.Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
// 			}
// 		}
// 	}
// }

// func AddAssetLogs(_redis *redislib.Config, logs ...*repository.MqCmsAsset) {
// 	if _redis == nil {
// 		_redis = redislib.Redis()
// 	}
// 	for _, log := range logs {
// 		if log != nil {
// 			if err := _redis.LPush(domain.ContractAssetKey, log); err != nil {
// 				logrus.Error(fmt.Sprintf("lpush cms_asset record err:%v, log:%+v", err, log))
// 			}
// 		}
// 	}
// }

// func AddBills(_redis *redislib.Config, bills ...repository.BillAssetSync) {
// 	if _redis == nil {
// 		_redis = redislib.Redis()
// 	}
// 	for _, bill := range bills {
// 		if bill.Amount.IsZero() {
// 			continue
// 		}
// 		redisKey := domain.SyncListSwapBill
// 		if domain.RobotUsers.HasKey(bill.UID) {
// 			redisKey = domain.SyncListSwapBillRobot
// 		}
// 		if err := _redis.LPush(redisKey, bill); err != nil {
// 			logrus.Error(fmt.Sprintf("lpush bill err:%v, bill:%+v", err, bill))
// 		}
// 	}
// }

// func AddOptionBills(_redis *redislib.Config, bills ...repository.BillOptionSync) {
// 	if _redis == nil {
// 		_redis = redislib.Redis()
// 	}
// 	for _, bill := range bills {
// 		if bill.Amount.IsZero() {
// 			continue
// 		}
// 		redisKey := domain.SyncListOptionBill
// 		if err := _redis.LPush(redisKey, bill); err != nil {
// 			logrus.Error(fmt.Sprintf("lpush option bill err:%v, bill:%+v", err, bill))
// 		}
// 	}
// }

// func AddTrialChange(_redis *redislib.Config, trials []*entity.TrialAsset) {
// 	if _redis == nil {
// 		_redis = redislib.Redis()
// 	}
// 	for _, trial := range trials {
// 		logrus.Info(1, "AddTrialChange", fmt.Sprintf("trials %+v", *trial))
// 		if err := _redis.LPush(domain.SyncTrialAssetSwap, trial); err != nil {
// 			logrus.Error(fmt.Sprintf("sql lpush trial asset add err:%v,trial:%+v", err, trial))
// 		}
// 	}
// }
