package coupling

// import (
// 	"fmt"
// 	"net/http"

// 	"futures-asset/internal/delivery/http/payload"
// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/db/swap"

// 	"github.com/pkg/errors"
// 	"github.com/sirupsen/logrus"
// )

// type PosService struct {
// }

// func NewPos() *PosService {
// 	return &PosService{}
// }

// func (slf *PosService) Trade(_req *payload.TradeParam, origin payload.TradeParam) (code domain.Code, reply payload.TradeReply, err error) {
// 	if _req.IsErr {
// 		// 成交错误单处理
// 		return tradeError(_req)
// 	} else {
// 		// 正常成交逻辑
// 		return trade(_req, origin)
// 	}
// }

// func trade(_req *payload.TradeParam, origin payload.TradeParam) (code domain.Code, reply payload.TradeReply, err error) {
// 	reply = payload.TradeReply{}

// 	trader := Trade{}
// 	code, reply, err = trader.Trade(_req)
// 	if err != nil {
// 		return code, reply, err
// 	}

// 	if origin.Taker.UserType == domain.UserTypePlatformRobot && origin.Maker.UserType == domain.UserTypePlatformRobot {
// 		// 机器人自成交不记录流水
// 		return code, reply, nil
// 	}

// 	redis := redislib.Redis()
// 	if err := redis.LPush(domain.ContractTradeKey, origin); err != nil {
// 		logrus.Error(fmt.Sprintf("mq lpush Asset fund Cost err:%v", err))
// 	}

// 	return code, reply, nil
// }

// func tradeError(_req *payload.TradeParam) (code domain.Code, reply payload.TradeReply, err error) {
// 	db, err := sqllib.Db()
// 	if err != nil {
// 		return domain.Code252407, reply, errors.Errorf("get db err: %s", err.Error())
// 	}

// 	reply = payload.TradeReply{
// 		Taker: payload.Reply{
// 			PosId:      takerLog.PosId,
// 			ProfitReal: takerLog.ProfitReal,
// 		},
// 		Maker: payload.Reply{
// 			PosId:      makerLog.PosId,
// 			ProfitReal: makerLog.ProfitReal,
// 		},
// 	}
// 	// taker maker都存在, 不做处理
// 	if takerLog.Id > 0 && makerLog.Id > 0 {
// 		return http.StatusOK, reply, nil
// 	}

// 	trader := Trade{}

// 	// 情况1: taker maker 都不存在, 重新走成交逻辑
// 	if takerLog.Id == 0 && makerLog.Id == 0 {
// 		return trader.Trade(_req)
// 	}
// 	// 情况2: taker不存在, 重新走Taker逻辑
// 	if takerLog.Id == 0 {
// 		return trader.TradeErr(_req, true)
// 	}
// 	// 情况3: maker不存在, 重新走Maker逻辑
// 	if makerLog.Id == 0 {
// 		return trader.TradeErr(_req, false)
// 	}

// 	return http.StatusOK, reply, nil
// }
