package persist

// import (
// 	"encoding/json"
// 	"fmt"

// 	"futures-asset/internal/domain"
// 	"futures-asset/pkg/redislib"
// 	"futures-asset/pkg/rocketmq"
// 	"futures-asset/util"

// 	"github.com/sirupsen/logrus"
// )

// type SyncMidType int // 下单中间件类型

// const (
// 	MidTypeRedis SyncMidType = 1
// 	MidTypeMQ    SyncMidType = 2

// 	midType = MidTypeMQ
// )

// func SyncPos(rc *redislib.Config, symbol string, data interface{}) error {
// 	if len(symbol) == 0 {
// 		logrus.Warnf("pos persist symbol is empty")
// 		return nil
// 	}

// 	switch midType {
// 	case MidTypeRedis:
// 		if err := rc.LPush(domain.SyncListSwapPos+symbol, data); err != nil {
// 			logrus.Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, data))
// 			return err
// 		}
// 	case MidTypeMQ:
// 		dataBytes, err := json.Marshal(data)
// 		if err != nil {
// 			return err
// 		}
// 		base, quote := util.BaseQuote(symbol)
// 		err = rocketmq.SendSync(rocketmq.TopicPos, base, quote, dataBytes)
// 		return err
// 	default:
// 	}

// 	return nil
// }
