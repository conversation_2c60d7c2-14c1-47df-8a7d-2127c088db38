// Package safe provides helpers for gracefully handling panics in background
// goroutines.
// https://github.com/deliveroo/safe-go/blob/master/safe.go
package safely

import (
	"fmt"
	"github.com/robfig/cron"
	"github.com/shopspring/decimal"
	"testing"
	"time"
)

func TestGo(t *testing.T) {
	Run(cleanData)
	time.Sleep(10 * time.Second)
}

func cleanData() {
	c := cron.New()
	// 每分钟起一次定时任务
	_ = c.AddFunc("* * * * * ?", printMsg)
	c.Start()
}

func printMsg() {
	fmt.Println("test msg...")
	m := decimal.NewFromFloat(2.2)
	fmt.Println(m.Div(decimal.Zero))
}
