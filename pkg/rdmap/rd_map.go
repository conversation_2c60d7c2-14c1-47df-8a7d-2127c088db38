package rdmap

import (
	"sync"
)

type RdMap struct {
	Data map[string]interface{}
	Lock *sync.RWMutex
}

func NewRdMap() *RdMap {
	return &RdMap{
		Data: make(map[string]interface{}),
		Lock: &sync.RWMutex{},
	}
}

func (d RdMap) HasKey(k string) bool {
	d.Lock.RLock()
	defer d.Lock.RUnlock()
	_, ok := d.Data[k]
	return ok
}

func (d RdMap) Keys() []string {
	d.Lock.RLock()
	defer d.Lock.RUnlock()
	keys := make([]string, 0)
	for v := range d.Data {
		keys = append(keys, v)
	}
	return keys
}

func (d RdMap) Set(k string, v interface{}) {
	d.Lock.Lock()
	defer d.Lock.Unlock()
	d.Data[k] = v
}
