package setting

// import (
// 	"bytes"
// 	"encoding/json"
// 	"errors"
// 	"fmt"
// 	"io/ioutil"
// 	"net/http"
// 	"sort"
// 	"strings"

// 	"futures-asset/cache/pair"
// 	"futures-asset/conf"
// 	"futures-asset/internal/domain"
// 	"futures-asset/pkg/httplib"
// 	"futures-asset/pkg/redislib"
// 	"futures-asset/util"

// 	"github.com/redis/go-redis/v9"
// 	"github.com/shopspring/decimal"
// 	"github.com/sirupsen/logrus"
// )

// // 获取合约配置的redis key
// func getContractSettingRedisKey() string {
// 	return domain.ContractSetting
// }

// // 获取合约配置等级的redis key
// func getContractSettingLevelRedisKey() string {
// 	return fmt.Sprintf("%s:level", domain.ContractSetting)
// }

// func GetOptionProfitDateKey() string {
// 	return domain.OptionProfitLossDate
// }

// func GetOptionProfitDataKeyByTime(_timeStr string) string {
// 	return domain.OptionProfitLossData + _timeStr
// }

// func getProfitKey() string {
// 	return domain.ProfitKey
// }

// type setting struct {
// }

// var Service *setting

// type LevelFilter struct {
// 	Level             int
// 	Lever             int
// 	HighLimit         decimal.Decimal
// 	LowLimit          decimal.Decimal
// 	InitMarginRate    decimal.Decimal
// 	WarnMarginRate    decimal.Decimal
// 	HoldingMarginRate decimal.Decimal
// }

// type CurrencyWhiteListConfig struct {
// 	Currency       string               `json:"currency"`
// 	CurrencyDetail map[string]WhiteList `json:"currencyDetail"`
// }
// type WhiteList struct {
// 	UserWhiteState bool     `json:"userWhiteState"`
// 	UserWhiteList  []string `json:"userWhiteList"`
// }

// type Profit struct {
// 	InitialFund decimal.Decimal
// 	CurrentFund decimal.Decimal
// 	ProfitRate  float64
// }

// func GetProfitFromRedis(uid string) (*Profit, error) {
// 	data, err := redislib.Redis().HGet(getProfitKey(), uid)
// 	if err != nil {
// 		return nil, err
// 	}
// 	profit := new(Profit)
// 	err = json.Unmarshal([]byte(data), profit)
// 	if err != nil {
// 		return nil, err
// 	}
// 	return profit, nil
// }

// func (slf *Profit) Flush(uid string) error {
// 	data, err := json.Marshal(slf)
// 	if err != nil {
// 		return err
// 	}
// 	err = redislib.Redis().HSet(getProfitKey(), uid, string(data))
// 	if err != nil {
// 		return err
// 	}
// 	return nil
// }

// func (slf *Profit) Calculate() error {
// 	if slf.InitialFund.IsZero() {
// 		return fmt.Errorf("InitialFund is zero")
// 	}
// 	profit := slf.CurrentFund.Sub(slf.InitialFund)
// 	if profit.IsNegative() {
// 		slf.ProfitRate = 0
// 		logrus.Infof("no profit, current fund: %s, initial fund: %s", slf.CurrentFund.String(), slf.InitialFund.String())
// 		return nil
// 	}
// 	profitRate := profit.Div(slf.InitialFund)
// 	slf.ProfitRate = profitRate.InexactFloat64()
// 	return nil
// }
