package setting

// import (
// 	"github.com/shopspring/decimal"
// )

// type BaseResponse struct {
// 	Code int    `json:"code"`
// 	Msg  string `json:"msg"`
// }

// type MarginRateLevelParams struct {
// 	Base  string `json:"base"`
// 	Quote string `json:"quote"`
// }

// type CoinPairsReply struct {
// 	Code int            `json:"code"`
// 	Msg  string         `json:"msg"`
// 	Data []ContractPair `json:"data"`
// }

// type (
// 	AllContractPairReply struct {
// 		Code int      `json:"code"`
// 		Msg  string   `json:"msg"`
// 		Data []string `json:"data"`
// 	}
// )

// type UserLevelsReply struct {
// 	Code int            `json:"code"`
// 	Msg  string         `json:"msg"`
// 	Data UserLevelsData `json:"data"`
// }
