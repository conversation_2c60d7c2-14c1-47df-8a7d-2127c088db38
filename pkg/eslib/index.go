package eslib

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"github.com/olivere/elastic"
)

type ESBase struct {
	Id              string
	index           string
	query           string
	from            int
	size            int
	PhrasePrefix    bool                              // true:按词拆分搜索 false:按字拆分进行搜索
	conditions      map[string]interface{}            // 过滤条件
	ranges          map[string]map[string]interface{} // 范围条件
	sortFields      map[string]bool                   // 排序字段
	searchFields    []string
	highlightFields []string
	terms           map[string][]interface{}
	sumAmount       bool // 是否对amount求和

	termQuery     *elastic.TermsQuery
	boolQuery     *elastic.BoolQuery
	searchService *elastic.SearchService
}

func New(index string) *ESBase {
	query := &ESBase{
		index: index,
	}
	if query.size == 0 {
		query.size = 50
	}

	return query
}

func (slf *ESBase) SetSumAmount() *ESBase {
	slf.sumAmount = true
	return slf
}

func (slf *ESBase) SetQuery(param string) *ESBase {
	slf.query = param
	return slf
}

func (slf *ESBase) SetFrom(param int) *ESBase {
	if param == 0 {
		return slf
	}
	slf.from = param
	return slf
}

func (slf *ESBase) SetSize(param int) *ESBase {
	if param == 0 {
		return slf
	}
	slf.size = param
	return slf
}

func (slf *ESBase) SetSearchFields(param []string) *ESBase {
	slf.searchFields = param
	return slf
}

func (slf *ESBase) SetHighlightFields(param []string) *ESBase {
	slf.highlightFields = param
	return slf
}

func (slf *ESBase) SetPhrasePrefix(param bool) *ESBase {
	slf.PhrasePrefix = param
	return slf
}

func (slf *ESBase) SetConditions(conditions map[string]interface{}) *ESBase {
	slf.conditions = conditions
	return slf
}

func (slf *ESBase) SetRanges(ranges map[string]map[string]interface{}) *ESBase {
	slf.ranges = ranges
	return slf
}

func (slf *ESBase) SetSortFields(param map[string]bool) *ESBase {
	slf.sortFields = param
	return slf
}

func (slf *ESBase) SetTerms(terms map[string][]interface{}) *ESBase {
	slf.terms = terms
	return slf
}

func (slf *ESBase) CreateIndex(ctx context.Context, mapping string) error {
	res, err := ES().IndexExists(slf.index).Do(ctx)
	if err != nil {
		return err
	}
	if !res {
		_, err = ES().CreateIndex(slf.index).Body(mapping).Do(ctx)
		if err != nil {
			return err
		}
	}
	return nil
}

func (slf *ESBase) DeleteIndex(ctx context.Context) error {
	_, err := ES().DeleteIndex(slf.index).Do(ctx)
	if err != nil {
		return err
	}
	return nil
}

func (slf *ESBase) Create(ctx context.Context, body interface{}) error {
	_, err := ES().Index().
		Index(slf.index).
		Type("_doc").
		Id(slf.Id).
		BodyJson(body).
		Do(ctx)
	return err
}

func (slf *ESBase) Update(ctx context.Context, body map[string]interface{}) error {
	_, err := ES().Update().
		Index(slf.index).
		Type("_doc").
		Id(slf.Id).
		Doc(body).
		DetectNoop(true).
		Do(ctx)
	return err
}

func (slf *ESBase) UpdateByQuery(ctx context.Context, scriptStr string, condition map[string]interface{}) error {
	conditions := make([]elastic.Query, 0)
	for key, value := range condition {
		conditions = append(conditions, elastic.NewMatchQuery(key, value))
	}
	boolQuery := elastic.NewBoolQuery().Must(conditions...)
	//PrintQueryDSL(boolQuery)

	script := elastic.NewScript(scriptStr)
	_, err := ES().UpdateByQuery().
		Index(slf.index).
		Query(boolQuery).
		Script(script).
		Do(ctx)
	return err
}

func (slf *ESBase) Search(ctx context.Context) (*elastic.SearchResult, error) {
	search := ES().Search().Index(slf.index).Query(slf.buildBoolCondition())

	if slf.sumAmount {
		sumAgg := elastic.NewSumAggregation().Field("amount")
		fmt.Println(sumAgg.Source())
		search.Aggregation("sumAmount", sumAgg)
	}
	for key, value := range slf.sortFields {
		search = search.Sort(key, value)
	}

	reply, err := search.From(slf.from).
		Size(slf.size). // take documents 0-9
		Do(ctx)

	return reply, err
}

func (slf *ESBase) buildBoolCondition() *elastic.BoolQuery {
	slf.boolQuery = elastic.NewBoolQuery()
	for key, value := range slf.conditions {
		if value != nil {
			switch value.(type) {
			case []int:
				if integers, ok := value.([]int); ok {
					queries := make([]elastic.Query, 0)
					for _, n := range integers {
						queries = append(queries, elastic.NewMatchQuery(key, n))
					}
					slf.boolQuery = slf.boolQuery.Must(elastic.NewBoolQuery().Should(queries...))
				}
			default:
				slf.boolQuery = slf.boolQuery.Must(elastic.NewMatchQuery(key, value).Operator("and"))
			}
		}
	}
	for key, value := range slf.terms {
		if value != nil {
			slf.boolQuery = slf.boolQuery.Filter(elastic.NewTermsQuery(key, value...))
		}
	}

	if len(slf.ranges) > 0 {
		for key, rangeMap := range slf.ranges {
			if rangeMap != nil {
				tempQuery := elastic.NewRangeQuery(key)
				for cmd, value := range rangeMap {
					switch cmd {
					case "gte":
						tempQuery = tempQuery.Gte(value)
					case "lte":
						tempQuery = tempQuery.Lte(value)
					case "gt":
						tempQuery = tempQuery.Gt(value)
					case "lt":
						tempQuery = tempQuery.Lt(value)
					}
				}
				slf.boolQuery = slf.boolQuery.Filter(tempQuery)
			}
		}
	}
	//PrintQueryDSL(slf.boolQuery)

	return slf.boolQuery
}

func (slf *ESBase) HighlightFields() *ESBase {
	highlighterFields := make([]*elastic.HighlighterField, 0)
	if len(slf.highlightFields) > 0 {
		for _, field := range slf.highlightFields {
			highlight := elastic.NewHighlighterField(field).PreTags("<tag1>").PostTags("</tag1>")
			highlighterFields = append(highlighterFields, highlight)
		}
	}
	highLight := elastic.NewHighlight().Fields(highlighterFields...)

	slf.searchService.Highlight(highLight)
	return slf
}

func PrintQueryDSL(query *elastic.BoolQuery) {
	src, _ := query.Source()
	var data, err = json.MarshalIndent(src, "", "  ")
	log.Println("PrintSource data:", string(data), err)
}

func (slf *ESBase) BulkDeleteByUserId(_userIds []string) error {
	if len(_userIds) <= 0 {
		return nil
	}
	boolQuery := elastic.NewBoolQuery()
	subQuery := elastic.NewBoolQuery()
	for _, uid := range _userIds {
		subQuery.Should(elastic.NewTermQuery("uid", uid))
	}
	boolQuery.Must(subQuery)
	_, err := ES().
		DeleteByQuery().
		Index(slf.index).
		Query(boolQuery).DoAsync(context.Background())
	return err
}
