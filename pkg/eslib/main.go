package eslib

import "github.com/olivere/elastic"

type Config struct {
	client *elastic.Client
}

var config *Config

func Init(addrs []string) error {
	client, err := elastic.NewClient(
		elastic.SetURL(addrs...),
		elastic.SetSniff(false),
	)
	config = &Config{client: client}

	return err
}

func ES() *elastic.Client {
	return config.client
}

func Bulk() *elastic.BulkService {
	return config.client.Bulk()
}
