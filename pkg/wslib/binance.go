package wslib

// import (
// 	"encoding/json"
// 	"fmt"
// 	"net/url"
// 	"runtime/debug"
// 	"strings"
// 	"sync"
// 	"time"

// 	"futures-asset/cache"

// 	"github.com/gorilla/websocket"
// 	"github.com/sirupsen/logrus"
// )

// type binanceWs struct {
// 	ws
// 	lock             *sync.RWMutex
// 	supportContracts []string
// }

// var BinanceWs *binanceWs

// type binanceMarkPriceData struct {
// 	Event      string `json:"e"`
// 	Symbol     string `json:"s"`
// 	MarkPrice  string `json:"p"`
// 	IndexPrice string `json:"i"`
// }

// func InitBinanceWs(_host string) {
// 	BinanceWs = new(binanceWs)
// 	BinanceWs.newWs(_host)
// 	return
// }

// func (bw *binanceWs) newWs(_host string) {
// 	bw.host = _host
// 	bw.lock = new(sync.RWMutex)
// 	bw.supportContracts = sharedcache.GetBurstServerContracts()
// 	u := url.URL{Scheme: "wss", Host: _host, Path: "/ws"}
// 	logrus.Infoln("connecting to ", u.String())
// 	var err error
// 	bw.conn, _, err = websocket.DefaultDialer.Dial(u.String(), nil)
// 	if err != nil {
// 		logrus.Errorln("dial ", u.String(), " error: ", err)
// 	}
// 	time.Sleep(time.Second)
// 	go bw.heartbeat()
// 	go bw.processMessage()
// 	return
// }

// func (bw *binanceWs) syncSupportContracts() {
// 	sharedcache.GetBurstServerContracts()
// }

// func (bw *binanceWs) heartbeat() {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			// logrus.Errorln("binanceWs heartbeat recover error exit: ", err)
// 			logrus.Error(0, "binanceWs heartbeat recover error exit: ", err)
// 			logrus.Error(0, string(debug.Stack()))
// 		}
// 	}()
// 	ticker := time.NewTicker(time.Second * 15)
// 	for {
// 		select {
// 		case <-ticker.C:
// 			dline := time.Now().Add(time.Duration(10) * time.Second)
// 			err := bw.conn.WriteControl(websocket.PingMessage, []byte{}, dline)
// 			if err != nil {
// 				logrus.Error(0, "heartbeat", err)
// 			}
// 		}
// 	}
// }

// func (bw *binanceWs) processMessage() {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			// logrus.Errorln("binanceWs processMessage recover error exit: ", err)
// 			logrus.Error(0, "binanceWs processMessage recover error exit: ", err)
// 			logrus.Error(0, string(debug.Stack()))
// 			time.Sleep(time.Second * 15)
// 			bw.newWs(bw.host)
// 		}
// 	}()
// 	for {
// 		msgType, msgBytes, err := bw.conn.ReadMessage()
// 		if err != nil {
// 			logrus.Errorln("binanceWs processMessage ReadMessage error: ", err)
// 			continue
// 		}
// 		switch msgType {
// 		case websocket.TextMessage:
// 			bw.parseMsg(msgBytes)
// 		default:
// 			logrus.Infoln("binanceWs ReadMessage ", string(msgBytes))
// 		}
// 	}
// }

// func (bw *binanceWs) Subscribe(_channel string) {
// 	subMsgBytes, err := json.Marshal(map[string]interface{}{
// 		"method": "SUBSCRIBE",
// 		"params": []string{_channel},
// 		"id":     1,
// 	})
// 	if err != nil {
// 		logrus.Errorln("binanceWs Subscribe Marshal error: ", err)
// 		return
// 	}
// 	bw.conn.WriteMessage(websocket.TextMessage, subMsgBytes)
// 	return
// }

// func (bw *binanceWs) Unsubscribe(_channel string) {
// 	subMsgBytes, err := json.Marshal(map[string]interface{}{
// 		"method": "UNSUBSCRIBE",
// 		"params": []string{_channel},
// 		"id":     1,
// 	})
// 	if err != nil {
// 		logrus.Errorln("binanceWs Unsubscribe Marshal error: ", err)
// 		return
// 	}
// 	bw.conn.WriteMessage(websocket.TextMessage, subMsgBytes)
// 	return
// }

// func (bw *binanceWs) parseMsg(_msgBytes []byte) {
// 	markPriceList := make([]binanceMarkPriceData, 0)
// 	_ = json.Unmarshal(_msgBytes, &markPriceList)

// 	for _, data := range markPriceList {
// 		switch data.Event {
// 		case "markPriceUpdate":
// 			bw._updateSyncMarkPriceData(data)
// 		default:
// 			logrus.Infoln("binanceWs parseMsg ", string(_msgBytes))
// 		}
// 	}
// }

// func (bw *binanceWs) _updateSyncMarkPriceData(_binanceMarkPriceData binanceMarkPriceData) {
// 	bw.lock.RLock()
// 	burstContracts := bw.supportContracts
// 	bw.lock.RUnlock()
// 	if len(burstContracts) < 1 {
// 		logrus.Errorln(fmt.Sprintf("binanceWs parseMsg syncCoinPair is empty"))
// 		return
// 	}
// 	for _, coinPair := range burstContracts {
// 		temp := strings.Replace(coinPair, "-", "", -1)
// 		if _binanceMarkPriceData.Symbol == strings.ToUpper(temp) {
// 			indexKey := fmt.Sprintf("%sbinance:index:%s", cache.Prefix, strings.ToUpper(coinPair))
// 			redislib.Redis().SetString(indexKey, _binanceMarkPriceData.IndexPrice)
// 			redislib.Redis().SetExpire(indexKey, "2m")
// 		}
// 	}
// 	return
// }
