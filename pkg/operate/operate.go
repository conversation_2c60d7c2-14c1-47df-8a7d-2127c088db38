package operate

// import (
// 	"errors"
// 	"time"

// 	"futures-asset/conf"

// 	"github.com/imroc/req"
// 	"github.com/shopspring/decimal"
// )

// type (
// 	ResBalance struct {
// 		Code int             `json:"code"`
// 		Msg  string          `json:"msg"`
// 		Data decimal.Decimal `json:"data"`
// 	}
// )

// // GetRiskReserve 运营后台 获取风险准备金
// func GetRiskReserve() (decimal.Decimal, error) {
// 	req.SetTimeout(20 * time.Second)
// 	r, err := req.Post("http://"+conf.Conf.Biz.Services["cms_oprate"]+"/v2/risk/swap/amount", nil)
// 	if err != nil {
// 		return decimal.Zero, err
// 	}
// 	resp := ResBalance{}
// 	if err := r.ToJSON(&resp); err != nil {
// 		return decimal.Zero, err
// 	}
// 	if resp.Code != 200 {
// 		return decimal.Zero, errors.New(resp.Msg)
// 	}

// 	return resp.Data, nil
// }
