package match

// 用户类型
const (
	UserNormal    = 1 // 普通用户
	UserNormalApi = 2 // 普通用户已创建API
	UserRobotFee  = 3 // 机器人有手续费
	UserRobot     = 4 // 机器人无手续费
	UserPRobotFee = 5 // 项目方有手续费机器人
	UserPRobot    = 6 // 项目方无手续费机器人
)

// 委托单状态
const (
	OrderStateCreated         = 1 // 已创建
	OrderStateSubmitted       = 2 // 等待成交，已进入撮合队列
	OrderStatePartialFilled   = 3 // 部分成交
	OrderStateFilled          = 4 // 完全成交
	OrderStatePartialCanceled = 5 // 部分成交撤销
	OrderStateCanceled        = 6 // 已撤销
)

// 委托方向
const (
	SideBuy  = 1 // 买
	SideSell = 2 // 卖
)

// 是否限价委托
const (
	IsLimitOrderYes = 1 // 是限价委托
	IsLimitOrderNo  = 2 // 不是限价委托（是市价委托）
)

// 委托类型
const (
	OrderTypePostOnly = 7 // 只做Maker

	OrderTypeNormal         = 10 // 普通订单
	OrderTypePlan           = 20 // 计划委托
	OrderTypeTakeProfitAll  = 31 // 全部止盈
	OrderTypeTakeProfitPart = 32 // 部分止盈
	OrderTypeStopLossAll    = 36 // 全部止损
	OrderTypeStopLossPart   = 37 // 部分止损
)

// 是否混合保证金
const (
	NotMixMargin = 0 // 混合保证金
	IsMixMargin  = 1 // 混合保证金
)

// 持仓模式
const (
	HoldModeSingleSidePosition = 1 // 双向持仓
	HoldModeBothSidePosition   = 2 // 单向持仓
)

// 有效方式
const (
	TimeInForceTypeGTC = "GTC" // 成交为止
	TimeInForceTypeIOC = "IOC" // 立即成交否则取消
	TimeInForceTypeFOK = "FOK" // 立即完全成交否则取消
)

// 开平方向
const (
	OffsetOpen  = 1 // 开
	OffsetClose = 2 // 平
)

// 账户类型(需钱包服务类提供)
const (
	AccountTypeSpot    = "spot"    // 币币账户
	AccountTypeMargin  = "margin"  // 逐仓杠杆账户
	AccountTypeSwap    = "swap"    // 永续合约账户
	AccountTypeFutures = "futures" // 交割合约账户
)

// 平台来源
const (
	PlatformApi    = "API"
	PlatformWeb    = "WEB"
	PlatformApp    = "APP"
	PlatformSystem = "SYSTEM"
)

// 事件类型
const (
	EventTypePlace     = 1
	EventTypeCancel    = 2
	EventTypeTriggered = 3
)

// 委托单或成交单错误单类型
const (
	ErrTypeFailed  = 1 // 解冻失败
	ErrTypeTimeOut = 2 // 解冻超时
	ErrTypeDealed  = 3 // 处理成功
)

// 触发类型
const (
	TriggerTypeGte = "gte" // 大于等于
	TriggerTypeLte = "lte" // 小于等于
)

// 深度变化类型
const (
	DepthChgTypeAdd = "add"
	DepthChgTypeSub = "sub"
)

// 角色类型
const (
	RoleMaker = 1 // maker
	RoleTaker = 2 // taker
)

// 委托单持久化最大分组数量
const (
	MaxOrderPersistGroupNum    = 5
	DefaultOrderPersistGroupId = "0"
)

// 撮合路由
const (
	urlDepthPrice        = "/wallet/v1/depth_price"           // 获取合约深度
	urlBurstCancel       = "/wallet/v1/burst_cancel"          // 爆仓撤单
	urlConditionCancel   = "/wallet/v1/cond_cancel"           // 条件撤单
	urlBurstClosePlace   = "/wallet/v1/close_place"           // 合约下单
	urlBothReducePlace   = "/wallet/v1/both_reduce_positions" // 合约多空抵消（不进深度）
	urlOrderList         = "/contract/v1/orders"              // 订单列表
	urlSpotLastPrice     = "/spot/v1/ticker"                  // 现货最新成交价
	urlContractLastPrice = "/wallet/v1/last_price"            // 合约最新成交价
	urlUnlockCancel      = "/wallet/v1/margin_unlock_cancel"  // 保证金解冻撤单
)

// Match Data 路由
const (
	urlMatchOrders   = "/contract/v1/orders" // 获取合约委托单列表
	urlContractDepth = "/contract/v1/depth"  // 币对深度
)
