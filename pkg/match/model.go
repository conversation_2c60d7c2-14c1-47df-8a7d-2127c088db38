package match

import (
	"futures-asset/internal/domain"

	"github.com/shopspring/decimal"
)

type DepthPriceParams struct {
	AccountType string `json:"accountType"`
	Base        string `json:"base"`
	Quote       string `json:"quote"`
	Depth       int64  `json:"depth"`
}

type CancelPositionOrderParams struct {
	UID         string `json:"uid"`
	AccountType string `json:"accountType"`
	Base        string `json:"base"`
	Quote       string `json:"quote"`
	MarginMode  int    `json:"marginMode"`
}

type ClosePositionParams struct {
	PositionParams
	UID             string   `json:"uid"`             // 用户ID
	AccountType     string   `json:"accountType"`     // 账户类型
	Base            string   `json:"base"`            // 交易币
	Quote           string   `json:"quote"`           // 计价币
	Leverage        int      `json:"leverage"`        // 杠杆倍数
	Platform        string   `json:"platform"`        // 平台来源
	LiquidationType int      `json:"liquidationType"` // 强平类型(爆仓或减仓)
	MarginMode      int      `json:"marginMode"`      // 保证金模式
	PositionMode    int      `json:"position_mode"`
	IsMixMargin     int      `json:"isMixMargin"`
	TimeInForce     string   `json:"timeInForce"`
	Depth           int      `json:"depth"`      // 深度
	BurstId         string   `json:"burstId"`    // 爆仓ID
	BurstTime       int64    `json:"burstTime"`  // 爆仓时间
	AwardOpIds      []string `json:"AwardOpIds"` // 用户奖励操作id
}

type PositionParams struct {
	Side         int             `json:"side"`      // 委托方向 buy-买 sell-卖
	PosSide      int             `json:"pos_side"`  // 多空 (1:多 2:空)
	OrderType    int             `json:"orderType"` // 委托类型
	IsLimitOrder int             `json:"isLimitOrder"`
	Price        decimal.Decimal `json:"price"`        // 委托价格
	Amount       decimal.Decimal `json:"amount"`       // 委托数量(市价买为交易额)
	TriggerPrice decimal.Decimal `json:"triggerPrice"` // 触发价格
}

type ReducePositionsParams struct {
	UID                 string   `json:"uid"`
	UserSide            int      `json:"userSide"`            // 委托方向 buy-买 sell-卖
	UserLeverage        int      `json:"userLeverage"`        // 杠杆倍数
	UserMarginMode      int      `json:"userMarginMode"`      // 用户保证金模式
	UserLiquidationType int      `json:"userLiquidationType"` // 用户强平类型
	UserHoldMode        int      `json:"userHoldMode"`
	UserIsMixMargin     int      `json:"userIsMixMargin"`
	UserTimeInForce     string   `json:"userTimeInForce"`
	UserAwardOpIds      []string `json:"UserAwardOpIds"` // 用户奖励操作id

	TargetUserId              string   `json:"targetUserId"`
	TargetUserLeverage        int      `json:"targetUserLeverage"`        // 杠杆倍数
	TargetUserMarginMode      int      `json:"targetUserMarginMode"`      // 被减仓用户保证金模式
	TargetUserLiquidationType int      `json:"targetUserLiquidationType"` // 被减仓用户强平类型
	TargetUserHoldMode        int      `json:"targetUserHoldMode"`
	TargetUserIsMixMargin     int      `json:"targetUserIsMixMargin"`
	TargetUserTimeInForce     string   `json:"targetUserTimeInForce"`
	TargetUserAwardOpIds      []string `json:"targetUserAwardOpIds"` // 被减仓用户奖励操作id

	AccountType string          `json:"accountType"`
	Base        string          `json:"base"`
	Quote       string          `json:"quote"`
	Price       decimal.Decimal `json:"price"`     // 平仓价格
	Amount      decimal.Decimal `json:"amount"`    // 平仓数量
	Platform    string          `json:"platform"`  // 平台来源
	BurstId     string          `json:"burstId"`   // 爆仓ID
	BurstTime   int64           `json:"burstTime"` // 爆仓时间
}

type BaseResponse struct {
	Code domain.Code `json:"code"`
	Msg  string      `json:"msg"`
}

type DepthPriceResponse struct {
	BaseResponse
	Data struct {
		BuyPrice  decimal.Decimal `json:"buyPrice"`
		SellPrice decimal.Decimal `json:"sellPrice"`
	} `json:"data"`
}

type ClosePositionResponse struct {
	BaseResponse
	Data struct {
		OrderId string `json:"orderId"`
	} `json:"data"`
}

type Ticker struct {
	PairName  string          `json:"symbol"`
	Gain      decimal.Decimal `json:"gain"`
	Open      decimal.Decimal `json:"open"`
	Low       decimal.Decimal `json:"low"`
	High      decimal.Decimal `json:"high"`
	Close     decimal.Decimal `json:"close"`
	Amount    decimal.Decimal `json:"amount"`
	Volume    decimal.Decimal `json:"volume"`
	BuyFirst  decimal.Decimal `json:"bid"`
	SellFirst decimal.Decimal `json:"ask"`
}

type LastPrice struct {
	LastPrice decimal.Decimal `json:"lastPrice"` // 最新成交价
}

type ContractLastPriceParams struct {
	AccountType string `json:"accountType"`
	Base        string `json:"base"`
	Quote       string `json:"quote"`
}

type SpotPriceResponse struct {
	BaseResponse
	Data Ticker `json:"data"`
}

type ContractPriceResponse struct {
	BaseResponse
	Data LastPrice `json:"data"`
}

type ClosePlaceParam struct {
	UID         string            `json:"uid"`
	AccountType string            `json:"accountType"` // 账户类型
	Base        string            `json:"base"`        // 合约交易币
	Quote       string            `json:"quote"`       // 合约计价币
	Leverage    int               `json:"leverage"`    // 杠杆倍数
	Platform    string            `json:"platform"`    // 平台来源
	MarginMode  domain.MarginMode `json:"marginMode"`  // 保证金模式 1-全仓 2-逐仓
	Side        int               `json:"side"`        // 委托方向 buy-买 sell-卖
	Offset      int               `json:"offset"`      // 开平方向 1-开 2-平
	OrderType   int               `json:"orderType"`   // 委托类型 limit-限价 market-市价 stop-limit-止盈止损
	Amount      decimal.Decimal   `json:"amount"`      // 委托数量(市价买为交易额)
	Depth       int64             `json:"depth"`       // 深度档位
}
