package match

// import (
// 	"bytes"
// 	"encoding/json"
// 	"errors"
// 	"fmt"
// 	"io/ioutil"
// 	"log"
// 	"net/http"
// 	"strings"
// 	"time"

// 	"futures-asset/conf"
// 	"futures-asset/internal/domain"
// 	"futures-asset/pkg/httplib"
// 	"futures-asset/util"

// 	"github.com/shopspring/decimal"
// 	"github.com/sirupsen/logrus"
// 	"github.com/tidwall/gjson"
// )

// type match struct {
// }

// var Service *match

// func (*match) ClosePlace(_params *ClosePlaceParam) {
// 	_params.Base = strings.ToLower(_params.Base)
// 	_params.Quote = strings.ToLower(_params.Quote)
// 	placeBytes, _ := json.Marshal(_params)
// 	url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["contractengine"], urlUnlockCancel)
// 	resp, err := http.Post(url, "application/json", bytes.NewBuffer(placeBytes))
// 	if err != nil {
// 		logrus.Error(fmt.Sprintf("%s post err:%+v,_params:%+v", urlUnlockCancel, err, _params))
// 		return
// 	}
// 	logrus.Infof("call contract-engine close place returned %+v _params:%+v", resp, _params)
// 	return
// }

// func (*match) ConditionCancel2(uid, base, quote string, posSide, side int32, marginMode int32, cancelType int32, skipInactive int32, haveTrial int32) (bool, int) {
// 	params := map[string]interface{}{}
// 	params["uid"] = uid
// 	params["base"] = base
// 	params["quote"] = quote
// 	params["pos_side"] = posSide           // 1-撤开仓单 2-撤平仓单
// 	params["side"] = side                  // 1-撤开买单 2-撤平卖单
// 	params["margin_mode"] = marginMode     // 保证金模式
// 	params["cancel_type"] = cancelType     // 撤单类型 1-用户撤单 2-系统撤单 3-运营撤单 4-爆仓撤单 5-减仓撤单
// 	params["skip_inactive"] = skipInactive // 是否跳过未激活的委托 0-不跳过 1-跳过
// 	params["have_trial"] = haveTrial

// 	url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["contractengine"], urlConditionCancel)
// 	reply, err := httplib.Post(url, params)
// 	if err != nil {
// 		logrus.Error("get swap orders err:" + err.Error())
// 		return false, 500
// 	}
// 	logrus.Infoln(fmt.Sprintf("ConditionCancel call engine %s returned %+v params: %+v", urlConditionCancel, string(reply), params))
// 	res := struct {
// 		Code int `json:"code"`
// 	}{}
// 	err = json.Unmarshal(reply, &res)
// 	if err != nil {
// 		logrus.Error("ConditionCancel swap orders Unmarshal err:" + err.Error())
// 		return false, 500
// 	}
// 	// 300032 盘口无订单
// 	if res.Code == 200 || res.Code == 300032 {
// 		return true, 200
// 	}
// 	return false, res.Code
// }

// // ConditionCancel 按照条件撤单
// func (m *match) ConditionCancel(uid, base, quote string, offset, side int32, marginMode int32, cancelType int32, skipInactive int32) (bool, int) {
// 	return m.ConditionCancel2(uid, base, quote, offset, side, marginMode, cancelType, skipInactive, 0)
// }

// // OrderListCount 订单列表
// func (*match) OrderListCount(uid, base, quote string, offset int) (bool, int) {
// 	params := map[string]interface{}{}
// 	params["uid"] = uid
// 	params["base"] = base
// 	params["quote"] = quote
// 	params["state"] = 7
// 	params["pageSize"] = 10
// 	params["pageNum"] = 1
// 	if offset > 0 {
// 		params["offset"] = offset
// 	}

// 	url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["contractdata"], urlOrderList)
// 	reply, err := httplib.Post(url, params)
// 	if err != nil {
// 		logrus.Error("gOrderList et swap orders POST err:" + err.Error())
// 		return false, -1
// 	}
// 	logrus.Infoln(fmt.Sprintf("OrderList call engine %s returned %+v params: %+v", urlOrderList, string(reply), params))
// 	res := struct {
// 		Code int `json:"code"`
// 		Data struct {
// 			Total int `json:"total"`
// 		} `json:"data"`
// 	}{}
// 	err = json.Unmarshal(reply, &res)
// 	if err != nil {
// 		logrus.Error("OrderList swap orders Unmarshal err:" + err.Error())
// 		return false, -1
// 	}
// 	if res.Code == 200 {
// 		return true, res.Data.Total
// 	}
// 	return false, -1
// }

// // 发送抵扣金
// func (m *match) DeliverAwardHandler(uid, awardId, symbol string, amount decimal.Decimal) (err error) {
// 	if awardId == "" {
// 		awardId = util.GenerateId()
// 	}

// 	now := time.Now().Unix()
// 	params := map[string]interface{}{}
// 	params["uid"] = uid
// 	params["orderId"] = awardId
// 	params["validitySt"] = now - 1
// 	params["validityEt"] = now + 86400*3650 + 1
// 	params["awardAmount"] = amount.String()
// 	params["changeWay"] = domain.DEDUCT_CHANGE_WAY_TRIAL2DEDUCT
// 	params["symbol"] = symbol

// 	fmt.Println("=====deliver award params:", params)
// 	url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["contractengine"], "/contract/v1/deliver_award")
// 	reply, err := httplib.Post(url, params)
// 	if err != nil {
// 		logrus.Error("gOrderList et swap orders POST err:" + err.Error())
// 		return err
// 	}
// 	res := gjson.ParseBytes(reply)
// 	if code := res.Get("code").Int(); code != http.StatusOK {
// 		msg := res.Get("msg").String()
// 		logrus.Error("DeliverAwardHandler error code: %v, msg: %v", code, msg)
// 		return fmt.Errorf("%v-%v", code, msg)
// 	}
// 	return nil
// }
