package repository

import (
	"context"

	"futures-asset/internal/domain/repository"

	"github.com/redis/go-redis/v9"
	"go.uber.org/dig"
)

type MemberRepositoryParam struct {
	dig.In

	RDB *redis.ClusterClient `name:"redis-cluster"`
}

type memberRepository struct {
	rdb *redis.ClusterClient
}

// TODO 实现所有函数逻辑
func NewMemberRepository(param MemberRepositoryParam) repository.MemberRepository {
	return &memberRepository{
		rdb: param.RDB,
	}
}

// GetUserLevelRate implements repository.MemberRepository.
func (m *memberRepository) GetUserLevelRate(ctx context.Context, uid string) (*repository.LevelRate, error) {
	reply := new(repository.LevelRate)

	return reply, nil
}

// GetUserBasic implements repository.MemberRepository.
func (m *memberRepository) GetUserBasic(ctx context.Context, uid string) (*repository.UserBasic, error) {
	reply := &repository.UserBasic{}

	return reply, nil
}

// GetUserLevel implements repository.MemberRepository.
func (m *memberRepository) GetUserLevel(ctx context.Context, uid string) (*repository.UserLevelData, error) {
	reply := &repository.UserLevelData{}

	return reply, nil
}

// HasAgree implements repository.MemberRepository.
func (m *memberRepository) HasAgree(ctx context.Context, uid string) repository.HasAgreeResp {
	reply := repository.HasAgreeResp{}

	return reply
}

// RobotList implements repository.MemberRepository.
func (m *memberRepository) RobotList(ctx context.Context) []repository.UserData {
	reply := []repository.UserData{}

	return reply
}
