package repository

import (
	"context"
	"strings"

	"futures-asset/internal/domain/repository"

	"github.com/shopspring/decimal"
	"go.uber.org/dig"
)

type FuturesEngineRepositoryParam struct {
	dig.In
	// 这里不需要任何特殊依赖
}

type futuresEngineRepository struct {
	// 这里可以添加需要的依赖，如数据库连接等
}

func NewFuturesEngineRepository(param FuturesEngineRepositoryParam) repository.FuturesEngineRepository {
	return &futuresEngineRepository{}
}

// GetDepthFirstPrice TODO 获取深度卖一买一价格
func (f *futuresEngineRepository) GetDepthFirstPrice(ctx context.Context, symbol string) (decimal.Decimal, decimal.Decimal, error) {
	return decimal.Zero, decimal.Zero, nil
}

// BothReducePositions TODO
func (f *futuresEngineRepository) BothReducePositions(ctx context.Context, params *repository.ReducePositionsParams) (*repository.BaseReply, error) {
	reduceData := new(repository.BaseReply)

	return reduceData, nil
}

// GetContractLastPrice 获取合约最新成交价
//
//	Params:
//	  base string: 交易币
//	  quote string: 计价币
//	Return:
//	  price decimal.Decimal: 最新成交价
func (f *futuresEngineRepository) GetContractLastPrice(ctx context.Context, base, quote string) (price decimal.Decimal, err error) {
	base = strings.ToLower(base)
	quote = strings.ToLower(quote)

	return decimal.Zero, nil
}

// ClosePositions implements repository.FuturesEngineRepository.
func (f *futuresEngineRepository) ClosePositions(ctx context.Context, params *repository.ClosePositionParams) (*repository.ClosePositionReply, error) {
	panic("unimplemented")
}

// GetSpotLastPrice 获取现货最新成交价
func (f *futuresEngineRepository) GetSpotLastPrice(ctx context.Context, base string, quote string) (price decimal.Decimal, err error) {
	panic("unimplemented")
}

// SwapDepth implements repository.FuturesEngineRepository.
func (f *futuresEngineRepository) SwapDepth(ctx context.Context, symbol string) repository.DepthInfo {
	// params := map[string]interface{}{}
	// params["contractCode"] = strings.ToUpper(contractCode)
	// params["step"] = "step0"

	// url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["contractdata"], urlContractDepth)
	// reply, err := httplib.Post(url, params)
	// if err != nil {
	// 	logrus.Error("get swap orders err:" + err.Error())
	// 	return DepthInfo{}
	// }

	depth := repository.DepthInfo{}

	return depth
}

// SwapOrders implements repository.FuturesEngineRepository.
func (f *futuresEngineRepository) SwapOrders(ctx context.Context, uid string, symbol string) repository.SwapOrderPage {
	// 	params := map[string]interface{}{}
	// 	params["uid"] = uid
	// 	if len(contractCode) > 0 {
	// 		params["contractCode"] = strings.ToUpper(contractCode)
	// 	}
	// 	params["accountType"] = AccountTypeSwap
	// 	params["state"] = 101
	// 	params["pageNum"] = 1
	// 	params["pageSize"] = 500

	// 	url := fmt.Sprintf("http://%s%s", conf.Conf.Biz.Services["contractdata"], urlMatchOrders)
	// 	reply, err := httplib.Post(url, params)
	// 	if err != nil {
	// 		logrus.Error("get swap orders err:" + err.Error())
	// 		return SwapOrderPage{}
	// 	}

	orders := repository.SwapOrderPage{}

	return orders
}
