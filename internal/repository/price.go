package repository

import (
	"context"
	"strings"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/util"

	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	"gorm.io/gorm"
)

type PriceRepositoryParam struct {
	dig.In

	DB  *gorm.DB             `name:"db"`
	RDB *redis.ClusterClient `name:"redis-cluster"`
}

type priceRepository struct {
	db  *gorm.DB
	rdb *redis.ClusterClient

	markPriceCache  cmap.ConcurrentMap[string, decimal.Decimal]
	indexPriceCache cmap.ConcurrentMap[string, decimal.Decimal]
	lastPriceCache  cmap.ConcurrentMap[string, decimal.Decimal]
}

func NewPriceRepository(param PriceRepositoryParam) repository.PriceRepository {
	return &priceRepository{
		db:  param.DB,
		rdb: param.RDB,

		markPriceCache:  cmap.New[decimal.Decimal](),
		indexPriceCache: cmap.New[decimal.Decimal](),
		lastPriceCache:  cmap.New[decimal.Decimal](),
	}
}

// GetIndexAndMarkPrice implements repository.PriceRepository.
func (repo *priceRepository) GetIndexAndMarkPrice(ctx context.Context, symbol string) (decimal.Decimal, decimal.Decimal) {
	mark, _ := repo.markPriceCache.Get(symbol)
	index, _ := repo.indexPriceCache.Get(symbol)

	return mark, index
}

// SetIndexPrice implements repository.PriceRepository.
func (repo *priceRepository) SetIndexPrice(ctx context.Context, symbol string, price decimal.Decimal) {
	repo.indexPriceCache.Set(strings.ToUpper(symbol), price)
}

// SetLastPrice implements repository.PriceRepository.
func (repo *priceRepository) SetLastPrice(ctx context.Context, symbol string, price decimal.Decimal) {
	repo.lastPriceCache.Set(strings.ToUpper(symbol), price)
}

// SetMarkPrice implements repository.PriceRepository.
func (repo *priceRepository) SetMarkPrice(ctx context.Context, symbol string, price decimal.Decimal) {
	repo.markPriceCache.Set(strings.ToUpper(symbol), price)
}

func (repo *priceRepository) InitMarkPrice(ctx context.Context) error {
	prices, err := repo.rdb.HGetAll(ctx, domain.AssetPrefix.Key("prices:mark")).Result()
	if err != nil {
		return err
	}

	for symbol, item := range prices {
		price, err := decimal.NewFromString(item)
		if err != nil {
			continue
		}
		repo.markPriceCache.Set(strings.ToUpper(symbol), price)
	}

	return nil
}

func (repo *priceRepository) GetAllMarkPrice(ctx context.Context) cmap.ConcurrentMap[string, decimal.Decimal] {
	return repo.markPriceCache
}

func (repo *priceRepository) GetMarkPrice(ctx context.Context, symbol string) decimal.Decimal {
	if repo.markPriceCache.Count() == 0 {
		err := repo.InitMarkPrice(ctx)
		if err != nil {
			return decimal.Zero
		}
	}
	mark, ok := repo.markPriceCache.Get(strings.ToUpper(symbol))
	if ok && !mark.IsZero() {
		return mark
	}

	return decimal.Zero
}

func (repo *priceRepository) InitIndexPrice(ctx context.Context) error {
	prices, err := repo.rdb.HGetAll(ctx, domain.AssetPrefix.Key("prices:index")).Result()
	if err != nil {
		return err
	}

	for symbol, item := range prices {
		price, err := decimal.NewFromString(item)
		if err != nil {
			continue
		}
		repo.indexPriceCache.Set(strings.ToUpper(symbol), price)
	}

	return nil
}

// GetAllIndexPrice implements repository.PriceRepository.
func (repo *priceRepository) GetAllIndexPrice(ctx context.Context) cmap.ConcurrentMap[string, decimal.Decimal] {
	return repo.indexPriceCache
}

// GetIndexPrice implements repository.PriceRepository.
func (repo *priceRepository) GetIndexPrice(ctx context.Context, symbol string) decimal.Decimal {
	if repo.indexPriceCache.Count() == 0 {
		err := repo.InitIndexPrice(ctx)
		if err != nil {
			return decimal.Zero
		}
	}

	index, ok := repo.indexPriceCache.Get(strings.ToUpper(symbol))
	if ok && !index.IsZero() {
		return index
	}

	mixIndexPriceKey := domain.GetPlatformIndexPriceRedisKey()
	mixIndexPriceStr, err := repo.rdb.HGet(ctx, mixIndexPriceKey, symbol).Result()
	if err != nil && err != redis.Nil {
		logrus.Errorln(symbol, "IndexPrice HGet", mixIndexPriceKey, "error:", err)
	}
	if len(mixIndexPriceStr) > 0 {
		index, _ = decimal.NewFromString(mixIndexPriceStr)
	}

	if index.IsZero() {
		// TODO 获取现货最新成交价格
		// spotLastPrice, err := match.Service.GetSpotLastPrice(symbol)
		// if err != nil {
		// 	logrus.Error(fmt.Sprintln("GetSpotLastPrice error:", err, symbol))
		// 	return decimal.Zero
		// }
		// return spotLastPrice
	}
	repo.SetIndexPrice(ctx, symbol, index)

	return index
}

func (repo *priceRepository) InitLastPrice(ctx context.Context) error {
	prices, err := repo.rdb.HGetAll(ctx, domain.AssetPrefix.Key("prices:last")).Result()
	if err != nil {
		return err
	}

	for symbol, item := range prices {
		price, err := decimal.NewFromString(item)
		if err != nil {
			continue
		}
		repo.lastPriceCache.Set(strings.ToUpper(symbol), price)
	}

	return nil
}

// GetAllLastPrice implements repository.PriceRepository.
func (repo *priceRepository) GetAllLastPrice(ctx context.Context) cmap.ConcurrentMap[string, decimal.Decimal] {
	return repo.lastPriceCache
}

// GetLastPrice implements repository.PriceRepository.
func (repo *priceRepository) GetLastPrice(ctx context.Context, symbol string) decimal.Decimal {
	if repo.lastPriceCache.Count() == 0 {
		err := repo.InitLastPrice(ctx)
		if err != nil {
			return decimal.Zero
		}
	}

	last, exist := repo.lastPriceCache.Get(strings.ToUpper(symbol))
	if exist && !last.IsZero() {
		return last
	}
	lastStr, err := repo.rdb.HGet(ctx, domain.GetContractLastPriceRedisKey(), strings.ToUpper(symbol)).Result()
	if err != nil && err != redis.Nil {
		return decimal.Zero
	}

	lastPrice, err := decimal.NewFromString(lastStr)
	if err != nil {
		return decimal.Zero
	}
	repo.lastPriceCache.Set(strings.ToUpper(symbol), lastPrice)

	return lastPrice
}

// SpotURate 通过 spot price map 获取币种现货最新成交价
func (repo *priceRepository) SpotURate(ctx context.Context, currency string) decimal.Decimal {
	if repo.indexPriceCache.Count() == 0 {
		err := repo.InitIndexPrice(ctx)
		if err != nil {
			return decimal.Zero
		}
	}
	if strings.ToUpper(currency) == strings.ToUpper(domain.CurrencyUSDT) {
		// 币种是USDT, 直接返回1
		return decimal.NewFromInt(1)
	}

	// 组合 symbol 获取对应价格
	symbol := util.ContractCode(currency, domain.CurrencyUSDT)
	if price, ok := repo.indexPriceCache.Get(strings.ToUpper(symbol)); ok {
		return price
	}

	// 币种没有对 USDT 的汇率， 需要先折其他币对中间做一次转换
	for _, item := range []string{domain.CurrencyBTC, domain.CurrencyETH} {
		symbol := util.ContractCode(currency, item)
		basePrice, ok := repo.indexPriceCache.Get(strings.ToUpper(symbol))
		if !ok || basePrice.IsZero() {
			continue
		}

		midPair := util.ContractCode(item, domain.CurrencyUSDT)
		midPrice, ok := repo.indexPriceCache.Get(strings.ToUpper(midPair))
		if !ok || midPrice.IsZero() {
			continue
		}

		return basePrice.Mul(midPrice)
	}

	return decimal.Zero
}

// SpotRate 获取两个币种之间的汇率，需要先转U 再获取
func (repo *priceRepository) SpotRate(ctx context.Context, base, quote string) decimal.Decimal {
	if base == quote {
		return decimal.NewFromInt(1)
	}

	if repo.indexPriceCache.Count() == 0 {
		err := repo.InitIndexPrice(ctx)
		if err != nil {
			return decimal.Zero
		}
	}
	symbol := util.ContractCode(base, quote)
	if price, ok := repo.indexPriceCache.Get(strings.ToUpper(symbol)); ok {
		return price
	}

	// 没有相关的现货币对， 需要先获取base 对U 然后 quote对u，最后两个数除一下
	baseURate := repo.SpotURate(ctx, base)
	if baseURate.IsZero() {
		return decimal.Zero
	}
	quoteURate := repo.SpotURate(ctx, quote)
	if quoteURate.IsZero() {
		return decimal.Zero
	}

	return baseURate.Div(quoteURate).Truncate(domain.PricePrecision)
}
