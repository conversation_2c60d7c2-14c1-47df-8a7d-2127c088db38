package repository

import (
	"fmt"
	"regexp"
	"testing"

	"futures-asset/internal/domain"

	"github.com/shopspring/decimal"
)

type DepthInfo struct {
	Time int        `json:"time"`
	Bids [][]string `json:"bids"`
	Asks [][]string `json:"asks"`
}

func TestFundingRate(t *testing.T) {
	pIndexList := []string{"-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035", "-0.000231244393609035"}
	totalCount := len(pIndexList)
	pCount := len(pIndexList)
	if pCount > domain.DefaultBaseNum {
		pCount = domain.DefaultBaseNum
	}
	pAvg := decimal.Zero
	{
		pSum := decimal.Zero
		pRight := decimal.Zero
		for i := 0; i < pCount; i++ {
			right := decimal.NewFromInt(int64(i + 1))
			pIndex, _ := decimal.NewFromString(pIndexList[i])
			pSum = pSum.Add(pIndex.Mul(right))
			pRight = pRight.Add(right)
		}
		fmt.Println("premium index sum: ", pSum.String(), "divisor sum: ", pRight.String())
		if totalCount > 0 {
			pAvg = pSum.Div(pRight)
		}
	}

	interestRate := decimal.NewFromFloat(0.0001)
	diff := interestRate.Sub(pAvg)
	min := decimal.NewFromFloat(-0.0005)
	max := decimal.NewFromFloat(0.0005)
	clamp := Clamp(min, max, diff)
	fmt.Println(fmt.Sprintf("interest rate: %s\navg premium index: %s\ni-P: %s", interestRate.String(), pAvg.String(), diff.String()))

	fundRate := pAvg.Add(clamp)
	fmt.Println("F: ", fundRate.String())
}

func TestPIndex(t *testing.T) {
	// 冲击保证金额 （IGM）= 200 USDT / 最高杠杆档位对应的初始保证金比率
	// 冲击保证金额 （IGM）= 200 USDT * 最高杠杆倍数 = 200 * 125 = 25000
	// TODO 边界处理
	maintenanceRate := decimal.NewFromFloat(0.008)
	igm := decimal.NewFromInt(200).Div(maintenanceRate)
	fmt.Println(fmt.Sprintf("IMN: %s", igm.String()))

	depth := DepthInfo{
		Asks: [][]string{
			{"279.67", "41.86"},
			{"279.68", "6.26"},
			{"279.69", "1.42"},
			{"279.7", "31.64"},
			{"279.71", "11.27"},
		},
		Bids: [][]string{
			{"279.66", "7.03"},
			{"279.63", "2.33"},
			{"279.62", "1.42"},
			{"279.61", "23.1"},
			{"279.6", "29.61"},
			{"279.59", "6.68"},
			{"279.57", "6.74"},
			{"279.56", "2.12"},
			{"279.55", "20.12"},
		},
	}

	buyIGMList := make([]decimal.Decimal, 0)
	buyVolumeTotal := decimal.Zero
	prevBuyVolume := decimal.Zero
	prevBuySize := decimal.Zero
	buyPrice := decimal.Zero

	sellIGMList := make([]decimal.Decimal, 0)
	sellVolumeTotal := decimal.Zero
	prevSellVolume := decimal.Zero
	prevSellSize := decimal.Zero
	sellPrice := decimal.Zero

	for _, dInfo := range depth.Asks {
		price, _ := decimal.NewFromString(dInfo[0])
		amount, _ := decimal.NewFromString(dInfo[1])
		volume := price.Mul(amount)

		sellIGMList = append(sellIGMList, price)
		sellVolumeTotal = sellVolumeTotal.Add(volume)

		if sellVolumeTotal.GreaterThan(igm) {
			sellPrice = price
			fmt.Println(fmt.Sprintf("sell volume total: %s prev gear sell volume: %s prev gear sell size: %s", sellVolumeTotal.String(), prevSellVolume.String(), prevSellSize.String()))
			break
		}
		prevSellVolume = prevSellVolume.Add(volume)
		prevSellSize = prevSellSize.Add(amount)
	}
	for _, dInfo := range depth.Bids {
		price, _ := decimal.NewFromString(dInfo[0])
		amount, _ := decimal.NewFromString(dInfo[1])
		volume := price.Mul(amount)

		buyIGMList = append(buyIGMList, price)
		buyVolumeTotal = buyVolumeTotal.Add(volume)

		if buyVolumeTotal.GreaterThan(igm) {
			buyPrice = price
			fmt.Println(fmt.Sprintf("buy volume total: %s prev gear buy volume: %s prev gear buy size: %s", buyVolumeTotal.String(), prevBuyVolume.String(), prevBuySize.String()))
			break
		}
		prevBuyVolume = prevBuyVolume.Add(volume)
		prevBuySize = prevBuySize.Add(amount)
	}

	buyIGM := decimal.Zero
	if len(buyIGMList) == 1 {
		buyIGM = buyIGMList[0]
	} else if len(buyIGMList) > 1 {
		buyIGM = decimal.Avg(buyIGMList[0], buyIGMList[1:]...)
		// 按照实际溢价指数公式计算 IMN / [(IMN-multiplier * ∑px-1 * qx-1)/px+multiplier * ∑qx-1]
		if !buyPrice.IsZero() {
			molecular := igm
			denominator := igm.Sub(prevBuyVolume).Div(buyPrice).Add(prevBuySize)
			buyIGM = molecular.Div(denominator)
		}
	}
	sellIGM := decimal.Zero
	if len(sellIGMList) == 1 {
		sellIGM = sellIGMList[0]
	} else if len(sellIGMList) > 1 {
		sellIGM = decimal.Avg(sellIGMList[0], sellIGMList[1:]...)
		// 按照实际溢价指数公式计算 IMN / [(IMN-multiplier * ∑px-1 * qx-1)/px+multiplier * ∑qx-1]
		if !sellPrice.IsZero() {
			molecular := igm
			denominator := igm.Sub(prevSellVolume).Div(sellPrice).Add(prevSellSize)
			sellIGM = molecular.Div(denominator)
		}
	}
	fmt.Println(fmt.Sprintf("impact ask price: %s impact bid price: %s", sellIGM.String(), buyIGM.String()))

	indexPrice := decimal.NewFromFloat(279.75)

	pIndex := decimal.Max(decimal.NewFromInt(0), buyIGM.Sub(indexPrice)).
		Sub(decimal.Max(decimal.NewFromInt(0), indexPrice.Sub(sellIGM))).
		Div(indexPrice)
	fmt.Println("Premium Index (P): ", pIndex.String())
}

func TestContractRegex(t *testing.T) {
	contract := "1000SHIB-USDT"
	pattern := regexp.MustCompile(`^\d+`)
	findString := pattern.FindString(contract)
	t.Log(findString)
	findString = pattern.FindString("SHIB-USDT200")
	t.Log(findString)
}

// func TestGetPremiumIndexList(t *testing.T) {
// 	var indexPriceWithScores = []redis.Z{
// 		// {
// 		// 	Score:  1713954660,
// 		// 	Member: "-1",
// 		// },
// 		// {
// 		// 	Score:  1716776640,
// 		// 	Member: "-0.0004399091261017",
// 		// },
// 		// {
// 		// 	Score:  1717235820,
// 		// 	Member: "-0.0010137174524466",
// 		// },
// 		// {
// 		// 	Score:  1717783560,
// 		// 	Member: "-0.0036791391171569",
// 		// },
// 		// {
// 		// 	Score:  1721053260,
// 		// 	Member: "0",
// 		// },
// 		// {
// 		// 	Score:  1721079425,
// 		// 	Member: "0.011",
// 		// },
// 		{
// 			Score:  1721083830,
// 			Member: "0.022",
// 		},
// 		{
// 			Score:  1721084430,
// 			Member: "0.033",
// 		},
// 		{
// 			Score:  1721086230,
// 			Member: "0.044",
// 		},
// 		{
// 			Score:  1721089830,
// 			Member: "0",
// 		},
// 	}

// 	pIndexList := GetPremiumIndexList(indexPriceWithScores)
// 	t.Logf("pair: %s pIndexList len: %d data: %+v", "", len(pIndexList), pIndexList)
// }

func TestFundingPIndex(t *testing.T) {
	// pIndexList := []string{"0", "-0.0008540773512256", "-0.0027504011173012", "-1"}
	// // 计算平均溢价指数
	// pAvg := decimal.Zero
	// totalCount := len(pIndexList)
	// pCount := len(pIndexList)
	// if pCount > cache.DefaultBaseNum {
	// 	pCount = cache.DefaultBaseNum
	// }
	// {
	// 	pSum := decimal.Zero
	// 	pRight := decimal.Zero
	// 	for i := 0; i < pCount; i++ {
	// 		right := decimal.NewFromInt(int64(i + 1))
	// 		pIndex, _ := decimal.NewFromString(pIndexList[i])
	// 		pSum = pSum.Add(pIndex.Mul(right))
	// 		pRight = pRight.Add(right)
	// 	}
	// 	// fmt.Println("premium index sum: ", pSum.String(), "divisor sum: ", pRight.String())
	// 	if totalCount > 0 {
	// 		pAvg = pSum.Div(pRight)
	// 	}
	// }
	// fmt.Println(pAvg.String())
}
