package repository

import (
	"context"
	"fmt"

	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"

	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	"gorm.io/gorm"
)

type ProfitLossRepositoryParam struct {
	dig.In

	DB *gorm.DB `name:"db"`
}

type profitLossRepository struct {
	db *gorm.DB `name:"db"`
}

// GetProfitLossData implements repository.ProfitLossRepository.
func (repo *profitLossRepository) GetProfitLossData(ctx context.Context, uid string, currency string, operateTime int64) (entity.ProfitLoss, error) {
	var profitLoss entity.ProfitLoss
	err := repo.db.Table(profitLoss.TableName()).Where("user_id = ? and currency = ? and operate_time = ?",
		uid, currency, operateTime).Find(&profitLoss).Error

	return profitLoss, err
}

// UpsertProfitLoss implements repository.ProfitLossRepository.
func (repo *profitLossRepository) UpsertProfitLoss(ctx context.Context, profitLoss *entity.ProfitLoss) (err error) {
	insertColumns := "`user_id`, `currency`, `net_in`,`profit_loss`,`operate_time`,`create_time`"
	updateColumns := "`net_in`=?,`profit_loss`=?,`update_time`=?"
	sql := fmt.Sprintf("INSERT INTO %s (%s)  VALUES (?,?,?,?,?,?) ON DUPLICATE KEY UPDATE %s;",
		profitLoss.TableName(), insertColumns, updateColumns)
	if err := repo.db.Exec(sql,
		profitLoss.UID, profitLoss.Currency, profitLoss.NetIn, profitLoss.ProfitLoss, profitLoss.OperateTime, profitLoss.CreateTime,
		profitLoss.NetIn, profitLoss.ProfitLoss, profitLoss.UpdateTime,
	).Error; err != nil {
		return err
	}

	return nil
}

func NewProfitLossRepository(param ProfitLossRepositoryParam) repository.ProfitLossRepository {
	return &profitLossRepository{
		db: param.DB,
	}
}

func (repo *profitLossRepository) GetPLRecord(ctx context.Context, _param *repository.ReqPLRecord) (repository.ResPLRecord, error) {
	records, err := repo.GetPLTrade(ctx, _param)
	if err != nil {
		logrus.Error(fmt.Sprintf("get profit loss data err:%+v, pram:%+v", err, _param))
	}
	recordDataMap := map[int64]int{}
	data := repository.ResPLRecord{}
	for _, record := range records {
		if dayIndex, ok := recordDataMap[record.OperateTime]; ok {
			data.ProfitLossRecords[dayIndex].NetIn = data.ProfitLossRecords[dayIndex].NetIn.Add(record.NetIn)
			data.ProfitLossRecords[dayIndex].TodayProfitLoss = data.ProfitLossRecords[dayIndex].TodayProfitLoss.Add(record.ProfitLoss)
		} else {
			dayData := repository.ProfitLossRecord{}
			dayData.DayTime = record.OperateTime
			dayData.NetIn = record.NetIn
			dayData.TodayProfitLoss = record.ProfitLoss
			data.ProfitLossRecords = append(data.ProfitLossRecords, dayData)
			recordDataMap[record.OperateTime] = len(data.ProfitLossRecords) - 1
		}
	}
	if len(data.ProfitLossRecords) == 0 {
		return data, nil
	}
	data.DaysAnalysis.DaysNum = len(data.ProfitLossRecords)

	// 当前余额 - 累计盈亏 = 开始日账户余额 + 净转入
	// 累计盈亏率 = 累计盈亏 / （当前余额 - 累计盈亏） *100%
	// 开始统计分析
	totalProfit := decimal.Zero
	for index := len(data.ProfitLossRecords) - 1; index >= 0; index-- {
		totalProfit = totalProfit.Add(data.ProfitLossRecords[index].TodayProfitLoss)
		data.ProfitLossRecords[index].CumulativeProfit = totalProfit

		if data.ProfitLossRecords[index].TodayProfitLoss.IsZero() {
			data.DaysAnalysis.EqualDays++
		} else if data.ProfitLossRecords[index].TodayProfitLoss.IsNegative() {
			data.DaysAnalysis.LossDays++
			data.DaysAnalysis.CumulativeLoss = data.DaysAnalysis.CumulativeLoss.Add(data.ProfitLossRecords[index].TodayProfitLoss)
		} else if data.ProfitLossRecords[index].TodayProfitLoss.IsPositive() {
			data.DaysAnalysis.ProfitDays++
			data.DaysAnalysis.CumulativeProfit = data.DaysAnalysis.CumulativeProfit.Add(data.ProfitLossRecords[index].TodayProfitLoss)
		}
	}
	data.DaysAnalysis.TotalProfitLoss = totalProfit
	if data.DaysAnalysis.ProfitDays != 0 {
		data.DaysAnalysis.AverageProfit = data.DaysAnalysis.CumulativeProfit.Div(decimal.NewFromInt(data.DaysAnalysis.ProfitDays))
	}
	if data.DaysAnalysis.LossDays != 0 {
		data.DaysAnalysis.AverageLoss = data.DaysAnalysis.CumulativeLoss.Div(decimal.NewFromInt(data.DaysAnalysis.LossDays))
	}

	data.TotalProfitLoss = repo.GetTotalProfit(ctx, _param.UID)

	return data, nil
}

func (repo *profitLossRepository) GetPLTrade(ctx context.Context, _param *repository.ReqPLRecord) ([]entity.ProfitLoss, error) {
	var records []entity.ProfitLoss
	sql := repo.db.Table(new(entity.ProfitLoss).TableName()).Where("user_id = ?", _param.UID)
	if _param.StartTime != 0 {
		sql = sql.Where("operate_time >= ?", _param.StartTime)
	}
	if _param.EndTime != 0 {
		sql = sql.Where("operate_time <= ?", _param.EndTime)
	}
	sql = sql.Order("operate_time DESC")
	err := sql.Find(&records).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return records, nil
	}

	return records, err
}

func (repo *profitLossRepository) GetTotalProfit(ctx context.Context, uid string) decimal.Decimal {
	var dbTotalProfit []entity.TotalProfit
	totalProfit := decimal.Zero

	sql := repo.db.Table(new(entity.TotalProfit).TableName()).Where("user_id = ?", uid)
	err := sql.Find(&dbTotalProfit).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		for _, profit := range dbTotalProfit {
			totalProfit = totalProfit.Add(profit.TotalProfit)
		}

		return totalProfit
	}

	return totalProfit
}

func (repo *profitLossRepository) GetTotalSubsidy(ctx context.Context) (entity.TotalProfit, error) {
	profit := entity.TotalProfit{}
	err := repo.TotalSubsidy(ctx)

	return profit, err
}

// GetTotalProfitData implements repository.ProfitLossRepository.
func (repo *profitLossRepository) GetTotalProfitData(ctx context.Context, uid, currency string) (entity.TotalProfit, error) {
	var totalProfit entity.TotalProfit
	err := repo.db.Table(totalProfit.TableName()).Where("user_id = ? and currency = ?", uid, currency).Find(&totalProfit).Error

	return totalProfit, err
}

// TotalSubsidy implements repository.ProfitLossRepository.
func (repo *profitLossRepository) TotalSubsidy(ctx context.Context) (err error) {
	profit := entity.TotalProfit{}
	err = repo.db.Table(profit.TableName()).Select("sum(subsidy) as subsidy").Find(&profit).Error

	return err
}

// UpsertTotalProfit implements repository.ProfitLossRepository.
func (repo *profitLossRepository) UpsertTotalProfit(ctx context.Context, totalProfit *entity.TotalProfit) (err error) {
	insertColumns := "`user_id`,`currency`,`total_profit`, `subsidy`, `create_time`"

	updateColumns := "`total_profit`=?, `subsidy` = ?, `update_time`=?"
	sql := fmt.Sprintf("INSERT INTO %s (%s)  VALUES (?,?,?,?,?) ON DUPLICATE KEY UPDATE %s;",
		totalProfit.TableName(), insertColumns, updateColumns)
	if err := repo.db.Exec(sql,
		totalProfit.UID, totalProfit.Currency, totalProfit.TotalProfit, totalProfit.Subsidy, totalProfit.CreateTime,
		totalProfit.TotalProfit, totalProfit.Subsidy, totalProfit.UpdateTime,
	).Error; err != nil {
		return err
	}

	return nil
}
