package repository

import (
	"context"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/util"

	"gorm.io/gorm"

	"go.uber.org/dig"
)

type BillParam struct {
	dig.In

	DB *gorm.DB `name:"db"`
}

type billRepository struct {
	db *gorm.DB
}

func NewBillRepository(param BillParam) repository.BillRepository {
	return &billRepository{
		db: param.DB,
	}
}

// Insert implements repository.BillAssetRepository.
func (repo *billRepository) InsertAsset(ctx context.Context, billAsset *entity.BillAsset) (err error) {
	tableName := billAsset.TableName()
	if ok := util.TableIsExit(tableName); !ok {
		if !repo.db.Migrator().HasTable(tableName) {
			err := repo.db.Migrator().CreateTable(billAsset)
			if err != nil {
				return err
			}
			util.SetNewTableName(tableName)
		}
	}

	if err := repo.db.Table(tableName).Create(billAsset).Error; err != nil {
		return err
	}

	return nil
}

// InsertRobot implements repository.BillAssetRepository.
func (repo *billRepository) InsertRobotAsset(ctx context.Context, billAsset *entity.BillAsset) (err error) {
	tableName := billAsset.RobotTableName()
	if ok := util.TableIsExit(tableName); !ok {
		if !repo.db.Migrator().HasTable(tableName) {
			err := repo.db.Migrator().CreateTable(billAsset)
			if err != nil {
				return err
			}
			util.SetNewTableName(tableName)
		}
	}

	if err := repo.db.Table(tableName).Create(billAsset).Error; err != nil {
		return err
	}

	return nil
}

// InsertFundingFee implements repository.BillRepository.
func (repo *billRepository) InsertFundingFee(ctx context.Context, billFundingFee *entity.LogFundingFee) (err error) {
	return repo.db.Table(billFundingFee.TableName()).Create(billFundingFee).Error
}

// InsertFundingRate implements repository.BillRepository.
func (repo *billRepository) InsertFundingRate(ctx context.Context, billFundingRate *entity.LogFundingRate) (err error) {
	return repo.db.Table(billFundingRate.TableName()).Create(billFundingRate).Error
}

// InsertOption implements repository.BillRepository.
func (repo *billRepository) InsertOption(ctx context.Context, billOption *entity.BillOption) (err error) {
	tableName := billOption.TableName()
	if billOption.OptionType == domain.OptionTypeDemo {
		tableName = billOption.DemoTableName()
	}
	if ok := util.TableIsExit(tableName); !ok {
		if !repo.db.Migrator().HasTable(tableName) {
			err := repo.db.Migrator().CreateTable(billOption)
			if err != nil {
				return err
			}
			util.SetNewTableName(tableName)
		}
	}

	if err := repo.db.Table(tableName).Create(billOption).Error; err != nil {
		return err
	}

	return nil
}

// InsertBurst implements repository.BillRepository.
func (repo *billRepository) InsertBurst(ctx context.Context, billBurst *entity.LogBurstSwap) (err error) {
	tableName := billBurst.TableName()
	if ok := util.TableIsExit(tableName); !ok {
		if !repo.db.Migrator().HasTable(tableName) {
			err := repo.db.Migrator().CreateTable(billBurst)
			if err != nil {
				return err
			}
			util.SetNewTableName(tableName)
		}
	}

	if err := repo.db.Table(tableName).Create(billBurst).Error; err != nil {
		return err
	}

	return nil
}

// InsertRivalBurst implements repository.BillRepository.
func (repo *billRepository) InsertRivalBurst(ctx context.Context, billRivalBurst *entity.RivalBurst) (err error) {
	tableName := billRivalBurst.TableName()
	if !repo.db.Migrator().HasTable(tableName) {
		err := repo.db.Migrator().CreateTable(billRivalBurst)
		if err != nil {
			return err
		}
	}

	if err := repo.db.Table(tableName).Create(billRivalBurst).Error; err != nil {
		return err
	}

	return nil
}
