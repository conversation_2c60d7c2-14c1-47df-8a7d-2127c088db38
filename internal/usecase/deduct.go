package usecase

// import (
// 	"futures-asset/configs"
// 	"futures-asset/internal/domain/repository"
// 	"futures-asset/internal/domain/usecase"

// 	"go.uber.org/dig"
// )

// type DeductUseCaseParam struct {
// 	dig.In

// 	config     *cfg.Config[configs.Config] `name:"config"`
// 	DeductRepo repository.DeductRepository
// }

// type DeductUseCase struct {
// 	config     *cfg.Config[configs.Config]
// 	deductRepo repository.DeductRepository
// }

// func NewDeductUseCase(param DeductUseCaseParam) usecase.DeductUseCase {
// 	return &DeductUseCase{
// 		config:     param.config,
// 		deductRepo: param.DeductRepo,
// 	}
// }
