package usecase

import (
	"context"

	"futures-asset/configs"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"

	"github.com/shopspring/decimal"
	"go.uber.org/dig"
	cfg "yt.com/backend/common.git/config"
)

// TrialUseCase 资产用例实现
type ProfitLossUseCase struct {
	config *cfg.Config[configs.Config]

	profitLossRepo repository.ProfitLossRepository
}

// TrialAssetUseCaseParam 资产用例参数
type ProfitLossUseCaseParam struct {
	dig.In

	Config         *cfg.Config[configs.Config] `name:"config"`
	ProfitLossRepo repository.ProfitLossRepository
}

// NewTrialAssetUseCase 创建资产用例实例
func NewProfitLossUseCase(param ProfitLossUseCaseParam) usecase.ProfitLossUseCase {
	return &ProfitLossUseCase{
		config:         param.Config,
		profitLossRepo: param.ProfitLossRepo,
	}
}

// GetPLRecord implements usecase.ProfitLossUseCase.
func (use *ProfitLossUseCase) GetPLRecord(ctx context.Context, req *repository.ReqPLRecord) (repository.ResPLRecord, error) {
	return use.profitLossRepo.GetPLRecord(ctx, req)
}

// GetPLTrade implements usecase.ProfitLossUseCase.
func (use *ProfitLossUseCase) GetPLTrade(ctx context.Context, req *repository.ReqPLRecord) ([]entity.ProfitLoss, error) {
	return use.profitLossRepo.GetPLTrade(ctx, req)
}

// GetTotalProfit implements usecase.ProfitLossUseCase.
func (use *ProfitLossUseCase) GetTotalProfit(ctx context.Context, uid string) decimal.Decimal {
	return use.profitLossRepo.GetTotalProfit(ctx, uid)
}

// GetTotalSubsidy implements usecase.ProfitLossUseCase.
func (use *ProfitLossUseCase) GetTotalSubsidy(ctx context.Context) (entity.TotalProfit, error) {
	return use.profitLossRepo.GetTotalSubsidy(ctx)
}
