package usecase

import (
	"context"
	"fmt"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	"futures-asset/internal/utils"
	"futures-asset/util"
	"time"

	"github.com/go-redsync/redsync/v4"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	futuresEnginePB "yt.com/backend/common.git/business/grpc/gen/futures/engine/v1"
)

// SettleUseCase 结算用例实现
type SettleUseCase struct {
	cacheRepo  repository.CacheRepository
	priceRepo  repository.PriceRepository
	positionUC usecase.PositionUseCase
	rs         *redsync.Redsync
}

// SettleUseCaseParam 结算用例参数
type SettleUseCaseParam struct {
	dig.In
	CacheRepo  repository.CacheRepository
	PriceRepo  repository.PriceRepository
	PositionUC usecase.PositionUseCase
	RS         *redsync.Redsync `name:"rs"`
}

// NewSettleUseCase 创建结算用例实例
func NewSettleUseCase(param SettleUseCaseParam) *SettleUseCase {
	return &SettleUseCase{
		cacheRepo:  param.CacheRepo,
		priceRepo:  param.PriceRepo,
		positionUC: param.PositionUC,
		rs:         param.RS,
	}
}

// ProcessAccountSettle 处理账户结算业务
// 参考Trade.Trade()和Trade.Process()函数实现，但只处理单个账户参数
func (use *SettleUseCase) ProcessAccountSettle(accountSettle *futuresEnginePB.AccountSettleEngine) error {
	ctx := context.Background()

	accountSettleParam := utils.AccountSettleParam{AccountSettle: accountSettle}
	uid := accountSettleParam.GetUID()
	amount := accountSettleParam.GetAmount()
	price := accountSettleParam.GetPrice()
	base, quote := accountSettleParam.GetBaseQuote()

	if uid == "" || base == "" || quote == "" {
		return fmt.Errorf("invalid account settle data: uid=%s, base=%s, quote=%s", uid, base, quote)
	}

	logrus.Infof("ProcessAccountSettle started for uid: %s, pair: %s-%s, amount: %s, price: %s",
		uid, base, quote, amount.String(), price.String())

	// 锁定用户资产
	userMutex := use.rs.NewMutex(domain.MutexSwapPosLock+uid, redsync.WithExpiry(30*time.Second))
	if err := userMutex.Lock(); err != nil {
		logrus.Errorf("ProcessAccountSettle failed to lock user asset: %v, uid: %s", err, uid)
		return fmt.Errorf("failed to lock user asset for settlement: %v", err)
	}
	defer func() {
		unlockOk, unlockErr := userMutex.Unlock()
		if unlockErr != nil || !unlockOk {
			logrus.Errorf("ProcessAccountSettle failed to unlock user asset: ok=%v, err=%v, uid: %s", unlockOk, unlockErr, uid)
		}
	}()

	// 获取合约代码
	contractCode := util.ContractCode(base, quote)

	// 加载用户资产
	userAsset, err := use.cacheRepo.Load(ctx, uid, contractCode)
	if err != nil {
		logrus.Errorf("ProcessAccountSettle load user asset error: %v, uid: %s", err, uid)
		return err
	}

	// 处理结算业务逻辑
	err = use.processAccountSettlement(ctx, userAsset, contractCode, amount, price, accountSettleParam)
	if err != nil {
		logrus.Errorf("ProcessAccountSettle process settlement error: %v, uid: %s", err, uid)
		return err
	}

	// 保存落库更新后的资产
	err = use.saveUserAsset(ctx, userAsset, contractCode)
	if err != nil {
		logrus.Errorf("ProcessAccountSettle save user asset error: %v, uid: %s", err, uid)
		return err
	}

	logrus.Infof("ProcessAccountSettle completed successfully for uid: %s", uid)
	return nil
}

// processAccountSettlement 处理账户结算的核心业务逻辑
// 参考原来的Trade.Process()函数实现，但只处理单个账户的结算
func (use *SettleUseCase) processAccountSettlement(ctx context.Context, userAsset *repository.AssetSwap,
	contractCode string, amount, price decimal.Decimal, accountSettleParam utils.AccountSettleParam) error {

	if userAsset == nil {
		return fmt.Errorf("user asset is nil")
	}

	logrus.Infof("processAccountSettlement: uid=%s, contractCode=%s, amount=%s, price=%s",
		userAsset.UID, contractCode, amount.String(), price.String())

	// 根据持仓模式处理结算
	if userAsset.PositionMode == domain.HoldModeBoth {
		// 双向持仓结算逻辑
		err := use.settleBothPositions(userAsset, amount, price, accountSettleParam)
		if err != nil {
			return err
		}
	} else {
		// 单向持仓结算逻辑
		err := use.settleSinglePosition(userAsset, amount, price, accountSettleParam)
		if err != nil {
			return err
		}
	}

	return nil
}

func (use *SettleUseCase) settleBothPositions(userAsset *repository.AssetSwap,
	amount, price decimal.Decimal, accountSettleParam utils.AccountSettleParam) error {

	side := accountSettleParam.GetSide()
	isOpen := accountSettleParam.IsOpenPosition()
	if side == domain.Sell {
		amount = amount.Neg() // 空仓置为负数
	}

	if (side == domain.Buy && isOpen && userAsset.BothPos.Pos.Sign() >= 0) ||
		(side == domain.Sell && isOpen && userAsset.BothPos.Pos.Sign() <= 0) { // Brad说单向模式负数是做空仓
		// 方向一致直接开仓
		err := use.positionUC.OpenBothPos(userAsset, use.priceRepo, accountSettleParam)
		if err != nil {
			return err
		}
	} else {
		// 反向对冲平仓?
		err := use.positionUC.CloseBothPos(userAsset, use.priceRepo, accountSettleParam)
		if err != nil {
			return err
		}
		// Todo 再开仓
	}

	return nil
}

func (use *SettleUseCase) settleSinglePosition(userAsset *repository.AssetSwap,
	amount, price decimal.Decimal, accountSettleParam utils.AccountSettleParam) error {

	side := accountSettleParam.GetSide()
	isOpen := accountSettleParam.IsOpenPosition()

	var err error
	if side == domain.Sell && isOpen {
		err = use.positionUC.OpenShortPos(userAsset, use.priceRepo, accountSettleParam)
	} else if side == domain.Sell && !isOpen {
		err = use.positionUC.CloseLongPos(userAsset, use.priceRepo, accountSettleParam)
	} else if side == domain.Buy && isOpen {
		err = use.positionUC.OpenLongPos(userAsset, use.priceRepo, accountSettleParam)
	} else if side == domain.Buy && !isOpen {
		err = use.positionUC.CloseShortPos(userAsset, use.priceRepo, accountSettleParam)
	}

	return err
}

func (use *SettleUseCase) saveUserAsset(ctx context.Context, userAsset *repository.AssetSwap, contractCode string) error {
	if userAsset == nil {
		return fmt.Errorf("user asset is nil")
	}

	// Todo使用缓存仓库保存资产

	logrus.Infof("saveUserAsset: uid=%s, contractCode=%s", userAsset.UID, contractCode)

	logrus.Info("saveUserAsset: asset saved successfully (placeholder implementation)")
	return nil
}
