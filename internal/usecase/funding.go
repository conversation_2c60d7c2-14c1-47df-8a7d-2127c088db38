package usecase

import (
	"context"
	"sync"

	"futures-asset/configs"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"

	"go.uber.org/dig"
	cfg "yt.com/backend/common.git/config"
)

// FundingUseCaseParam 资产用例参数
type FundingUseCaseParam struct {
	dig.In

	Config      *cfg.Config[configs.Config] `name:"config"`
	FundingRepo repository.FundingRepository
}

// FundingUseCase 资产用例实现
type FundingUseCase struct {
	config      *cfg.Config[configs.Config]
	fundingRepo repository.FundingRepository
}

// NewFundingUseCase 创建资产用例实例
func NewFundingUseCase(param FundingUseCaseParam) usecase.FundingUseCase {
	return &FundingUseCase{
		config:      param.Config,
		fundingRepo: param.FundingRepo,
	}
}

// AllFundData implements usecase.FundingUseCase.
func (use *FundingUseCase) AllFundData(ctx context.Context, wg *sync.WaitGroup) (r []repository.FundingRate) {
	return use.fundingRepo.AllFundData(ctx, wg)
}

// FundRateAll implements usecase.FundingUseCase.
func (use *FundingUseCase) FundRateAll(ctx context.Context) (repository.FundRateAll, error) {
	return use.fundingRepo.FundRateAll(ctx)
}

// FundingFeeList implements usecase.FundingUseCase.
func (use *FundingUseCase) FundingFeeList(ctx context.Context, req *repository.FundingFeeListParam) (repository.FundingFeeList, error) {
	return use.fundingRepo.FundingFeeList(ctx, req)
}

// GetFundRate implements usecase.FundingUseCase.
func (use *FundingUseCase) GetFundRate(ctx context.Context, contractCode string) (repository.FundingRate, error) {
	return use.fundingRepo.GetFundRate(ctx, contractCode)
}

// GetFundRateList implements usecase.FundingUseCase.
func (use *FundingUseCase) GetFundRateList(ctx context.Context, req *repository.FundRateParam) (repository.FundRateReply, error) {
	return use.fundingRepo.GetFundRateList(ctx, req)
}

// LastFundMap implements usecase.FundingUseCase.
func (use *FundingUseCase) LastFundMap(ctx context.Context, wg *sync.WaitGroup) (r map[string]entity.LogFundingRate) {
	return use.fundingRepo.LastFundMap(ctx, wg)
}

func (use *FundingUseCase) GetBaseNum(ctx context.Context, key string) int {
	return use.fundingRepo.GetBaseNum(ctx, key)
}
