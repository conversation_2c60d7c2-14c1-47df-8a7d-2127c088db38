package liquidation

// import (
// 	"encoding/json"

// 	"futures-asset/cache"
// 	"futures-asset/cache/swapcache"
// 	"futures-asset/internal/delivery/http/payload"
// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/entity"
// 	"futures-asset/internal/domain/repository"
// )

// // Reduce 降级减仓-仅更新强平单数据(不区分Taker和Maker)
// type Reduce struct{}

// // LiquidationData 仅更新Taker强平单数据
// func (r *Reduce) LiquidationData(trade payload.Trade, req repository.TradeCommon, pos repository.PosSwap) {
// 	// 更新Taker
// 	// // 更新DB中数据
// 	// // 	- 强平状态
// 	// // 	- 强平成交总额
// 	burstSwap := entity.BurstSwap{
// 		BurstId:              trade.BurstId,
// 		BurstTime:            trade.BurstTime,
// 		UID:                  pos.UID,
// 		PosId:                pos.PosId,
// 		PosType:              pos.PosSide,
// 		LiquidationDealValue: req.Amount.Mul(req.Price).Truncate(domain.CurrencyPrecision), // 强平单成交总额,
// 	}
// 	if pos.Pos.Sign() == 0 {
// 		burstSwap.Status = domain.BurstFinished
// 	}
// 	// 序列化发送更新数据到队列, burst_time服务中处理更新逻辑(使用反射的调用方式)
// 	jsonBytes, _ := json.Marshal(burstSwap)
// 	swapcache.SendSwapTask(pos.ContractCode, cache.SwapBurstTask{
// 		Type:     cache.TaskTypeUpdateBurst,
// 		Data:     jsonBytes,
// 		FuncName: "UpdateReduceFields",
// 	})
// }
