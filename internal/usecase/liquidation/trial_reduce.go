package liquidation

// import (
// 	"fmt"
// 	"time"

// 	"futures-asset/internal/delivery/http/payload"
// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/entity"
// 	"futures-asset/internal/domain/repository"
// 	"futures-asset/util"

// 	uuid "github.com/satori/go.uuid"
// 	"github.com/shopspring/decimal"
// 	"github.com/sirupsen/logrus"
// )

// // TrialReduce 体验金减仓-只增加记录
// type TrialReduce struct{}

// // LiquidationData 只插入强平数据
// func (r *TrialReduce) LiquidationData(trade payload.Trade, req repository.TradeCommon, pos repository.PosSwap) {
// 	// 向DB插入强平单数据
// 	createTime := time.Now().UnixNano()
// 	base, quote := util.BaseQuote(pos.ContractCode)
// 	burstSwap := entity.BurstSwap{
// 		Id:                   fmt.Sprintf("%d_%s", createTime, uuid.NewV4().String()),
// 		BurstId:              trade.BurstId,
// 		PosId:                pos.PosId,
// 		UID:                  pos.UID,
// 		UserType:             int(pos.UserType),
// 		AccountType:          pos.AccountType,
// 		Base:                 base,
// 		Quote:                quote,
// 		Currency:             quote,
// 		OrderType:            domain.Limit,
// 		MarginMode:           domain.MarginMode(pos.MarginMode),
// 		PosType:              pos.PosSide,
// 		PosAmount:            req.Amount,
// 		PosValue:             pos.PosValue,
// 		Leverage:             pos.Leverage,
// 		LiquidationPrice:     pos.Liquidation,
// 		LiquidationType:      int(pos.LiquidationType),
// 		LiquidationFee:       decimal.Decimal{},
// 		LiquidationDealValue: req.Amount.Mul(req.Price).Truncate(domain.CurrencyPrecision), // 强平单成交总额
// 		IsOverflow:           domain.NotOverflow,
// 		IsForceRival:         domain.NotForceRival,
// 		ForceRivalAmount:     decimal.Decimal{},
// 		OpenPrice:            pos.OpenPriceAvg,
// 		OpenTime:             pos.OpenTime,
// 		BurstTime:            trade.BurstTime,
// 		Status:               domain.BurstFinished,
// 		CreateTime:           createTime,
// 	}
// 	err := burstSwap.Insert(nil)
// 	if err != nil {
// 		logrus.Errorln(fmt.Sprintf("insert disable symbol reduce liquidation data into db error: %s, %+v", err, burstSwap))
// 	}
// }
