package usecase

import (
	"context"
	"fmt"
	"time"

	"futures-asset/configs"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	"futures-asset/util"

	"github.com/go-redsync/redsync/v4"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
	"go.uber.org/dig"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
	cfg "yt.com/backend/common.git/config"
)

// AssetUseCase 资产用例实现
type AssetUseCase struct {
	rs *redsync.Redsync

	config         *cfg.Config[configs.Config]
	assetRepo      repository.AssetRepository
	profitLossRepo repository.ProfitLossRepository
	priceRepo      repository.PriceRepository
	cacheRepo      repository.CacheRepository
	formulaRepo    repository.FormulaRepository
	burstRepo      repository.BurstRepository
	settingRepo    repository.SettingRepository
}

// AssetUseCaseParam 资产用例参数
type AssetUseCaseParam struct {
	dig.In
	Config *cfg.Config[configs.Config] `name:"config"`

	RS *redsync.Redsync `name:"rs"`

	AssetRepo      repository.AssetRepository
	ProfitLossRepo repository.ProfitLossRepository
	PriceRepo      repository.PriceRepository
	CacheRepo      repository.CacheRepository
	FormulaRepo    repository.FormulaRepository
	BurstRepo      repository.BurstRepository
	SettingRepo    repository.SettingRepository
}

// NewAssetUseCase 创建资产用例实例
func NewAssetUseCase(param AssetUseCaseParam) usecase.AssetUseCase {
	return &AssetUseCase{
		rs: param.RS,

		config:         param.Config,
		assetRepo:      param.AssetRepo,
		profitLossRepo: param.ProfitLossRepo,
		priceRepo:      param.PriceRepo,
		cacheRepo:      param.CacheRepo,
		formulaRepo:    param.FormulaRepo,
		burstRepo:      param.BurstRepo,
		settingRepo:    param.SettingRepo,
	}
}

// LockAsset implements usecase.AssetUseCase.
func (use *AssetUseCase) LockAsset(ctx context.Context, param *usecase.LockParam) (*repository.AssetSwap, error) {
	if len(param.OrderID) <= 0 || len(param.Symbol) == 0 || param.Amount.Sign() <= 0 || param.MarginMode <= 0 {
		return nil, usecase.ParamInvalidError{Msg: "param err"}
	}
	if param.MarginMode != futuresassetpb.MarginMode_MARGIN_MODE_CROSS && param.MarginMode != futuresassetpb.MarginMode_MARGIN_MODE_ISOLATED {
		return nil, usecase.ParamInvalidError{Msg: "margin mode err"}
	}

	// 如果用户在爆仓中, 不能进行挂单
	isBursting, _ := use.burstRepo.IsBursting(ctx, repository.CheckBurstParam{
		UID:        param.UID,
		MarginMode: param.MarginMode,
		IsTrialPos: len(param.AwardOpIds) > 0,
	})
	if isBursting && param.IsInnerCall != 1 {
		return nil, usecase.BrustingError{Msg: "user bursting"}
	}

	userMutex := use.rs.NewMutex(domain.MutexSwapPosLock+param.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		return nil, errors.New("user lock err when recycle trial asset")
	}
	defer userMutex.Unlock()

	asset, err := use.cacheRepo.Load(ctx, param.UID, param.Symbol)
	if err != nil {
		return nil, errors.Wrap(err, "load user asset")
	}

	leverage := asset.GetLeverage(param.Symbol)
	if leverage.MarginMode != futuresassetpb.MarginMode_MARGIN_MODE_UNSPECIFIED && leverage.MarginMode != param.MarginMode {
		return nil, usecase.ParamInvalidError{Msg: "margin mode"}
	}

	if asset.PositionMode == futuresassetpb.PositionMode_POSITION_MODE_ONE_WAY {
		return nil, usecase.ParamInvalidError{Msg: "position mode"}
	}

	userLeverage := decimal.Zero
	switch param.MarginMode {
	case futuresassetpb.MarginMode_MARGIN_MODE_ISOLATED:
		// TODO 按照杠杆倍数
		if leverage.LLeverage != param.Leverage {
			return nil, usecase.ParamInvalidError{Msg: "isolated leverage not match"}
		}
		userLeverage = decimal.NewFromInt(int64(leverage.LLeverage))

	case futuresassetpb.MarginMode_MARGIN_MODE_CROSS:
		if leverage.Leverage != param.Leverage {
			return nil, usecase.ParamInvalidError{Msg: "cross leverage not match"}
		}
		userLeverage = decimal.NewFromInt(int64(leverage.Leverage))

	default:
		return nil, usecase.ParamInvalidError{Msg: "margin mode not match"}

	}
	if userLeverage.IntPart() <= 0 {
		return nil, usecase.ParamInvalidError{Msg: "leverage negative"}
	}

	maxPosValue, err := use.formulaRepo.GetMaxPosValueWithLeverage(ctx, param.Symbol, leverage.Leverage)
	if err != nil {
		return nil, errors.Wrap(err, "get max pos value")
	}
	if maxPosValue.IsZero() {
		return nil, errors.New("max pos value is zero")
	}

	markPrice := use.priceRepo.GetMarkPrice(ctx, param.Symbol)
	if maxPosValue.IsPositive() {
		frozenValue := asset.GetFrozenByCode(param.Symbol).Mul(userLeverage)
		if maxPosValue.LessThan(asset.ShortPos.CalcPosValue(markPrice).Add(asset.LongPos.CalcPosValue(markPrice)).Add(param.Amount).Add(frozenValue)) {
			return nil, errors.New("max pos")
		}
	}

	available, err := use.assetRepo.GetAvailableBase(ctx, asset, param.MarginMode, param.Currency)
	if err != nil {
		return nil, errors.Wrap(err, "get available base")
	}

	// trialBalance := asset.TrialCBalance(param.Currency)
	// if asset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI {
	// 	trialBalance, err = asset.TotalJoinTrialBalance()
	// 	if err != nil {
	// 		log.Println("both Lock error:", err)
	// 		return nil, err
	// 	}
	// }

	if available.Sign() <= 0 {
		return nil, usecase.InsuffFundsError{Msg: "insufficient balance"}
	}

	originFrozen := param.Amount
	rate := decimal.NewFromInt(1)
	if asset.AssetMode == futuresassetpb.AssetMode_ASSET_MODE_MULTI {
		// 联合保证金 需要转成 usdt
		rate = use.priceRepo.SpotURate(ctx, param.Currency)
		if rate.IsZero() {
			logrus.Infof("available: %s  param.Leverage: %d rate: %s param.Amount: %s", available, leverage.Leverage, rate, param.Amount)

			return nil, usecase.InsuffFundsError{Msg: "spot rate is zero"}
		}

		usdtFrozen, _ := util.RoundCeil(originFrozen.Mul(rate), domain.CurrencyPrecision)
		diff := available.Sub(usdtFrozen)
		if param.OrderType == futuresassetpb.OrderType_ORDER_TYPE_MARKET {
			// 市价锁定, 资金不足则按照最大可锁定资金锁定
			if diff.Sign() <= 0 {
				codeSetting, err := use.settingRepo.GetCachePair(ctx, param.Symbol)
				if err != nil {
					return nil, err
				}
				minAmount := codeSetting.MinAmount
				if available.Mul(userLeverage).LessThan(minAmount) {
					return nil, errors.New("place min amount limit")
				}
				// TODO check
				// param.Amount = available.Mul(userLeverage).Div(rate).Div(param.Price).Truncate(codeSetting.AmountPrecision)
				// originFrozen, _ = util.RoundCeil(param.Amount.Mul(param.Price).Div(userLeverage).Mul(rate), domain.CurrencyPrecision)
				// diff = available.Sub(usdtFrozen)
			}
		} else {
			if diff.Sign() < 0 {
				return nil, usecase.InsuffFundsError{Msg: "diff insufficient balance multi"}
			}
			// if available.Mul(userLeverage).Div(rate).Div(param.Price).LessThan(param.Amount) {
			// 	return nil, errors.New("insufficient balance")
			// }
			if available.Div(rate).LessThan(param.Amount) {
				return nil, usecase.InsuffFundsError{Msg: "limit order insufficient balance multi"}
			}
		}
	} else {
		diff := available.Sub(originFrozen)
		if param.OrderType == futuresassetpb.OrderType_ORDER_TYPE_MARKET {
			// 市价锁定, 资金不足则按照最大可锁定资金锁定
			if diff.Sign() <= 0 {
				codeSetting, err := use.settingRepo.GetCachePair(ctx, param.Symbol)
				if err != nil {
					return nil, err
				}
				minAmount := codeSetting.MinAmount
				logrus.Printf("place market order settingMinAmount:%s", codeSetting.MinAmount)
				if available.Mul(userLeverage).LessThan(minAmount) {
					logrus.Printf("returned place market order available: %s minAmount: %s (userLeverage: %s settingMinAmount: %s)",
						available, minAmount, userLeverage, codeSetting.MinAmount)
					return nil, errors.New("place min amount limit")
				}
				// TODO check
				// param.Amount = available.Mul(userLeverage).Div(param.Price).Truncate(codeSetting.AmountPrecision)
				// originFrozen, _ = util.RoundCeil(param.Amount.Mul(param.Price).Div(userLeverage), constvar.CurrencyPrecision)
				// diff = available.Sub(originFrozen)
			}
		} else {
			if diff.Sign() < 0 {
				return nil, usecase.InsuffFundsError{Msg: "diff insufficient balance single"}
			}
			if available.LessThan(param.Amount) {
				return nil, usecase.InsuffFundsError{Msg: "limit order insufficient balance single"}
			}
		}
	}

	logrus.Info(0, "================ OpenBuy.Lock [IncrFrozen]", param.UID, "originFrozen", originFrozen, "param", fmt.Sprintf("%+v", param), fmt.Sprintf("asset.Frozen %+v", asset.Frozen))
	asset.IncrFrozen(param.Symbol, originFrozen)
	logrus.Info(0, "================ OpenBuy.Lock [IncrFrozen]", param.UID, "originFrozen", originFrozen, fmt.Sprintf("asset.Frozen %+v", asset.Frozen))

	err = use.cacheRepo.UpdateBalance(ctx, asset)
	if err != nil {
		return nil, err
	}

	// TODO 资产异步存库
	// assetSwap := modelutil.NewLogAssetSync(asset, param.Quote, param.OperateTime)
	// go AddAssetLogs(redis, assetSwap) // wallet资产异步存库

	return nil, nil
}

// UnLockAsset implements usecase.AssetUseCase.
func (use *AssetUseCase) UnLockAsset(ctx context.Context, param *usecase.BatchUnlockParam) (*usecase.BatchUnlockReply, error) {
	userMutex := use.rs.NewMutex(domain.MutexSwapPosLock+param.UID, redsync.WithExpiry(30*time.Second))
	if userMutex.Lock() != nil {
		return nil, errors.New("user lock err when recycle trial asset")
	}
	defer userMutex.Unlock()

	asset, err := use.cacheRepo.Load(ctx, param.UID, param.Symbol)
	if err != nil {
		return nil, errors.Wrap(err, "load user asset")
	}

	/*
		{
		UserId:********
		Base:eth
		Quote:usdt
		AccountType:swap
		OperateTime:1686215518423961995
		Orders:[
		{
		UserId: UserType:0 OrderId:10886728767014174720
		Base: Quote:
		OrderType:10
		Price:1833.3 Amount:0.2 AccountType:
		Side:1
		Offset:1
		OperateTime:0
		Leverage:20
		MarginMode:1
		UnfrozenMargin:18.333
		IsInnerCall:0 IsErr:false
		HoldMode:0
		IsLimitOrder:1 LiquidationType:0
		}]
		}
	*/

	leverage := asset.GetLeverage(param.Symbol)

	reply := &usecase.BatchUnlockReply{
		SuccessList: make([]*usecase.UnLockParam, 0),
		FailedList:  make([]*usecase.UnLockParam, 0),
	}
	// assetSyncList := make([]*repository.LogAssetSync, 0)
	for _, order := range param.Batchs {
		replyItem := &usecase.UnLockParam{
			OrderId: order.OrderId,
		}

		if leverage.MarginMode != futuresassetpb.MarginMode_MARGIN_MODE_UNSPECIFIED && leverage.MarginMode != futuresassetpb.MarginMode(order.MarginMode) {
			// 记录错误订单
			reply.FailedList = append(reply.FailedList, replyItem)
			continue
		}

		// 按照统一币对批量修改(注: 此处slf.req.ContractCode()不可使用order.ContractCode()替代, 因为撮合给的orders中所有ContractCode都为空)
		asset.DecrFrozen(param.Symbol, order.Amount, len(order.AwardOpIds) > 0)

		// reply.SuccessList = append(reply.SuccessList, replyItem)
		// assetSync := modelutil.NewLogAssetSync(asset, param.Currency, time.Now().UnixNano())
		// assetSyncList = append(assetSyncList, assetSync)
	}

	err = use.cacheRepo.UpdateBalance(ctx, asset)
	if err != nil {
		return nil, err
	}

	// TODO 资产异步存库
	// go AddAssetLogs(assetSyncList...) // wallet资产异步存库

	// // 如果存在体验金更新仓位保证金
	// if len(posSides) > 0 {
	// 	go func() {
	// 		_redis := redislib.Redis()
	// 		if posSides[enum.LongPos] {
	// 			pos := userCache.NewLogPosSync(asset.TrialLongPos, slf.req.OperateTime, "", slf.req.OrderId,
	// 				slf.req.Side, slf.req.Offset, slf.req.Amount.Neg(), decimal.Zero)
	// 			if err := persist.SyncPos(_redis, slf.req.ContractCode(), pos); err != nil {
	// 				loglib.GetLog().Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
	// 			}
	// 		}
	// 		if posSides[enum.ShortPos] {
	// 			pos := userCache.NewLogPosSync(asset.TrialShortPos, slf.req.OperateTime, "", slf.req.OrderId,
	// 				slf.req.Side, slf.req.Offset, slf.req.Amount.Neg(), decimal.Zero)
	// 			if err := persist.SyncPos(_redis, slf.req.ContractCode(), pos); err != nil {
	// 				loglib.GetLog().Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
	// 			}
	// 		}
	// 		if posSides[enum.BothPos] {
	// 			pos := userCache.NewLogPosSync(asset.TrialBothPos, slf.req.OperateTime, "", slf.req.OrderId,
	// 				slf.req.Side, slf.req.Offset, slf.req.Amount.Neg(), decimal.Zero)
	// 			if err := persist.SyncPos(_redis, slf.req.ContractCode(), pos); err != nil {
	// 				loglib.GetLog().Error(fmt.Sprintf("lpush pos change log err:%v, pos:%+v", err, pos))
	// 			}
	// 		}
	// 	}()
	// }

	return reply, nil
}
