package usecase

import (
	"context"
	"time"

	"futures-asset/configs"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"

	"go.uber.org/dig"
	cfg "yt.com/backend/common.git/config"
)

// BurstUseCase 资产用例实现
type BurstUseCase struct {
	config    *cfg.Config[configs.Config]
	burstRepo repository.BurstRepository
}

// BurstUseCaseParam 资产用例参数
type BurstUseCaseParam struct {
	dig.In

	Config    *cfg.Config[configs.Config] `name:"config"`
	BurstRepo repository.BurstRepository
}

// NewBurstUseCase 创建资产用例实例
func NewBurstUseCase(param BurstUseCaseParam) usecase.BurstUseCase {
	return &BurstUseCase{
		config:    param.Config,
		burstRepo: param.BurstRepo,
	}
}

// GetBurstInfoByTableNameAndId implements usecase.BurstUseCase.
func (uc *BurstUseCase) GetBurstInfoByTableNameAndId(ctx context.Context, id string) (*entity.BurstSwap, error) {
	return uc.burstRepo.GetBurstInfoByTableNameAndId(ctx, id)
}

// SearchBurstInfos implements usecase.BurstUseCase.
func (uc *BurstUseCase) SearchBurstInfos(ctx context.Context, conditions map[string]interface{}, ranges map[string]map[string]interface{}, pageNum int, pageSize int) (int64, []entity.BurstSwap) {
	return uc.burstRepo.SearchBurstInfos(ctx, conditions, ranges, pageNum, pageSize)
}

// StatBurstTimesByTableNameList implements usecase.BurstUseCase.
func (uc *BurstUseCase) StatBurstTimesByTableNameList(ctx context.Context, startTime time.Time, endTime time.Time) []entity.StatBurstInfo {
	return uc.burstRepo.StatBurstTimesByTableNameList(ctx, startTime, endTime)
}
