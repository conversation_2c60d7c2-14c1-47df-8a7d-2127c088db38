package utils

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"
)

const DefaultTimeout = 30 * time.Second

const (
	TimezoneUTC = "UTC"

	TimezoneTaipei = "Asia/Taipei"
)

func TimeNowUTC() time.Time {
	return time.Now().UTC()
}

func TimeNowUnix() int64 {
	return time.Now().Unix()
}

func TimestampToRFC(timestamp int64) string {
	return time.Unix(timestamp, 0).UTC().Format(time.RFC3339)
}

func TimestampConvTime(timestamp int64, timezone string) (time.Time, error) {
	loc, err := time.LoadLocation(timezone)
	if err != nil {
		return time.Time{}, err
	}

	return time.Unix(timestamp, 0).In(loc), nil
}

func ParseInLocation(timeStr, timezone, layout string) (time.Time, error) {
	loc, err := time.LoadLocation(timezone)
	if err != nil {
		return time.Time{}, err
	}

	t, err := time.ParseInLocation(layout, timeStr, loc)
	if err != nil {
		return time.Time{}, err
	}

	return t, nil
}

type TimestampConv struct {
	Timestamp int64

	Timezone string
	Layout   string
}

func (tv *TimestampConv) ToTime() (time.Time, error) {
	timeStr, err := tv.ToStr()
	if err != nil {
		return time.Time{}, err
	}

	t, err := ParseInLocation(timeStr, tv.Timezone, tv.Layout)
	if err != nil {
		return time.Time{}, err
	}

	return t, nil
}

func (tv *TimestampConv) ToStr() (string, error) {
	tempTime, err := TimestampConvTime(tv.Timestamp, tv.Timezone)
	if err != nil {
		return "", err
	}

	timeStr := tempTime.Format(tv.Layout)

	return timeStr, nil
}

// 此func()是檢查報價-前一天均價API所提供的時間是否為昨天，convertTime 固定會是每天零點UTC+0
// 因機器時區為UTC+8，報價會在每天零點(UTC+8)計算前一天的均價，轉成前一天零點(UTC+0)
// 因此這邊的轉換邏輯會跟報價那邊的一樣，確保兩邊的結果一致
func IsYesterday(convertTime time.Time) bool {
	yesterday := time.Now().AddDate(0, 0, -1)
	yesterdayByDay := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, time.UTC)

	return convertTime.Equal(yesterdayByDay)
}

func RunLostTime() func() float64 {
	t := TimeNowUTC()

	return func() float64 {
		return TimeNowUTC().Sub(t).Seconds()
	}
}

func StartTimeMetric(msg string) func(ctx context.Context) {
	t := TimeNowUTC()

	return func(ctx context.Context) {
		logrus.Println(ctx.Value(LogUUID), ":", msg, ", cost time:", TimeNowUTC().Sub(t).Seconds(), "s")
	}
}
