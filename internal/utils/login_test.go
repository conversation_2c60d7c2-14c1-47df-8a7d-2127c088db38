package utils

import (
	"strings"
	"testing"

	"github.com/nyaruka/phonenumbers"
	"github.com/sirupsen/logrus"
)

type CountryPhone struct {
	Phone   string
	Country string
}

func TestParsePhoneFrom(t *testing.T) {
	tests := []CountryPhone{
		{Phone: "+93123456789", Country: "阿富汗"},
		{Phone: "+355123456789", Country: "阿尔巴尼亚"},
		{Phone: "+213123456789", Country: "阿尔及利亚"},
		{Phone: "+376123456", Country: "安道尔"},
		{Phone: "+244123456789", Country: "安哥拉"},
		{Phone: "+12681234567", Country: "安提瓜和巴布达"},
		{Phone: "+541234567890", Country: "阿根廷"},
		{Phone: "+374123456789", Country: "亚美尼亚"},
		{Phone: "+61123456789", Country: "澳大利亚"},
		{Phone: "+43123456789", Country: "奥地利"},
		{Phone: "+994123456789", Country: "阿塞拜疆"},
		{Phone: "+12421234567", Country: "巴哈马"},
		{Phone: "+97312345678", Country: "巴林"},
		{Phone: "+8801234567890", Country: "孟加拉国"},
		{Phone: "+12461234567", Country: "巴巴多斯"},
		{Phone: "+375123456789", Country: "白俄罗斯"},
		{Phone: "+32123456789", Country: "比利时"},
		{Phone: "+5011234567", Country: "伯利兹"},
		{Phone: "+22912345678", Country: "贝宁"},
		{Phone: "+97512345678", Country: "不丹"},
		{Phone: "+59112345678", Country: "玻利维亚"},
		{Phone: "+387123456789", Country: "波黑"},
		{Phone: "+26712345678", Country: "博茨瓦纳"},
		{Phone: "+551234567890", Country: "巴西"},
		{Phone: "+6731234567", Country: "文莱"},
		{Phone: "+359123456789", Country: "保加利亚"},
		{Phone: "+22612345678", Country: "布基纳法索"},
		{Phone: "+25712345678", Country: "布隆迪"},
		{Phone: "+2381234567", Country: "佛得角"},
		{Phone: "+85512345678", Country: "柬埔寨"},
		{Phone: "+237123456789", Country: "喀麦隆"},
		{Phone: "+11234567890", Country: "加拿大"},
		{Phone: "+23612345678", Country: "中非共和国"},
		{Phone: "+23512345678", Country: "乍得"},
		{Phone: "+56123456789", Country: "智利"},
		{Phone: "+861234567890", Country: "中国"},
		{Phone: "+571234567890", Country: "哥伦比亚"},
		{Phone: "+2691234567", Country: "科摩罗"},
		{Phone: "+242123456789", Country: "刚果（布）"},
		{Phone: "+50612345678", Country: "哥斯达黎加"},
		{Phone: "+385123456789", Country: "克罗地亚"},
		{Phone: "+5312345678", Country: "古巴"},
		{Phone: "+35712345678", Country: "塞浦路斯"},
		{Phone: "+420123456789", Country: "捷克共和国"},
		{Phone: "+243123456789", Country: "刚果（金）"},
		{Phone: "+4512345678", Country: "丹麦"},
		{Phone: "+25312345678", Country: "吉布提"},
		{Phone: "+17671234567", Country: "多米尼克"},
		{Phone: "+18091234567", Country: "多米尼加共和国"},
		{Phone: "+593123456789", Country: "厄瓜多尔"},
		{Phone: "+201234567890", Country: "埃及"},
		{Phone: "+50312345678", Country: "萨尔瓦多"},
		{Phone: "+240123456789", Country: "赤道几内亚"},
		{Phone: "+2911234567", Country: "厄立特里亚"},
		{Phone: "+37212345678", Country: "爱沙尼亚"},
		{Phone: "+26812345678", Country: "斯威士兰"},
		{Phone: "+251123456789", Country: "埃塞俄比亚"},
		{Phone: "+6791234567", Country: "斐济"},
		{Phone: "+358123456789", Country: "芬兰"},
		{Phone: "+331234567890", Country: "法国"},
		{Phone: "+24112345678", Country: "加蓬"},
		{Phone: "+2201234567", Country: "冈比亚"},
		{Phone: "+995123456789", Country: "格鲁吉亚"},
		{Phone: "+491234567890", Country: "德国"},
		{Phone: "+233123456789", Country: "加纳"},
		{Phone: "+301234567890", Country: "希腊"},
		{Phone: "+14731234567", Country: "格林纳达"},
		{Phone: "+50212345678", Country: "危地马拉"},
		{Phone: "+22412345678", Country: "几内亚"},
		{Phone: "+2451234567", Country: "几内亚比绍"},
		{Phone: "+5921234567", Country: "圭亚那"},
		{Phone: "+50912345678", Country: "海地"},
		{Phone: "+50412345678", Country: "洪都拉斯"},
		{Phone: "+36123456789", Country: "匈牙利"},
		{Phone: "+3541234567", Country: "冰岛"},
		{Phone: "+911234567890", Country: "印度"},
		{Phone: "+621234567890", Country: "印度尼西亚"},
		{Phone: "+981234567890", Country: "伊朗"},
		{Phone: "+9641234567890", Country: "伊拉克"},
		{Phone: "+353123456789", Country: "爱尔兰"},
		{Phone: "+972123456789", Country: "以色列"},
		{Phone: "+391234567890", Country: "意大利"},
		{Phone: "+18761234567", Country: "牙买加"},
		{Phone: "+811234567890", Country: "日本"},
		{Phone: "+962123456789", Country: "约旦"},
		{Phone: "+77123456789", Country: "哈萨克斯坦"},
		{Phone: "+254123456789", Country: "肯尼亚"},
		{Phone: "+68612345", Country: "基里巴斯"},
		{Phone: "+96512345678", Country: "科威特"},
		{Phone: "+996123456789", Country: "吉尔吉斯斯坦"},
		{Phone: "+85612345678", Country: "老挝"},
		{Phone: "+37112345678", Country: "拉脱维亚"},
		{Phone: "+96112345678", Country: "黎巴嫩"},
		{Phone: "+26612345678", Country: "莱索托"},
		{Phone: "+231123456789", Country: "利比里亚"},
		{Phone: "+218123456789", Country: "利比亚"},
		{Phone: "+4231234567", Country: "列支敦士登"},
		{Phone: "+37012345678", Country: "立陶宛"},
		{Phone: "+352123456789", Country: "卢森堡"},
		{Phone: "+261123456789", Country: "马达加斯加"},
		{Phone: "+265123456789", Country: "马拉维"},
		{Phone: "+60123456789", Country: "马来西亚"},
		{Phone: "+9601234567", Country: "马尔代夫"},
		{Phone: "+22312345678", Country: "马里"},
		{Phone: "+35612345678", Country: "马耳他"},
		{Phone: "+6921234567", Country: "马绍尔群岛"},
		{Phone: "+22212345678", Country: "毛里塔尼亚"},
		{Phone: "+2301234567", Country: "毛里求斯"},
		{Phone: "+521234567890", Country: "墨西哥"},
		{Phone: "+6911234567", Country: "密克罗尼西亚"},
		{Phone: "+37312345678", Country: "摩尔多瓦"},
		{Phone: "+377123456789", Country: "摩纳哥"},
		{Phone: "+97612345678", Country: "蒙古"},
		{Phone: "+382123456789", Country: "黑山"},
		{Phone: "+212123456789", Country: "摩洛哥"},
		{Phone: "+258123456789", Country: "莫桑比克"},
		{Phone: "+9512345678", Country: "缅甸"},
		{Phone: "+264123456789", Country: "纳米比亚"},
		{Phone: "+674123456", Country: "瑙鲁"},
		{Phone: "+977123456789", Country: "尼泊尔"},
		{Phone: "+31123456789", Country: "荷兰"},
		{Phone: "+64123456789", Country: "新西兰"},
		{Phone: "+50512345678", Country: "尼加拉瓜"},
		{Phone: "+22712345678", Country: "尼日尔"},
		{Phone: "+234123456789", Country: "尼日利亚"},
		{Phone: "+850123456789", Country: "朝鲜"},
		{Phone: "+38912345678", Country: "北马其顿"},
		{Phone: "+4712345678", Country: "挪威"},
		{Phone: "+96812345678", Country: "阿曼"},
		{Phone: "+921234567890", Country: "巴基斯坦"},
		{Phone: "+6801234567", Country: "帕劳"},
		{Phone: "+970123456789", Country: "巴勒斯坦"},
		{Phone: "+5071234567", Country: "巴拿马"},
		{Phone: "+6751234567", Country: "巴布亚新几内亚"},
		{Phone: "+595123456789", Country: "巴拉圭"},
		{Phone: "+51123456789", Country: "秘鲁"},
		{Phone: "+631234567890", Country: "菲律宾"},
		{Phone: "+48123456789", Country: "波兰"},
		{Phone: "+351123456789", Country: "葡萄牙"},
		{Phone: "+97412345678", Country: "卡塔尔"},
		{Phone: "+40123456789", Country: "罗马尼亚"},
		{Phone: "+71234567890", Country: "俄罗斯"},
		{Phone: "+250123456789", Country: "卢旺达"},
		{Phone: "+18691234567", Country: "圣基茨和尼维斯"},
		{Phone: "+17581234567", Country: "圣卢西亚"},
		{Phone: "+17841234567", Country: "圣文森特和格林纳丁斯"},
		{Phone: "+68512345", Country: "萨摩亚"},
		{Phone: "+378123456789", Country: "圣马力诺"},
		{Phone: "+2391234567", Country: "圣多美和普林西比"},
		{Phone: "+966123456789", Country: "沙特阿拉伯"},
		{Phone: "+22112345678", Country: "塞内加尔"},
		{Phone: "+381123456789", Country: "塞尔维亚"},
		{Phone: "+2481234567", Country: "塞舌尔"},
		{Phone: "+232123456789", Country: "塞拉利昂"},
		{Phone: "+6512345678", Country: "新加坡"},
		{Phone: "+421123456789", Country: "斯洛伐克"},
		{Phone: "+38612345678", Country: "斯洛文尼亚"},
		{Phone: "+67712345", Country: "所罗门群岛"},
		{Phone: "+252123456789", Country: "索马里"},
		{Phone: "+27123456789", Country: "南非"},
		{Phone: "+821234567890", Country: "韩国"},
		{Phone: "+21112345678", Country: "南苏丹"},
		{Phone: "+341234567890", Country: "西班牙"},
		{Phone: "+94123456789", Country: "斯里兰卡"},
		{Phone: "+249123456789", Country: "苏丹"},
		{Phone: "+597123456", Country: "苏里南"},
		{Phone: "+46123456789", Country: "瑞典"},
		{Phone: "+41123456789", Country: "瑞士"},
		{Phone: "+963123456789", Country: "叙利亚"},
		{Phone: "+886123456789", Country: "台湾"},
		{Phone: "+992123456789", Country: "塔吉克斯坦"},
		{Phone: "+255123456789", Country: "坦桑尼亚"},
		{Phone: "+66123456789", Country: "泰国"},
		{Phone: "+67012345678", Country: "东帝汶"},
		{Phone: "+22812345678", Country: "多哥"},
		{Phone: "+67612345", Country: "汤加"},
		{Phone: "+18681234567", Country: "特立尼达和多巴哥"},
		{Phone: "+21612345678", Country: "突尼斯"},
		{Phone: "+901234567890", Country: "土耳其"},
		{Phone: "+99312345678", Country: "土库曼斯坦"},
		{Phone: "+68812345", Country: "图瓦卢"},
		{Phone: "+256123456789", Country: "乌干达"},
		{Phone: "+380123456789", Country: "乌克兰"},
		{Phone: "+971123456789", Country: "阿联酋"},
		{Phone: "+441234567890", Country: "英国"},
		{Phone: "+11234567880", Country: "美国"},
		{Phone: "+59812345678", Country: "乌拉圭"},
		{Phone: "+998123456789", Country: "乌兹别克斯坦"},
		{Phone: "+67812345", Country: "瓦努阿图"},
		// {Phone: "+379123456789", Country: "梵蒂冈"},
		{Phone: "+581234567890", Country: "委内瑞拉"},
		{Phone: "+84123456789", Country: "越南"},
		{Phone: "+967123456789", Country: "也门"},
		{Phone: "+260123456789", Country: "赞比亚"},
		{Phone: "+263123456789", Country: "津巴布韦"},
	}

	for _, cp := range tests {
		newLoginID := ""
		num, err := phonenumbers.Parse(cp.Phone, "")
		if err != nil {
			logrus.WithFields(logrus.Fields{
				"err": err,
			}).Error("failed phone")
		}

		internationalFormat := phonenumbers.Format(num, phonenumbers.INTERNATIONAL)
		for i, str := range strings.Split(internationalFormat, " ") {
			newLoginID += str

			if i == 0 {
				newLoginID += "-"
			}
		}

		newLoginID = strings.ReplaceAll(newLoginID, "+", "")

		phone, _ := ParsePhoneFrom(newLoginID)
		number := "+" + phone.AreaCode + phone.Number
		// fmt.Println(fmt.Sprintf("%+v", phone))
		if number != cp.Phone {
			t.Error("not equal")
		}
	}
}
