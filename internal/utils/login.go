package utils

import (
	"errors"
	"strings"
)

const (
	_loginIDPhoneSeparator       = "-"
	_loginIDPhoneSeparatedLength = 2
)

var (
	ErrLoginIDPhoneSeparatedLength = errors.New("mismatch phone format, should be XXX-XXXXXXXXXX")
	ErrUnsupportedLoginType        = errors.New("unsupported login type")
)

type Phone struct {
	AreaCode string
	Number   string
}

func (p Phone) IsEmpty() bool {
	return len(p.AreaCode) == 0 || len(p.Number) == 0
}

func (p Phone) LoginID() string {
	return GetLoginID(p.AreaCode, p.Number)
}

// ParsePhoneFrom parses login id to area code and phone number
func ParsePhoneFrom(loginID string) (Phone, error) {
	phone := Phone{}
	span := strings.Split(loginID, _loginIDPhoneSeparator)
	if len(span) >= _loginIDPhoneSeparatedLength {
		phone.AreaCode = span[0]
		phone.Number = strings.ReplaceAll(strings.Join(span[1:], ""), _loginIDPhoneSeparator, "")
	}

	return phone, nil
}

func GetLoginID(areaCode, phone string) string {
	return areaCode + _loginIDPhoneSeparator + phone
}

type Login struct {
	Email       string
	AreaCode    string
	PhoneNumber string
}
