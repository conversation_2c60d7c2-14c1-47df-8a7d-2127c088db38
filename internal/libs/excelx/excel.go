package libs

import (
	"bytes"
	"fmt"
	"futures-asset/internal/domain"
	"go/ast"
	"reflect"
	"strings"

	"github.com/360EntSecGroup-Skylar/excelize/v2"
)

func ExportExcel(thead []string, tbody interface{}) (r *bytes.Buffer, err error) {
	// make sure 'tbody' is a Slice
	valTbody := reflect.ValueOf(tbody)
	if valTbody.Kind() != reflect.Slice {
		err = fmt.Errorf("tbody not slice")
		return
	}
	var (
		// it's a slice, so open up its values
		n     = valTbody.Len()
		sheet = "Sheet1"
	)

	xlsx := excelize.NewFile()
	index := xlsx.NewSheet(sheet)

	// write thead
	for k, v := range thead {
		var axis string
		axis, err = excelize.CoordinatesToCellName(k+1, 1)
		if err != nil {
			err = fmt.Errorf("thead to cell name error: %s", err)
			return
		}
		xlsx.SetCellValue(sheet, axis, v)
	}
	// write tbody
	for i := 0; i < n; i++ {
		v := valTbody.Index(i)
		if v.Kind() == reflect.Ptr {
			v = v.Elem()
		}
		if v.Kind() != reflect.Struct {
			continue
		}
		numField := v.NumField() // number of fields in struct

		c := 0
		for j := 0; j < numField; j++ {
			f := v.Field(j)
			t := v.Type().Field(j)
			if !ast.IsExported(t.Name) {
				continue
			}
			tags := ParseTagSetting(t.Tag, "excel")
			// is ignored field
			if _, ok := tags["-"]; ok {
				continue
			}
			c += 1
			var axis string
			axis, err = excelize.CoordinatesToCellName(c, i+2)
			if err != nil {
				err = fmt.Errorf("tbody to cell name error: %s", err)
				return
			}
			xlsx.SetCellValue(sheet, axis, f.Interface())
		}
	}
	xlsx.SetActiveSheet(index)
	r, err = xlsx.WriteToBuffer()
	if err != nil {
		err = fmt.Errorf("write to buffer error: %s", err)
		return
	}
	return
}

func ExportMulSheetExcel(sheets []string, theads [][]string, tbodys []interface{}, reqUserType int32) (r *bytes.Buffer, err error) {
	if len(sheets) != len(tbodys) {
		return nil, fmt.Errorf("len(sheets) != len(tbodys)")
	}
	xlsx := excelize.NewFile()
	for is := 0; is < len(sheets); is++ {
		sheet := sheets[is]
		tbody := tbodys[is]
		thead := theads[is]

		valTbody := reflect.ValueOf(tbody)
		if valTbody.Kind() != reflect.Slice {
			err = fmt.Errorf("tbody not slice")
			return
		}
		var n = valTbody.Len()
		index := xlsx.NewSheet(sheet)

		// write thead
		for k, v := range thead {
			var axis string
			axis, err = excelize.CoordinatesToCellName(k+1, 1)
			if err != nil {
				err = fmt.Errorf("thead to cell name error: %s", err)
				return
			}
			xlsx.SetCellValue(sheet, axis, v)
		}
		// write tbody
		for i := 0; i < n; i++ {
			v := valTbody.Index(i)
			if v.Kind() == reflect.Ptr {
				v = v.Elem()
			}
			if v.Kind() != reflect.Struct {
				continue
			}
			numField := v.NumField() // number of fields in struct

			c := 0
			for j := 0; j < numField; j++ {
				f := v.Field(j)
				t := v.Type().Field(j)
				if !ast.IsExported(t.Name) {
					continue
				}
				tags := ParseTagSetting(t.Tag, "excel")
				// is ignored field
				if _, ok := tags["-"]; ok {
					continue
				}
				c += 1
				var axis string
				axis, err = excelize.CoordinatesToCellName(c, i+2)
				if err != nil {
					err = fmt.Errorf("tbody to cell name error: %s", err)
					return
				}
				xlsx.SetCellValue(sheet, axis, f.Interface())
			}
		}
		if is == 0 {
			xlsx.SetActiveSheet(index)
		}
	}

	xlsx.DeleteSheet("Sheet1")
	if reqUserType == domain.UserTypeManager {

	} else if reqUserType == domain.UserTypeStaff {
		xlsx.DeleteSheet(sheets[0])
	} else if reqUserType == domain.UserTypeSeniorAgent {
		xlsx.DeleteSheet(sheets[0])
		xlsx.DeleteSheet(sheets[1])
	} else if reqUserType == domain.UserTypeAgent {
		xlsx.DeleteSheet(sheets[0])
		xlsx.DeleteSheet(sheets[1])
		xlsx.DeleteSheet(sheets[2])
	} else {
		xlsx.DeleteSheet(sheets[0])
		xlsx.DeleteSheet(sheets[1])
		xlsx.DeleteSheet(sheets[2])
	}
	r, err = xlsx.WriteToBuffer()
	if err != nil {
		err = fmt.Errorf("write to buffer error: %s", err)
		return
	}
	return
}

func ExportMultiExcel(firstTitleList []string, secondTitleList [][]string, tbody interface{}) (r *bytes.Buffer, err error) {
	// make sure 'tbody' is a Slice
	valTbody := reflect.ValueOf(tbody)
	if valTbody.Kind() != reflect.Slice {
		err = fmt.Errorf("tbody not slice")
		return
	}
	if len(firstTitleList) != len(secondTitleList) {
		err = fmt.Errorf("title len not equal")
		return
	}
	var (
		// it's a slice, so open up its values
		n     = valTbody.Len()
		sheet = "Sheet1"
	)

	xlsx := excelize.NewFile()
	index := xlsx.NewSheet(sheet)

	col := 0
	for k, title := range firstTitleList {
		if k > 0 {
			col += len(secondTitleList[k-1])
		}
		start, er := excelize.ColumnNumberToName(col + 1)
		if er != nil {
			err = fmt.Errorf("firstTitleList to cell name error: %s", er)
			return
		}
		end, er := excelize.ColumnNumberToName(col + len(secondTitleList[k]))
		if er != nil {
			err = fmt.Errorf("firstTitleList to cell name error: %s", er)
			return
		}
		xlsx.MergeCell(sheet, fmt.Sprintf("%s1", start), fmt.Sprintf("%s1", end))
		var axis string
		axis, err = excelize.CoordinatesToCellName(col+1, 1)
		if err != nil {
			err = fmt.Errorf("firstTitleList to cell name error: %s", err)
			return
		}
		xlsx.SetCellValue(sheet, axis, title)
	}

	// write thead
	col = 0
	for _, list := range secondTitleList {
		for _, subTitle := range list {
			var axis string
			axis, err = excelize.CoordinatesToCellName(col+1, 2)
			if err != nil {
				err = fmt.Errorf("secondTitleList to cell name error: %s", err)
				return
			}
			col++
			xlsx.SetCellValue(sheet, axis, subTitle)
		}
	}

	// write tbody
	for i := 0; i < n; i++ {
		v := valTbody.Index(i)
		if v.Kind() == reflect.Ptr {
			v = v.Elem()
		}
		if v.Kind() != reflect.Struct {
			continue
		}
		numField := v.NumField() // number of fields in struct

		c := 0
		for j := 0; j < numField; j++ {
			f := v.Field(j)
			t := v.Type().Field(j)
			if !ast.IsExported(t.Name) {
				continue
			}
			tags := ParseTagSetting(t.Tag, "excel")
			// is ignored field
			if _, ok := tags["-"]; ok {
				continue
			}
			c += 1
			var axis string
			axis, err = excelize.CoordinatesToCellName(c, i+3)
			if err != nil {
				err = fmt.Errorf("tbody to cell name error: %s", err)
				return
			}
			xlsx.SetCellValue(sheet, axis, f.Interface())
		}
	}
	xlsx.SetActiveSheet(index)
	r, err = xlsx.WriteToBuffer()
	if err != nil {
		err = fmt.Errorf("write to buffer error: %s", err)
		return
	}
	return
}

// ParseTagSetting get model's field tags
func ParseTagSetting(tags reflect.StructTag, key ...string) map[string]string {
	setting := map[string]string{}
	for _, v := range key {
		str := tags.Get(v)
		if len(str) == 0 {
			continue
		}
		tags := strings.Split(str, ";")
		for _, value := range tags {
			if len(value) == 0 {
				continue
			}
			v := strings.Split(value, ":")
			k := strings.TrimSpace(strings.ToUpper(v[0]))
			if len(v) >= 2 {
				setting[k] = strings.Join(v[1:], ":")
			} else {
				setting[k] = k
			}
		}
	}
	return setting
}

func ExportMultiMapToExcel(firstTitleList []string, secondTitleList [][]string, tbody [][]interface{}) (r *bytes.Buffer, err error) {
	valTbody := reflect.ValueOf(tbody)
	if valTbody.Kind() != reflect.Slice {
		err = fmt.Errorf("tbody not slice")
		return
	}
	if len(firstTitleList) != len(secondTitleList) {
		err = fmt.Errorf("title len not equal")
		return
	}
	var (
		// it's a slice, so open up its values
		n     = valTbody.Len()
		sheet = "Sheet1"
	)

	xlsx := excelize.NewFile()
	index := xlsx.NewSheet(sheet)

	col := 0
	for k, title := range firstTitleList {
		if k > 0 {
			col += len(secondTitleList[k-1])
		}
		start, er := excelize.ColumnNumberToName(col + 1)
		if er != nil {
			err = fmt.Errorf("firstTitleList to cell name error: %s", er)
			return
		}
		end, er := excelize.ColumnNumberToName(col + len(secondTitleList[k]))
		if er != nil {
			err = fmt.Errorf("firstTitleList to cell name error: %s", er)
			return
		}
		xlsx.MergeCell(sheet, fmt.Sprintf("%s1", start), fmt.Sprintf("%s1", end))
		var axis string
		axis, err = excelize.CoordinatesToCellName(col+1, 1)
		if err != nil {
			err = fmt.Errorf("firstTitleList to cell name error: %s", err)
			return
		}
		xlsx.SetCellValue(sheet, axis, title)
	}

	// write thead
	col = 0
	for _, list := range secondTitleList {
		for _, subTitle := range list {
			var axis string
			axis, err = excelize.CoordinatesToCellName(col+1, 2)
			if err != nil {
				err = fmt.Errorf("secondTitleList to cell name error: %s", err)
				return
			}
			col++
			xlsx.SetCellValue(sheet, axis, subTitle)
		}
	}

	// i行 c列
	for i := 0; i < n; i++ {
		c := 0
		for _, value := range tbody[i] {
			c++
			var axis string
			axis, err = excelize.CoordinatesToCellName(c, i+3)
			if err != nil {
				err = fmt.Errorf("tbody to cell name error: %s", err)
				return
			}
			xlsx.SetCellValue(sheet, axis, value)
		}
	}
	xlsx.SetActiveSheet(index)
	r, err = xlsx.WriteToBuffer()
	if err != nil {
		err = fmt.Errorf("write to buffer error: %s", err)
		return
	}
	return
}
