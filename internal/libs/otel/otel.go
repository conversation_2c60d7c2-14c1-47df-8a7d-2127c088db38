package otel

import (
	"context"
	"fmt"
	"futures-asset/configs"

	cfg "yt.com/backend/common.git/config"
	"yt.com/backend/common.git/monitoring/otelinit"
)

func New(ctx context.Context, conf *cfg.Config[configs.Config]) (func(ctx context.Context) error, error) {
	shutdown, err := otelinit.StartTrace(ctx, &otelinit.Config{
		ServiceName: fmt.Sprintf("%s-%s", conf.Env.ServiceName, conf.Env.Env),
		Host:        conf.Observability.Otel.Host,
		Port:        conf.Observability.Otel.Port,
		IsSecure:    conf.Observability.Otel.IsSecure,
		Exporter:    conf.Observability.Otel.Exporter,
	})
	if err != nil {
		return nil, fmt.Errorf("otelinit.StartTrace error: %w", err)
	}

	return shutdown, nil
}
