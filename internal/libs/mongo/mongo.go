package mongo

import (
	"fmt"
	"futures-asset/configs"

	"go.mongodb.org/mongo-driver/mongo"
	cfg "yt.com/backend/common.git/config"
	"yt.com/backend/common.git/database/mongoinit"
)

func New(conf *cfg.Config[configs.Config]) (*mongo.Database, error) {
	dbConf := &mongoinit.Config{
		Username:                         conf.MongoDB.Username,
		Password:                         conf.MongoDB.Password,
		Hosts:                            conf.MongoDB.Hosts,
		DatabaseName:                     conf.MongoDB.DatabaseName,
		ReplicaName:                      conf.MongoDB.ReplicaName,
		MaxStaleness:                     conf.MongoDB.MaxStaleness,
		MaxPoolSize:                      conf.MongoDB.MaxPoolSize,
		MinPoolSize:                      conf.MongoDB.MinPoolSize,
		MaxConnIdleTime:                  conf.MongoDB.MaxConnIdleTime,
		Compressors:                      conf.MongoDB.Compressors,
		EnableStandardReadWriteSplitMode: conf.MongoDB.EnableStandardReadWriteSplitMode,
	}

	mongoInit := mongoinit.New(
		dbConf,
		mongoinit.SetCustomDecimalType(),
	)

	mongoCli, err := mongoInit.Conn()
	if err != nil {
		return nil, fmt.Errorf("failed to initiate mongodb connection pool: %w", err)
	}

	return mongoCli, nil
}
