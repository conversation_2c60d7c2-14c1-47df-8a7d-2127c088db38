package amqpd

import (
	"fmt"

	"github.com/streadway/amqp"
)

var (
	SpotConn       *amqp.Connection
	ContractConn   *amqp.Connection
	SpotConfig     MqConnect
	ContractConfig MqConnect
)

type (
	MqConnect struct {
		User string
		Pwd  string
		Host string
		Port string
	}
)

func Init(user, pwd, host, port string, isContract bool) (err error) {
	if isContract {
		ContractConfig.User = user
		ContractConfig.Pwd = pwd
		ContractConfig.Host = host
		ContractConfig.Port = port
		ContractConn, err = amqp.Dial(fmt.Sprintf("amqp://%s:%s@%s:%s/",
			user,
			pwd,
			host,
			port))
	} else {
		SpotConfig.User = user
		SpotConfig.Pwd = pwd
		SpotConfig.Host = host
		SpotConfig.Port = port
		SpotConn, err = amqp.Dial(fmt.Sprintf("amqp://%s:%s@%s:%s/",
			user,
			pwd,
			host,
			port))
	}
	if err != nil {
		err = fmt.Errorf("failed amqpd connect %s", err)
		return
	}
	return
}
