package amqpd

import (
	"fmt"
	"os"
	"time"
)

type entry struct {
	Queue    string
	Consumer string
	Handler  func([]byte) error
}

type AmqpdConsumer struct {
	running bool
	entries []*entry
	clis    []*Amqpd
}

func NewAmqpdConsumer() *AmqpdConsumer {
	o := &AmqpdConsumer{
		running: true,
	}
	return o
}

func (this *AmqpdConsumer) AddFunc(queue, consumer string, fn func([]byte) error) {
	this.entries = append(this.entries, &entry{
		Queue:    queue,
		Consumer: consumer,
		Handler:  fn,
	})
	return
}

func (this *AmqpdConsumer) Start(isContract bool) {
	for _, entry := range this.entries {
		go this.run(entry, isContract)
	}
	return
}

func (this *AmqpdConsumer) run(entry *entry, isContract bool) {
	for this.running {
		cli, err := New(isContract)
		if err != nil {
			fmt.Fprintf(os.<PERSON>derr, "amqpd connect err, %s\n", err)
			time.Sleep(time.Second * 5)
			continue
		}
		this.clis = append(this.clis, cli)
		if err = cli.Consume(entry.Queue, entry.Consumer, entry.Handler); err != nil {
			fmt.Fprintf(os.Stderr, "amqpd consume err, %s\n", err)
			time.Sleep(time.Second * 5)
			continue
		}
		cli.Close()
		time.Sleep(time.Second)
	}
	return
}

func (this *AmqpdConsumer) Stop() (err error) {
	this.running = false
	for _, cli := range this.clis {
		cli.Close()
	}
	return
}
