package amqpd

import (
	"errors"
	"fmt"

	"github.com/streadway/amqp"
)

type Amqpd struct {
	channel *amqp.Channel
}

func New(isContract bool) (r *Amqpd, err error) {
	if isContract {
		err = reConnect(ContractConfig.User, ContractConfig.Pwd, ContractConfig.Host, ContractConfig.Port, isContract)
	} else {
		err = reConnect(SpotConfig.User, SpotConfig.Pwd, SpotConfig.Host, SpotConfig.Port, isContract)
	}
	if err != nil {
		err = fmt.Errorf("re connect: %s", err)
		return
	}
	var chann *amqp.Channel
	if isContract {
		chann, err = ContractConn.Channel()
	} else {
		chann, err = SpotConn.Channel()
	}
	if err != nil {
		err = fmt.Errorf("failed to open a channel %s", err)
		return
	}
	r = &Amqpd{channel: chann}
	return
}

func reConnect(user, pwd, host, port string, isContract bool) (err error) {
	var Connection *amqp.Connection
	if isContract {
		Connection = ContractConn
	} else {
		Connection = SpotConn
	}
	if Connection == nil {
		err = errors.New("amqpd not init")
		return
	}
	for i := 0; i < 5; i++ {
		if !Connection.IsClosed() {
			return
		}
		err = Init(user, pwd, host, port, isContract)
		if err != nil {
			err = fmt.Errorf("amqpd connection error: %s", err)
			return
		}
	}
	err = errors.New("amqpd retry fail")
	return
}

func (this *Amqpd) Close() error {
	return this.channel.Close()
}

// ExchangeDeclare
func (this *Amqpd) ExchangeDeclare(name string, kind string, durable bool) (err error) {
	return this.channel.ExchangeDeclare(name, kind, durable, false, false, false, nil)
}

// Publish
func (this *Amqpd) Publish(exchange, key string, body []byte) (err error) {
	return this.channel.Publish(exchange, key, false, false,
		amqp.Publishing{ContentType: "text/plain", Body: body})
}

// QueueDeclare
func (this *Amqpd) QueueDeclare(name string, durable bool) (err error) {
	_, err = this.channel.QueueDeclare(name, durable, false, false, false, nil)
	return
}

// QueueBind
func (this *Amqpd) QueueBind(name, key, exchange string) (err error) {
	return this.channel.QueueBind(name, key, exchange, false, nil)
}

// Consume
func (this *Amqpd) Consume(queue, consumer string, handler func([]byte) error) (err error) {
	var msgs <-chan amqp.Delivery
	msgs, err = this.channel.Consume(queue, consumer, false, false, false, false, nil)
	if err != nil {
		return
	}
	for dely := range msgs {
		err := handler(dely.Body)
		if err != nil {
			dely.Reject(true)
			continue
		}
		dely.Ack(false)
	}
	return
}
