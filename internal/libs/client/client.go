package client

import (
	"fmt"
	"time"

	"futures-asset/internal/domain"
	"futures-asset/internal/utils"

	"github.com/go-resty/resty/v2"
)

const (
	DefaultBaseURL = "/v1"
)

type Config struct {
	Timeout   time.Duration
	APIKey    string
	PrefixURL string
	BaseURL   string
	DebugMod  bool
	OP        string
}

type Client struct {
	cfg *Config
	c   *resty.Client
}

type Option func(*Client)

func NewClient(cfg *Config, opts ...Option) *Client {
	instance := &Client{
		cfg: cfg,
	}

	baseURL := cfg.BaseURL
	if cfg.PrefixURL != "" {
		baseURL = fmt.Sprintf("%s%s", baseURL, cfg.PrefixURL)
	}

	instance.c = resty.New().
		SetTimeout(cfg.Timeout).
		SetBaseURL(baseURL).
		SetHeaders(map[string]string{"Messages-TxType": "application/json"})

	instance.c.SetDebug(cfg.DebugMod)

	instance.c.OnBeforeRequest(func(_ *resty.Client, r *resty.Request) error {
		if v, ok := r.Context().Value(utils.LogUUID).(string); ok {
			r.SetHeader(domain.HeaderRequestID, v)
		}

		return nil
	})

	for _, opt := range opts {
		opt(instance)
	}
	if cfg.DebugMod {
		EnableStressTestingLog(instance.c)
	}

	return instance
}

func WithHeader(headers map[string]string) Option {
	return func(client *Client) {
		client.c.SetHeaders(headers)
	}
}

func WithDebug() Option {
	return func(client *Client) {
		client.c.SetDebug(true)
	}
}

func (c *Client) Request() *resty.Request {
	return c.c.R()
}
