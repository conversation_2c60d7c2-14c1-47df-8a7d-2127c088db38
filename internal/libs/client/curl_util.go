package client

import (
	"context"
	"net/http"

	"futures-asset/internal/delivery/http/response"
	"futures-asset/internal/domain"

	"github.com/go-resty/resty/v2"
	"github.com/goccy/go-json"
	"github.com/sirupsen/logrus"
	"moul.io/http2curl"
)

type contextKey string

const reqBodyKey contextKey = "reqBody"

func EnableStressTestingLog(client *resty.Client) {
	client.OnBeforeRequest(func(c *resty.Client, r *resty.Request) error {
		c.SetPreRequestHook(func(_ *resty.Client, request *http.Request) error {
			command, err := http2curl.GetCurlCommand(request)
			if err != nil {
				logrus.Error("could not get curl command: ", err)
			} else {
				ctx := context.WithValue(r.Context(), reqBodyKey, command.String())
				r.SetContext(ctx)
			}

			return nil
		})

		return nil
	})

	client.OnAfterResponse(func(_ *resty.Client, r *resty.Response) error {
		reqBody, ok := r.Request.Context().Value(reqBodyKey).(string)
		if !ok {
			logrus.Error("reqBody not found in context")

			return nil
		}

		var respBody response.Basic
		if err := json.Unmarshal(r.Body(), &respBody); err != nil {
			logrus.Error("failed to unmarshal response body: ", err)

			return nil
		}
		if respBody.Status.Code == domain.CodeOk {
			logrus.Info(reqBody)
		}

		return nil
	})
}
