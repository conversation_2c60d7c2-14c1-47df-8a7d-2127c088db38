package redis

import (
	"context"

	"futures-asset/configs"

	"github.com/go-redsync/redsync/v4"
	"github.com/redis/go-redis/v9"
	cfg "yt.com/backend/common.git/config"
	"yt.com/backend/common.git/database/redisinit"
)

func New(ctx context.Context, conf *cfg.Config[configs.Config]) (*redis.ClusterClient, error) {
	rdb, err := redisinit.NewClusterClient(ctx, &redisinit.Config{
		Host:            conf.Redis.Host,
		Port:            conf.Redis.Port,
		Username:        conf.Redis.Username,
		Password:        conf.Redis.Password,
		DialTimeout:     conf.Redis.DialTimeout,
		ReadTimeout:     conf.Redis.ReadTimeout,
		WriteTimeout:    conf.Redis.WriteTimeout,
		PoolSize:        conf.Redis.PoolSize,
		MinIdleConns:    conf.Redis.MinIdleConns,
		MaxIdleConns:    conf.Redis.MaxIdleConns,
		ConnMaxIdleTime: conf.Redis.ConnMaxIdleTime,
	})
	if err != nil {
		return nil, err
	}

	return rdb, nil
}

func NewMutex(ctx context.Context, conf *cfg.Config[configs.Config]) (*redsync.Redsync, error) {
	rs, err := redisinit.NewRedSyncClusterClient(ctx, &redisinit.Config{
		Host:            conf.Redis.Host,
		Port:            conf.Redis.Port,
		Username:        conf.Redis.Username,
		Password:        conf.Redis.Password,
		DialTimeout:     conf.Redis.DialTimeout,
		ReadTimeout:     conf.Redis.ReadTimeout,
		WriteTimeout:    conf.Redis.WriteTimeout,
		PoolSize:        conf.Redis.PoolSize,
		MinIdleConns:    conf.Redis.MinIdleConns,
		MaxIdleConns:    conf.Redis.MaxIdleConns,
		ConnMaxIdleTime: conf.Redis.ConnMaxIdleTime,
	})
	if err != nil {
		return nil, err
	}

	return rs, nil
}
