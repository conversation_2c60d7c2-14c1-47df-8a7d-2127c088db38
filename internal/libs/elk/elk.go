package elk

import (
	"context"
	"fmt"
	"net"
	"net/http"

	"futures-asset/configs"

	"github.com/olivere/elastic/v7"
	cfg "yt.com/backend/common.git/config"
)

func New(ctx context.Context, conf *cfg.Config[configs.Config]) (*elastic.Client, error) {
	transport := &http.Transport{
		DialContext:           (&net.Dialer{Timeout: conf.Elasticsearch.DialerTimeout}).DialContext,
		MaxIdleConnsPerHost:   conf.Elasticsearch.MaxIdleConnsPerHost,
		IdleConnTimeout:       conf.Elasticsearch.IdleConnTimeout,
		TLSHandshakeTimeout:   conf.Elasticsearch.TLSHandshakeTimeout,
		ResponseHeaderTimeout: conf.Elasticsearch.ResponseHeaderTimeout,
		ExpectContinueTimeout: conf.Elasticsearch.ExpectContinueTimeout,
	}

	client, err := elastic.NewClient(
		elastic.SetURL(conf.Elasticsearch.URL...),
		elastic.SetBasicAuth(conf.Elasticsearch.UserName, conf.Elasticsearch.Password),
		elastic.SetSniff(false),
		elastic.SetHttpClient(&http.Client{Transport: transport}),
	)
	if err != nil {
		return nil, fmt.Errorf("elastic.NewClient error: %w", err)
	}

	for _, url := range conf.Elasticsearch.URL {
		_, _, err = client.Ping(url).Do(ctx)
		if err != nil {
			return nil, fmt.Errorf("elastic.client.ping [%s] error: %w", url, err)
		}
	}

	return client, nil
}
