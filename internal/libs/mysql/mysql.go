package mysql

import (
	"fmt"

	"gorm.io/gorm"

	"futures-asset/configs"

	cfg "yt.com/backend/common.git/config"
	"yt.com/backend/common.git/database/mysqlinit"
)

func New(conf *cfg.Config[configs.Config]) (*gorm.DB, error) {
	dbConf := &mysqlinit.Config{
		User:              conf.DB.Master.UserName,
		Password:          conf.DB.Master.Password,
		Host:              conf.DB.Master.Host,
		Port:              conf.DB.Master.Port,
		Database:          conf.DB.Master.Name,
		MaxIdleConns:      conf.DB.Master.MaxIdleConns,
		MaxOpenConns:      conf.DB.Master.MaxOpenConns,
		ConnMaxLifetime:   conf.DB.Master.ConnMaxLifetime,
		InterpolateParams: true,

		Logger: mysqlinit.Logger{
			IgnoreRecordNotFoundError: conf.DB.IgnoreRecordNotFoundError,
		},
	}

	dbInit := mysqlinit.New(dbConf, mysqlinit.WithLogLevel(conf.Env.Log.Level), mysqlinit.WithPerformance())

	orm, err := dbInit.Conn()
	if err != nil {
		return nil, fmt.Errorf("failed to initiate db connection pool: %w", err)
	}

	return orm, nil
}
