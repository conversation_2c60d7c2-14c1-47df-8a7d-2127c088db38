package kafka

import (
	"fmt"

	"futures-asset/configs"

	"github.com/IBM/sarama"
	"github.com/sirupsen/logrus"
	cfg "yt.com/backend/common.git/config"
	"yt.com/backend/common.git/kafka"
)

type Cluster struct {
	cluster *kafka.Cluster
	config  cfg.Kafka

	retentionMs     string
	retentionMemory string
	segmentBytes    string
}

const (
	defaultRetentionMs     = "1800000"   // 預設30分鐘
	defaultRetentionMemory = "524288000" // 預設512MB
	defaultSegmentBytes    = "268435456" // 預設256MB
)

func NewCluster(config *cfg.Config[configs.Config]) (*Cluster, error) {
	cluster, err := kafka.NewCluster(&kafka.Config{
		ClientID:          config.Kafka.ClientID,
		Brokers:           config.Kafka.Brokers,
		ChannelBufferSize: config.Kafka.ChannelBufferSize,
		SASL: kafka.SASL{
			Enable:    config.Kafka.SASL.Enable,
			Mechanism: sarama.SASLMechanism(config.Kafka.SASL.Mechanism),
			User:      config.Kafka.SASL.User,
			Password:  config.Kafka.SASL.Password,
		},
	})
	if err != nil {
		return nil, fmt.Errorf("kafka.NewCluster error: %w", err)
	}

	retentionMs := config.CustomConfig.Kafka.TopicRetentionPolicy.RetentionMs
	if retentionMs == "" {
		retentionMs = defaultRetentionMs
	}

	retentionMemory := config.CustomConfig.Kafka.TopicRetentionPolicy.RetentionMemory
	if retentionMemory == "" {
		retentionMemory = defaultRetentionMemory
	}

	segmentBytes := config.CustomConfig.Kafka.TopicRetentionPolicy.SegmentBytes
	if segmentBytes == "" {
		segmentBytes = defaultSegmentBytes
	}

	return &Cluster{
		cluster:         cluster,
		config:          config.Kafka,
		retentionMs:     retentionMs,
		retentionMemory: retentionMemory,
		segmentBytes:    segmentBytes,
	}, nil
}

// CreateTopics 建立多個topic
func (use *Cluster) CreateTopics(brokers []string, topics []string) error {
	// 只有client類型需要有Topic保留政策
	needRetentionPolicy := []string{
		"orchid-client-orderBook",
		"orchid-client-order",
		"orchid-client-position",
		"orchid-client-close-position",
		"orchid-client-profit"}

	list := make([]kafka.TopicRetentionPolicy, 0, len(topics))
	for _, topic := range topics {
		topicPolicy := kafka.TopicRetentionPolicy{
			Topic: topic,
		}

		// 檢查是否需要保留策略
		if contains(needRetentionPolicy, topic) {
			topicPolicy.RetentionMs = use.retentionMs
			topicPolicy.RetentionMemory = use.retentionMemory
		}

		list = append(list, topicPolicy)
	}

	return use.cluster.CreateTopics(brokers, kafka.Topics{List: list})
}

// UpdateTopicsConfig 更新topic config
func (use *Cluster) UpdateTopicsConfig() {
	// TODO 改成通用版本
	// 只有client類型需要有Topic保留政策
	needRetentionPolicy := []string{
		"orchid-client-orderBook",
		"orchid-client-order",
		"orchid-client-position",
		"orchid-client-close-position",
		"orchid-client-profit"}

	topicConfigs := map[string]string{
		"retention.ms":    use.retentionMs,
		"retention.bytes": use.retentionMemory,
		"segment.bytes":   use.segmentBytes,
	}

	for _, topic := range needRetentionPolicy {
		if err := use.cluster.UpdateTopicConfig(topic, topicConfigs); err != nil {
			logrus.WithFields(logrus.Fields{
				"err": err,
			}).Error("cluster.UpdateTopicConfig error")

			continue
		}
	}
}

func contains(slice []string, item string) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}

type Topic struct {
	Name string
	sarama.TopicDetail
}

// Topics 取得consumer/producer topic列表資料
func (use *Cluster) Topics() ([]Topic, []Topic, error) {
	var consumer []Topic
	for _, topics := range use.config.Consumer.Topic {
		for _, topic := range topics {
			v, ok, err := use.cluster.GetTopic(topic)
			if err != nil {
				return nil, nil, fmt.Errorf("consumer getTopic [%s] error: %w", topic, err)
			}

			if ok {
				consumer = append(consumer, Topic{Name: topic, TopicDetail: v})
			}
		}
	}

	var producer []Topic
	for _, topic := range use.config.Producer.Topic {
		v, ok, err := use.cluster.GetTopic(topic)
		if err != nil {
			return nil, nil, fmt.Errorf("producer getTopic [%s] error: %w", topic, err)
		}

		if ok {
			producer = append(producer, Topic{Name: topic, TopicDetail: v})
		}
	}

	return consumer, producer, nil
}
