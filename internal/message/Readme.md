# 推送服务消息队列格式

```txt
_prefix_Ticker_topic       = "%v.market.ticker:%v"
_prefix_AllTicker_topic    = "%v.market.all_ticker"
_prefix_RisingRank_topic   = "%v.market.rising_rank"
_prefix_LastTrade_topic    = "%v.market.last_trade:%v"
_prefix_MarkPrice_topic    = "%v.market.mark_price:%v"
_prefix_AllMarkPrice_topic = "%v.market.all_mark_price"
_prefix_FundingRate_topic  = "%v.market.funding_rate:%v"
_prefix_KLine_topic        = "%v.market.kline:%v:%v"
_prefix_Depth_topic        = "%v.market.depth:%v:%v"
_prefix_Orders_topic       = "%v.accounts"
_prefix_Orders_topic       = "%v.orders:%v"
_prefix_Trades_topic       = "%v.trades:%v"
_prefix_Positions_topic    = "%v.positions:%v"
_prefix_Bills_topic        = "%v.bills:%v"
```

```golang
func GetTickerTopic(_accountType string, _base string, _quote string) string {
    return fmt.Sprintf(_prefix_Ticker_topic, _accountType, FormatPair(_base, _quote))
}

func GetAllTickerTopic(_accountType string) string {
    return fmt.Sprintf(_prefix_AllTicker_topic, _accountType)
}

func GetRisingRankTopic(_accountType string) string {
    return fmt.Sprintf(_prefix_RisingRank_topic, _accountType)
}

func GetLastTradeTopic(_accountType string, _base string, _quote string) string {
    return fmt.Sprintf(_prefix_LastTrade_topic, _accountType, FormatPair(_base, _quote))
}

func GetKLineTopic(_accountType string, _base string, _quote string, _period string) string {
    return fmt.Sprintf(_prefix_KLine_topic, _accountType, FormatPair(_base, _quote), _period)
}

func GetDepthTopic(_accountType string, _base string, _quote string, _step string) string {
    return fmt.Sprintf(_prefix_Depth_topic, _accountType, FormatPair(_base, _quote), _step)
}

func GetMarkPriceTopic(_accountType string, _base string, _quote string) string {
    return fmt.Sprintf(_prefix_MarkPrice_topic, _accountType, FormatPair(_base, _quote))
}

func GetAllMarkPriceTopic(_accountType string) string {
    return fmt.Sprintf(_prefix_AllMarkPrice_topic, _accountType)
}

func GetFundingRateTopic(_accountType string, _base string, _quote string) string {
    return fmt.Sprintf(_prefix_FundingRate_topic, _accountType, FormatPair(_base, _quote))
}

func GetOrdersTopic(_accountType string, _base string, _quote string) string {
    return fmt.Sprintf(_prefix_Orders_topic, _accountType, FormatPair(_base, _quote))
}

func GetTradesTopic(_accountType string, _base string, _quote string) string {
    return fmt.Sprintf(_prefix_Trades_topic, _accountType, FormatPair(_base, _quote))
}

func GetPositionsTopic(_accountType string, _base string, _quote string) string {
    return fmt.Sprintf(_prefix_Positions_topic, _accountType, FormatPair(_base, _quote))
}

func GetBillsTopic(_accountType string, _base string, _quote string) string {
    return fmt.Sprintf(_prefix_Bills_topic, _accountType, FormatPair(_base, _quote))
}
```
