package domain

const (
	UserTypeNormal        int32 = 1  // 普通用户
	UserTypeMedia         int32 = 2  // 媒体渠道
	UserTypeGroup         int32 = 3  // 社区合伙人
	UserTypeProject       int32 = 4  // 项目方
	UserTypeStaff         int32 = 5  // 企业员工
	UserTypePlatformRobot int32 = 6  // 平台机器人
	UserTypeQuantify      int32 = 7  // 量化团队
	UserTypeSeniorAgent   int32 = 8  // 高级代理人
	UserTypeAgent         int32 = 9  // 普通代理人
	UserTypeManager       int32 = 10 // 企业经理
)

// 判断是否为代理用户
func IsAgentUser(usertype int32) bool {
	return usertype == UserTypeAgent || usertype == UserTypeSeniorAgent
}
