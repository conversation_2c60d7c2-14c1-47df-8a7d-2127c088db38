package domain

// U 合约支持的币种 USDT 和USDC
const (
	CurrencyUSDT  = "USDT"
	CurrencyUSDC  = "USDC"
	CurrencyBTC   = "BTC"
	CurrencyETH   = "ETH"
	CurrencyADA   = "ADA"
	CurrencyBCH   = "BCH"
	CurrencyDOT   = "DOT"
	CurrencyETC   = "ETC"
	CurrencyLINK  = "LINK"
	CurrencyLTC   = "LTC"
	CurrencyMATIC = "MATIC"
	CurrencySOL   = "SOL"
	CurrencyUSD   = "USD" // 联合保证金 都转到USD 去冻
)

// CurrencyList 钱包支持的币种列表 目前只支持以下4个币种，之后会按照需求修改
var CurrencyList = []string{
	CurrencyUSDT, CurrencyUSDC, CurrencyBTC, CurrencyETH,
	CurrencyADA, CurrencyBCH, CurrencyDOT, CurrencyETC,
	CurrencyLINK, CurrencyLTC, CurrencyMATIC, CurrencySOL,
}

// 永续合约下单类型 (OrderType)
const (
	OrderTypePostOnly = 7 // 只做Maker

	OrderTypeNormal         = 10 // 普通订单
	OrderTypePlan           = 20 // 计划委托
	OrderTypeTakeProfitAll  = 31 // 全部止盈
	OrderTypeTakeProfitPart = 32 // 部分止盈
	OrderTypeStopLossAll    = 36 // 全部止损
	OrderTypeStopLossPart   = 37 // 部分止损
)

// 永续合约下单类型 (IsLimitOrder)
const (
	Market = 2
	Limit  = 1
)

// LiquidationType 永续合约强平类型 (LiquidationType)
type LiquidationType int32

const (
	LiquidationTypeNone             LiquidationType = 0 // 0.无状态
	LiquidationTypeBurst            LiquidationType = 1 // 1.爆仓(撮合成交、直接成交(自己和他人对手方强制减仓))
	LiquidationTypeReduce           LiquidationType = 2 // 2.减仓(直接成交(自己和自己)、撮合成交(自己和自己、自己和他人))
	LiquidationTypeStopProfitReduce LiquidationType = 3 // 3.止盈减仓(直接成交(自己和他人对手方强制减仓))
	LiquidationTypeTrialReduce      LiquidationType = 4 // 4.体验金减仓
	LiquidationTypeDisableSymbol    LiquidationType = 5 // 5.禁用币对减仓
	LiquidationTypeRobotSelfReduce  LiquidationType = 6 // 6.机器人自减仓
	LiquidationTypeBurstSelfReduce  LiquidationType = 7 // 7.最后一档爆仓自成交减仓
)

// CancelType 永续合约撤单类型 (CancelType)
type CancelType int32

const (
	CancelTypeUser                 CancelType = 1  // 用户撤单
	CancelTypeSystem               CancelType = 2  // 系统撤单
	CancelTypeOperator             CancelType = 3  // 运营撤单
	CancelTypeBurst                CancelType = 5  // 爆仓撤单
	CancelTypeReduce               CancelType = 5  // 减仓撤单
	CancelTypeMarketOrderLeft      CancelType = 6  // 市价剩余撤单
	CancelTypeDisableUser          CancelType = 7  // 禁用用户撤单
	CancelTypeDisableSymbol        CancelType = 8  // 禁用币对撤单
	CancelTypeDirectPosLack        CancelType = 9  // 暗撮可平仓位不足撤单
	CancelTypeLockAssetsFail       CancelType = 10 // 锁定资产失败撤单
	CancelTypeFrozenMarginNotEqual CancelType = 11 // 冻结保证金不相等撤单
	CancelTypePostOnlyDeal         CancelType = 12 // 只做Maker立即成交撤单
	CancelTypeTrialRecycle         CancelType = 13 // 体验金回收撤单
	CancelTypeClosePosAll          CancelType = 14 // 全部平仓后撤销止赢止损平仓单
	CancelTypeClosePosLoss         CancelType = 15 // 平仓亏损后, 账户余额不够冻结, 进行撤单
)

// 资产日志类型
const (
	Lock   = 1 // 锁定
	UnLock = 2 // 解锁
)

const (
	Long  = 1 // 多仓
	Short = 2 // 空仓
	Both  = 3 // 双向持仓平 单向持仓代表只减仓
)

const (
	Buy  = 1
	Sell = 2
)

const (
	LongPos  int32 = 1 // 多仓
	ShortPos int32 = 2 // 空仓
	BothPos  int32 = 3 // 单向
)

// 永续合约账单类型
const (
	BillTypeAll                 = 0  // 0 全部
	BillTypeFee                 = 1  // 1 手续费			交易的手续费
	BillTypeFeeTrial            = 2  // 2 手续费（体验金）
	BillTypeFunding             = 3  // 3 资金费用			每天0，8，16 点结算时收
	BillTypeFundingTrial        = 4  // 4 资金费用（体验金）
	BillTypePlatFee             = 5  // 5 强平费			爆仓的时候变
	BillTypePlatFeeTrial        = 6  // 6 强平费(体验金)			爆仓的时候变
	BillTypeReal                = 7  // 7 结算已实现盈亏	用户交易平仓的时候收
	BillTypeRealTrial           = 8  // 8 结算已实现盈亏(体验金)	亏损的时候
	BillTrialAssetAdd           = 9  // 9 体验金领取账单
	BillTrialAssetRecycle       = 10 // 10 体验金回收
	BillTypeInnerIn             = 11 // 11 划转 转入
	BillTypeInnerOut            = 12 // 12 划转 转出
	BillTypeSubsidy             = 13 // 13 穿仓补贴 (爆仓的时候变 不给用户看)
	BillTypeCloseSubsidy        = 14 // 14 平仓穿仓补贴 (手动平仓造成穿仓 不给用户看)
	BillTypeOptionFee           = 15 // 15.期权手续费
	BillTypeOptionFeeReturn     = 16 // 16.期权手续费归还
	BillTypeOptionPremium       = 17 // 17.期权权利金
	BillTypeOptionPremiumReturn = 18 // 18.期权权利金归还
	BillTypeOptionProfitReal    = 19 // 19.用户期权盈利
	BillTypeOptionRealFee       = 20 // 20.期权实收手续费
	BillTypeDeductAdd           = 21 // 21.抵扣金增加
	BillTypeDeductProfitReal    = 22 // 22.盈利转化为抵扣金
	BillTypeSubsidyTrial        = 23 // 23.体验金穿仓补贴
	BillTypeCloseSubsidyTrial   = 24 // 24.体验金平仓穿仓补贴
)

type MarginMode int32

const (
	MarginModeNone     MarginMode = 0
	MarginModeCross    MarginMode = 1
	MarginModeIsolated MarginMode = 2
)

// 持仓模式
const (
	HoldModeHedge = 1 // 双向持仓 (锁仓模式)
	HoldModeBoth  = 2 // 单向持仓
)

// 合约币对状态
const (
	ContractStart      = 1 // 启用
	ContractNoTrade    = 2 // 已禁用 (禁止下单，保持持仓)
	ContractDisablePre = 3 // 禁用(强平后下架) 定时任务待生效
	ContractDisable    = 4 // 禁用
)

const (
	TrialAssetNoReceived     = 0 // 未收到, 或者筛选条件全部
	TrialAssetStatusNoEffect = 1 // 待生效
	TrialAssetStatusEffect   = 2 // 生效中
	TrialAssetStatusWarn     = 3 // 已预警
	TrialAssetStatusInvalid  = 4 // 已失效
	TrialAssetStatusFinish   = 5 // 体验金完全使用
)

const (
	TrialAssetDisplayStatusPending   = 101 // 待生效
	TrialAssetDisplayStatusAvailable = 102 // 可用
	TrialAssetDisplayStatusUsing     = 103 // 使用中
	TrialAssetDisplayStatusRecycled  = 104 // 已回收
	TrialAssetDisplayStatusExpired   = 105 // 已失效
	TrialAssetDisplayStatusFinished  = 106 // 已完全使用
)

const (
	TrialAssetTypeLoop = 1 // 循环
	TrialAssetTypeOnce = 2 // 一次性
)

// TrialAssetTimeType 简称 TATT
const (
	TATimeTypeAll = 0 // 全部
	TATTAward     = 1 // 领取
	TATTEffective = 2 // 生效时间
	TATTInvalid   = 3 // 失效时间
)

const (
	ValTypeCny  = 1
	ValTypeUsdt = 2
	ValTypeBtc  = 3
)

// 后台服务部门的枚举，跟am 的job表 保持一致
const (
	DepartmentAsset   = 1 // 财务部门
	DepartmentService = 2 // 客服部门
	DepartmentOperate = 3 // 运营部门
)

// PosStatus 持仓状态枚举
type PosStatus int32

const (
	PosStatusNone    PosStatus = 0 // 未开始
	PosStatusHolding PosStatus = 1 // 持仓中
	PosStatusEnd     PosStatus = 2 // 已结束
)

// 收支方向
const (
	Income = 1 // 收入
	Outlay = 2 // 支出
)

// 合约类型
const (
	USwap    = 1 // U本位合约
	CoinSwap = 2 // 币本位合约
)

// 成交类型
const (
	TradeTypeMatch  = iota // 撮合成交
	TradeTypeDirect        // 直接成交 (暗成交, 不进撮合直接成交)
)

// 合约币对配置操作类型
const (
	PairOptTypeAdd    = iota + 1 // 1:新增
	PairOptTypeUpdate            // 2:更新
)
