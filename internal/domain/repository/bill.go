package repository

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	"futures-asset/internal/domain/entity"
	"futures-asset/util"

	"github.com/shopspring/decimal"
)

type BillRepository interface {
	InsertAsset(ctx context.Context, billAsset *entity.BillAsset) (err error)
	InsertRobotAsset(ctx context.Context, billAsset *entity.BillAsset) (err error)
	InsertOption(ctx context.Context, billOption *entity.BillOption) (err error)
	InsertFundingFee(ctx context.Context, billFundingFee *entity.LogFundingFee) (err error)
	InsertFundingRate(ctx context.Context, billFundingRate *entity.LogFundingRate) (err error)
	InsertBurst(ctx context.Context, billBurst *entity.LogBurstSwap) (err error)
	InsertRivalBurst(ctx context.Context, billRivalBurst *entity.RivalBurst) (err error)
}

type BillAssetSync struct {
	entity.BillAsset
}

// MarshalBinary implement encoding.BinaryMarshaler for redis
func (slf BillAssetSync) MarshalBinary() ([]byte, error) {
	return json.Marshal(slf)
}

// UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
func (slf BillAssetSync) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, &slf)
}

// NewBillAssetSync 构建账单记账实体
//   - billType: web端展示的账单类型
func NewBillAssetSync(mqTrial *MqCmsAsset, billType int, amount decimal.Decimal) *BillAssetSync {
	billSwap := BillAssetSync{
		BillAsset: entity.BillAsset{
			UID:       mqTrial.UID,
			OperateId: mqTrial.OrderId,
			// BillId:       util.GenerateId(), // TODO snowflakeID
			ContractCode: strings.ToUpper(mqTrial.ContractCode),
			Currency:     strings.ToUpper(mqTrial.Currency),
			BillType:     billType,
			Amount:       amount,
			OperateTime:  time.Now().UnixNano(),
		},
	}
	return &billSwap
}

func NewBillAsset(uid, base, quote, operateId string, _billType int, _amount decimal.Decimal) *BillAssetSync {
	billSwap := BillAssetSync{
		BillAsset: entity.BillAsset{
			UID:       uid,
			OperateId: operateId,
			// BillId:       util.GenerateId(), // TODO snowflakeID
			ContractCode: util.ContractCode(base, quote),
			Currency:     strings.ToUpper(quote),
			BillType:     _billType,
			Amount:       _amount,
			OperateTime:  time.Now().UnixNano(),
		},
	}
	return &billSwap
}
