package repository

import (
	"encoding/json"

	"futures-asset/internal/domain/entity"

	"github.com/shopspring/decimal"
)

// MqCmsAsset send to cms_asset mq model
type MqCmsAsset struct {
	ContractCode     string          `json:"contractCode"`     // 合约代码
	Currency         string          `json:"currency"`         // 资产币种
	OrderId          string          `json:"orderId"`          // 订单ID
	UID              string          `json:"uid"`              // 用户id
	UserType         int32           `json:"userType"`         // 用户类型
	ChannelCode      string          `json:"channelCode"`      // 用户渠道码
	AgentUserId      string          `json:"agentUserId"`      // 代理人id
	AgentStatus      int             `json:"agentStatus"`      // 代理人状态-1:启用,2:禁用
	AgentChannelCode string          `json:"agentChannelCode"` // 代理人渠道码
	RegisterLanguage string          `json:"registerLanguage"` // 用户注册语言
	Platform         string          `json:"platform"`         // 来源 (WEB,APP,H5)
	StrategyType     int32           `json:"strategyType"`     // 策略类型 (0:合约交易)
	DealAmount       decimal.Decimal `json:"dealAmount"`       // 成交仓位数量
	DealPrice        decimal.Decimal `json:"dealPrice"`        // 成交价格
	Amount           decimal.Decimal `json:"amount"`           // 财务记账数量(按照domain.ContractTradeOpenFee等交易类型区分)
	TAsset           CmsTrialAsset   `json:"tAsset"`           // 体验金变化数量
	OperateType      int             `json:"operateType"`      // 操作类型
	OperateTime      int64           `json:"operateTime"`      // 操作时间
	// 只有期权行权时,wallet返佣逻辑中会使用
	OptionId string          `json:"optionId"` // option-期权ID
	Premium  decimal.Decimal `json:"premium"`  // option-权利金总额 (只有期权行权时,wallet返佣逻辑中会使用(通知枚举值为:OptionRealFee-20))
	Period   string          `json:"period"`   // 期权时间粒度 (3分钟, 5分钟) (只有期权行权时,wallet返佣逻辑中使用)
}

type CmsTrialAsset struct {
	TAmount decimal.Decimal      `json:"tAmount"`
	TDetail []*entity.TrialAsset `json:"tDetail"`
}

// MarshalBinary implement encoding.BinaryMarshaler for redis
func (slf MqCmsAsset) MarshalBinary() ([]byte, error) {
	return json.Marshal(slf)
}

// UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
func (slf MqCmsAsset) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, &slf)
}
