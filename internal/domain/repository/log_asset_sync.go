package repository

import (
	"encoding/json"
	"futures-asset/internal/domain/entity"
)

type LogAssetSync struct {
	AssetSwap entity.Wallet
}

// MarshalBinary implement encoding.BinaryMarshaler for redis
func (slf LogAssetSync) MarshalBinary() ([]byte, error) {
	return json.Marshal(slf)
}

// UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
func (slf LogAssetSync) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, &slf)
}
