package repository

import (
	"context"

	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/shopspring/decimal"
)

// TODO 内存缓存更新逻辑补充
type PriceRepository interface {
	GetIndexAndMarkPrice(ctx context.Context, symbol string) (decimal.Decimal, decimal.Decimal)

	GetAllMarkPrice(ctx context.Context) cmap.ConcurrentMap[string, decimal.Decimal]
	GetMarkPrice(ctx context.Context, symbol string) decimal.Decimal
	SetMarkPrice(ctx context.Context, symbol string, price decimal.Decimal)

	GetAllIndexPrice(ctx context.Context) cmap.ConcurrentMap[string, decimal.Decimal]
	GetIndexPrice(ctx context.Context, symbol string) decimal.Decimal
	SetIndexPrice(ctx context.Context, symbol string, price decimal.Decimal)

	GetAllLastPrice(ctx context.Context) cmap.ConcurrentMap[string, decimal.Decimal]
	GetLastPrice(ctx context.Context, symbol string) decimal.Decimal
	SetLastPrice(ctx context.Context, symbol string, price decimal.Decimal)

	SpotURate(ctx context.Context, currency string) decimal.Decimal
	SpotRate(ctx context.Context, base, quote string) decimal.Decimal
}

type MarkPrice struct {
	ContractCode string          `json:"contractCode"`
	MarkPrice    decimal.Decimal `json:"markPrice"`
	IndexPrice   decimal.Decimal `json:"indexPrice"`
}
