package repository

import (
	"context"
	"sync"

	"futures-asset/internal/domain/entity"
	"futures-asset/internal/libs/pager"

	"github.com/shopspring/decimal"
)

type FundingRepository interface {
	FundingFeeList(ctx context.Context, req *FundingFeeListParam) (FundingFeeList, error)

	FundRate(ctx context.Context, contractCode string, cfg ContractPair) decimal.Decimal
	GetFundRateList(ctx context.Context, req *FundRateParam) (FundRateReply, error)
	GetFundRate(ctx context.Context, contractCode string) (FundingRate, error)
	FundRateAll(ctx context.Context) (FundRateAll, error)
	LastFundMap(ctx context.Context, wg *sync.WaitGroup) (r map[string]entity.LogFundingRate)
	AllFundData(ctx context.Context, wg *sync.WaitGroup) (r []FundingRate)

	GetBaseNum(ctx context.Context, key string) int
}

type (
	FundingFeeListParam struct {
		pager.Condition
		AccountType string `json:"accountType"` // 合约类型
		Base        string `json:"base"`        // 交易币
		Quote       string `json:"quote"`       // 计价币
		MarginMode  int    `json:"marginMode"`  // 全仓逐仓
		PosSide     int32  `json:"posSide"`     // 仓位类型 1:多仓 2:空仓
		UID         string `json:"uid"`         // 用户ID
		UserType    []int  `json:"userType"`    // 用户类型
		FundingId   string `json:"fundingId"`   // 资金费用单号
		Direction   int    `json:"direction"`   // 用户资金费用方向 1.收入，2.支出
		StartTime   int64  `json:"startTime"`   // 资金费用收支时间开始
		EndTime     int64  `json:"endTime"`     // 资金费用收支时间结束
		IsExcel     int    `json:"isExcel"`     // 是否导出
	}
	FundingFeeList struct {
		pager.Page
		List []entity.LogFundingFee `json:"list"`
	}
)

type (
	// FundRateParam 资金费率记录
	FundRateParam struct {
		pager.Condition
		Base      string `json:"base"`       // 交易币
		Quote     string `json:"quote"`      // 计价币
		StartTime int64  `json:"start_time"` // 开始时间
		EndTime   int64  `json:"end_time"`   // 结束时间
	}

	FundRateReply struct {
		pager.Page
		List []entity.LogFundingRate `json:"list"`
	}
)

type FundingRate struct {
	ContractCode string          `json:"contract_code"`
	ShowRank     int             `json:"show_rank"`      // 币对展示排位
	FundRate     decimal.Decimal `json:"fund_rate"`      // 实时资金费率
	LastFundRate decimal.Decimal `json:"last_fund_rate"` // 上次传结算资金费率
	LastTime     int64           `json:"last_time"`      // 上次结算时间
	StartTime    int64           `json:"start_time"`
	EndTime      int64           `json:"end_time"`
}

type FundRateAll struct {
	FundList  []FundingRate `json:"fund_list"`
	StartTime int64         `json:"start_time"`
	EndTime   int64         `json:"end_time"`
}
