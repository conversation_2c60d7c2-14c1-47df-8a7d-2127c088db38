package repository

import (
	"encoding/json"

	"futures-asset/internal/domain/entity"
)

type LogPosSync struct {
	PosSwap entity.Position // 存库
	Pos     PosSwap         // 用于推送
}

// MarshalBinary implement encoding.BinaryMarshaler for redis
func (slf LogPosSync) MarshalBinary() ([]byte, error) {
	return json.Marshal(slf)
}

// UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
func (slf LogPosSync) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, &slf)
}
