package repository

import (
	"encoding/json"
	"strings"
	"time"

	"futures-asset/internal/domain/entity"

	"github.com/shopspring/decimal"
)

type BillOptionSync struct {
	entity.BillOption
}

// MarshalBinary implement encoding.BinaryMarshaler for redis
func (slf BillOptionSync) MarshalBinary() ([]byte, error) {
	return json.Marshal(slf)
}

// UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
func (slf BillOptionSync) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, &slf)
}

// NewBillOptionSync 构建期权下单时账单记账实体
//   - billType: web端展示的账单类型
func NewBillOptionSync(uid, base, quote string, billType, accountType, optionType int, amount decimal.Decimal, optionId, orderId string) *BillOptionSync {
	bill := BillOptionSync{
		BillOption: entity.BillOption{
			UID:         uid,
			OptionId:    optionId,
			OperateId:   orderId,
			// BillId:      util.GenerateId(), // TODO snowflakeID
			Base:        strings.ToUpper(base),
			Quote:       strings.ToUpper(quote),
			Currency:    strings.ToUpper(quote),
			BillType:    billType,
			Amount:      amount,
			OperateTime: time.Now().UnixNano(),
			FromAccount: accountType,
			ToAccount:   accountType,
			OptionType:  optionType,
		},
	}
	return &bill
}

// NewBillOptionExerciseSync 构建行权时账单记账实体
//   - billType: web端展示的账单类型
func NewBillOptionExerciseSync(uid, base, quote string, billType, accountType, optionType int, amount, premium decimal.Decimal, optionId, orderId string) *BillOptionSync {
	bill := BillOptionSync{
		BillOption: entity.BillOption{
			UID:         uid,
			OptionId:    optionId,
			OperateId:   orderId,
			// BillId:      util.GenerateId(), // TODO snowflakeID
			Base:        strings.ToUpper(base),
			Quote:       strings.ToUpper(quote),
			Currency:    strings.ToUpper(quote),
			BillType:    billType,
			Amount:      amount,
			OperateTime: time.Now().UnixNano(),
			FromAccount: accountType,
			ToAccount:   accountType,
			OptionType:  optionType,
			Premium:     premium,
		},
	}
	return &bill
}
