package repository

import (
	"context"

	"github.com/shopspring/decimal"
)

type SettingRepository interface {
	// GetMarginRateLevel 从redis获取维持保证金率等级
	GetMarginRateLevel(ctx context.Context, symbol string) (LevelHoldSortList, error)
	// GetPairSettingInfo 从redis获取币对配置
	GetPairSettingInfo(ctx context.Context, symbol string) (*ContractPair, error)
	// GetCachePair 从内存缓存获取币对配置
	GetCachePair(ctx context.Context, symbol string) (*ContractPair, error)
	// GetAllPairSettingInfo 从redis获取所有币对配置
	GetAllPairSettingInfo(ctx context.Context) (map[string]ContractPair, error)
	AllContractPair() (AllContractPairReply, error)

	GetUserLevelsRate() (UserLevelsData, error)
	// FetchMarginLevel 获取保证金等級
	/*
		// returns:
		// 0: LevelFilter 当前等级配置
		// 1: bool 是否是最后一级
		// 2: error 错误信息
	*/
	FetchMarginLevel(ctx context.Context, symbol string, posTotalValue decimal.Decimal) (LevelFilter, bool, error)
	// NextMarginLevel 获取下一个级维持保证金率信息
	/*
		//	Return:
		//	  0 LevelFilter: 仓位等级信息
		//	  1 error: 错误信息
	*/
	NextMarginLevel(ctx context.Context, symbol string, level int) (LevelFilter, error)

	// UpdateAllPairInfo 更新所有setting和level配置，保存到redis
	UpdateAllPairInfo(ctx context.Context) error

	GetCoinWhiteListConfigMap(ctx context.Context) (map[string]CurrencyWhiteListConfig, error)
}

type LevelFilter struct {
	Level             int
	Lever             int
	HighLimit         decimal.Decimal
	LowLimit          decimal.Decimal
	InitMarginRate    decimal.Decimal
	WarnMarginRate    decimal.Decimal
	HoldingMarginRate decimal.Decimal
}

type LevelHoldSortList []LevelFilter

func (ll LevelHoldSortList) Len() int {
	return len(ll)
}

func (ll LevelHoldSortList) Less(i, j int) bool {
	return ll[i].HoldingMarginRate.LessThan(ll[j].HoldingMarginRate)
}

func (ll LevelHoldSortList) Swap(i, j int) {
	ll[i], ll[j] = ll[j], ll[i]
}

type (
	MarginRateLevelInfo struct {
		Gear            int             `json:"gear"`
		Start           decimal.Decimal `json:"start"`
		End             decimal.Decimal `json:"end"`
		LeverMultiple   int             `json:"leverMultiple"`
		InitRate        decimal.Decimal `json:"initRate"`
		WarnRate        decimal.Decimal `json:"warnRate"`
		MaintenanceRate decimal.Decimal `json:"maintenanceRate"`
	}
	ContractPair struct {
		OptType              int                   `json:"optType"`              // 合约币对操作类型 (1:新增 2:更新)
		Base                 string                `json:"base"`                 // 交易币
		Quote                string                `json:"quote"`                // 计价币
		ShowRank             int                   `json:"showRank"`             // 币对展示排位
		MaxLeverage          int                   `json:"leverMultiple"`        // 最大杠杆倍数
		DefaultLeverage      int                   `json:"defaultLeverage"`      // 初始化杠杆倍数
		InitPrice            decimal.Decimal       `json:"initPrice"`            // 初始价格
		AmountPrecision      int32                 `json:"basePrecision"`        // 数量精度(小数位数)
		PricePrecision       int32                 `json:"quotePrecision"`       // 价格精度(小数位数)
		MinAmount            decimal.Decimal       `json:"limitMarketAmount"`    // 现价或市价最小交易数量(据此计算出数量精度)
		RiskRate             decimal.Decimal       `json:"riskRate"`             // 风险准备金率费率
		State                int                   `json:"status"`               // 状态 (1:启用，其他非启用)
		OrderLimitRate       decimal.Decimal       `json:"orderLimitRate"`       // 用户委托下单时，最多成交深度数量的占比
		BuyMinPricePercent   decimal.Decimal       `json:"buyMinPricePercent"`   // 用户委托下单时，委托价格与最新成交价限制区间 最低买入价格百分比
		BuyMaxPricePercent   decimal.Decimal       `json:"buyMaxPricePercent"`   // 用户委托下单时，委托价格与最新成交价限制区间 最高买入价格百分比
		SellMinPricePercent  decimal.Decimal       `json:"sellMinPricePercent"`  // 用户委托下单时，委托价格与最新成交价限制区间 最低卖出价格百分比
		SellMaxPricePercent  decimal.Decimal       `json:"sellMaxPricePercent"`  // 用户委托下单时，委托价格与最新成交价限制区间 最高卖出价格百分比
		Interest             decimal.Decimal       `json:"interest"`             // 基础利率(I)
		MinCapitalFeeRate    decimal.Decimal       `json:"limitsA"`              // 资金费率最小值
		MaxCapitalFeeRate    decimal.Decimal       `json:"limitsB"`              // 资金费率最大值
		MarginRateGear       []MarginRateLevelInfo `json:"marginRateGear"`       // 合约风险等级
		ForceLiquidationTime int64                 `json:"forceLiquidationTime"` // 币对强平后禁用时间
	}
)

type (
	CurrencyWhiteListConfig struct {
		Currency       string               `json:"currency"`
		CurrencyDetail map[string]WhiteList `json:"currencyDetail"`
	}
	WhiteList struct {
		UserWhiteState bool     `json:"userWhiteState"`
		UserWhiteList  []string `json:"userWhiteList"`
	}
)

type (
	AllContractPairReply struct {
		Code int      `json:"code"`
		Msg  string   `json:"msg"`
		Data []string `json:"data"`
	}

	UserLevelInfo struct {
		Level         int             `json:"level"`
		BbMaker       decimal.Decimal `json:"bbMaker"`
		BbTaker       decimal.Decimal `json:"bbTaker"`
		ContractMaker decimal.Decimal `json:"contractMaker"`
		ContractTaker decimal.Decimal `json:"contractTaker"`
	}

	UserLevelsData struct {
		List []UserLevelInfo `json:"levelList"`
	}
)

type LevelLeverSortList []LevelFilter

func (ll LevelLeverSortList) Len() int {
	return len(ll)
}

func (ll LevelLeverSortList) Less(i, j int) bool {
	return ll[i].Lever < ll[j].Lever
}

func (ll LevelLeverSortList) Swap(i, j int) {
	ll[i], ll[j] = ll[j], ll[i]
}

type LevelLowSortList []LevelFilter

func (ll LevelLowSortList) Len() int {
	return len(ll)
}

func (ll LevelLowSortList) Less(i, j int) bool {
	return ll[i].LowLimit.LessThan(ll[j].LowLimit)
}

func (ll LevelLowSortList) Swap(i, j int) {
	ll[i], ll[j] = ll[j], ll[i]
}
