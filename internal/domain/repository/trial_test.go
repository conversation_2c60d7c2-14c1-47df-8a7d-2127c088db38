package repository

import (
	"fmt"
	"testing"

	"futures-asset/internal/domain/entity"

	"github.com/shopspring/decimal"
)

// SubTrial 循环体验金列表减体验金
func TestSubTrial(t *testing.T) {
	tAsset := TrialAsset{
		TrialBalance: decimal.NewFromInt(1000),
		TrialList: []*entity.TrialAsset{
			{
				Amount: decimal.NewFromInt(1),
			},
			{
				Amount: decimal.NewFromInt(2),
			},
			{
				Amount: decimal.NewFromInt(3),
			},
			{
				Amount: decimal.NewFromInt(5),
			},
		},
	}
	tAsset.SubTrial(decimal.NewFromInt(5), nil, nil, nil, 0)
	for _, t := range tAsset.TrialList {
		fmt.Printf("%s", t.Amount)
	}
}
