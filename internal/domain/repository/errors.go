package repository

import (
	"errors"
	"fmt"
	"futures-asset/internal/domain"
	"net/http"
)

var ErrRecordNotFound = errors.New("query record not found")
var ErrBalanceInsufficient = errors.New("balance insufficient")

type CallAPIError struct {
	URL string
	Err error
}

func (err CallAPIError) Error() string {
	return fmt.Sprintf("call api [%s] error: %v", err.URL, err.Err)
}

type APIResponseError struct {
	HTTPStatus int
	Code       domain.Code
	Msg        string
	Err        error
}

func (e APIResponseError) ErrorCode() domain.Code {
	if e.Code <= 0 {
		return domain.InternalError
	}

	return e.Code
}

func (e APIResponseError) Message() string {
	if e.Msg == "" {
		return "unexpected error"
	}

	return e.Msg
}

func (e APIResponseError) Error() string {
	if e.Err == nil {
		return ""
	}

	return e.Err.Error()
}

func (e APIResponseError) HTTPStatusCode() int {
	if e.HTTPStatus <= 0 {
		return http.StatusOK
	}

	return e.HTTPStatus
}

type InsertRecordError struct {
	DBErr error
}

func (e InsertRecordError) Error() string {
	return fmt.Sprintf("failed to insert record into db: %s", e.DBErr)
}

type UpdateRecordError struct {
	DBErr error
}

func (e UpdateRecordError) Error() string {
	return fmt.Sprintf("failed to update record into db: %s", e.DBErr)
}

type QueryRecordError struct {
	DBErr error
}

func (e QueryRecordError) Error() string {
	return fmt.Sprintf("failed to query db: %v", e.DBErr)
}
