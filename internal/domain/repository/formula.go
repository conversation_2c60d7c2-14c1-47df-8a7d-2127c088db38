package repository

import (
	"context"

	"github.com/shopspring/decimal"
)

type FormulaRepository interface {
	CalcLiquidationPrice(ctx context.Context, basePos *PosQuery, longPos, shortPos, bothPos *PosSwap,
		totalIsoMargin, balance, crossOther, crossOtherMargin, markPrice decimal.Decimal,
	) decimal.Decimal
	IsolatedCollapsePrice(pos *PosSwap, takerFeeRate, makerFeeRate decimal.Decimal) (decimal.Decimal, string)
	CrossCollapsePrice(ctx context.Context, asset *AssetSwap, pos *PosSwap, isolatedAllMargin, otherCrossUnreal, otherCrossMargin, takerFeeRate, makeFeeRate decimal.Decimal) (decimal.Decimal, string, error)

	PremiumIndex(ctx context.Context, symbol string, cfg ContractPair) decimal.Decimal
	SetOneFundRate(ctx context.Context, fundRateListKey, symbol string, indexPrice, interest decimal.Decimal)
	GetMaxPosValueWithLeverage(ctx context.Context, symbol string, leverage int) (decimal.Decimal, error)
	MakeMarkPrice(ctx context.Context, contractCode string)
	FundingRate(ctx context.Context, contractCode string, cfg ContractPair) decimal.Decimal
}
