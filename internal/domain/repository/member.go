package repository

import (
	"context"

	"github.com/shopspring/decimal"
)

type MemberRepository interface {
	GetUserLevel(ctx context.Context, uid string) (*UserLevelData, error)
	GetUserLevelRate(ctx context.Context, uid string) (*LevelRate, error)
	GetUserBasic(ctx context.Context, uid string) (*UserBasic, error)
	HasAgree(ctx context.Context, uid string) HasAgreeResp
	RobotList(ctx context.Context) []UserData
}

type (
	UserLevelData struct {
		BbLevel  int    `json:"bbLevel"`
		DictUser int    `json:"dictUser"`
		UID      string `json:"uid"`
	}
	GetUserLevelRet struct {
		Code int           `json:"code"`
		Msg  string        `json:"msg"`
		Data UserLevelData `json:"data"`
	}
)

type (
	LevelRateResult struct {
		Code int       `json:"code"`
		Msg  string    `json:"msg"`
		Data LevelRate `json:"data"`
	}

	/*用户等级及费率*/
	LevelRate struct {
		UID              string          `json:"uid"`
		VIP              int             `json:"vip"`      // 现货等级
		LevelS           int             `json:"levelS"`   // 期货等级
		UserType         int             `json:"userType"` // 用户类型
		UserTypeStatus   int             `json:"userTypeStatus"`
		BbMaker          decimal.Decimal `json:"bbMaker"`          // 币币maker费率
		BbTaker          decimal.Decimal `json:"bbTaker"`          // 币币taker费率
		ContractMaker    decimal.Decimal `json:"contractMaker"`    // 合约maker费率
		ContractTaker    decimal.Decimal `json:"contractTaker"`    // 合约taker费率
		IsRobotFee       int             `json:"isRobotFee"`       // 是否收手续费(机器人)-1:是,2:否'
		ChannelCode      string          `json:"channelCode"`      // 渠道码
		AgentChannelCode string          `json:"agentChannelCode"` // 代理人渠道码
		AgentUserId      string          `json:"agentUserId"`
		RegisterLanguage string          `json:"registerLanguage"`
		OptionLevel      int             `json:"optionLevel"`
		OptionTaker      decimal.Decimal `json:"optionTaker"`
		LastRequestTime  int64           // 最近一次请求时间戳
	}
)

type (
	BasicResult struct {
		Code int       `json:"code"`
		Msg  string    `json:"msg"`
		Data UserBasic `json:"data"`
	}

	/*用户详情表*/
	UserBasic struct {
		UID               string `json:"uid"`               // 用户ID
		RealName          string `json:"realName"`          // 用户真名
		SensitiveRealName string `json:"sensitiveRealName"` // 脱敏的用户真名
		IdCard            string `json:"idCard"`            // 身份证号
		SensitiveIdCard   string `json:"sensitiveIdCard"`   // 脱敏的身份证号
		Sex               int    `json:"sex"`               // 1 男 2 女
		PhonePrefix       string `json:"phonePrefix"`       // 手机号前缀 (中国: 86)
		Phone             string `json:"phone"`             // 手机号
		SensitivePhone    string `json:"sensitivePhone"`    // 脱敏的手机号
		MailAddr          string `json:"mail"`              // 邮箱
		SensitiveMailAddr string `json:"sensitiveMailAddr"` // 脱敏的邮箱
	}
)

type (
	AgreeRes struct {
		Code int          `json:"code"`
		Msg  string       `json:"msg"`
		Data HasAgreeResp `json:"data"`
	}
	HasAgreeResp struct {
		HasAgree int `json:"hasAgree"`
	}
)

type (
	ResRobotList struct {
		Code int        `json:"code"`
		Msg  string     `json:"msg"`
		Data []UserData `json:"data"`
	}
	UserData struct {
		UID string `json:"uid"`
	}
)
