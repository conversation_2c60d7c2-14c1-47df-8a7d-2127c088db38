package domain

// 期权类型
const (
	OptionTypeFirm = 1 // 1.实盘
	OptionTypeDemo = 2 // 2.模拟盘
)

// 期权状态
const (
	OptionStateNoStart    = 1 // 未开始
	OptionStateProcessing = 2 // 进行中
	OptionStateEexercised = 3 // 已行权
	OptionStateCanceled   = 4 // 已撤单
)

// 期权相关Redis Key
const (
	OptionProfitLossData RedisKey = "option:profit:loss:data:" // redis 中存在的合约盈亏记录数据

	SyncListOptionBill    = "option:sync:bill"        // 期权账单同步list
	OptionProfitLossDate  = "option:profit:loss:date" // redis 中存在的利润管理的数据
	OptionTotalProfitHash = "option:total_profit"     // 期权累计盈亏数据
)

const (
	OptionPrefix            = "option:" // option:{uid}
	OptionDemoPrefix        = "option:demo:"
	OptionDemoBalancePrefix = "option:demo:asset"
)

// AllOptionUsersKey all users played option
func AllOptionUsersKey() string {
	return OptionPrefix + RedisAllUser
}
