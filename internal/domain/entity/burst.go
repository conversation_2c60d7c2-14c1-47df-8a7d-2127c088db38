package entity

import (
	"futures-asset/internal/domain"

	"github.com/shopspring/decimal"
)

type StatBurstInfo struct {
	UID       string `json:"uid"`
	BurstNum  int    `json:"burstNum"`  // 爆仓总次数
	ReduceNum int    `json:"reduceNum"` // 减仓总次数
}

// BurstSwap contract swap burst log
type BurstSwap struct {
	Id                   string             `gorm:"id;type:varchar(100);not null" json:"id"`
	BurstId              string             `gorm:"burst_id;type:varchar(30);not null" json:"burst_id"`                              // 爆仓ID (强平单号)
	PosId                string             `gorm:"pos_id;type:varchar(30);not null" json:"pos_id"`                                  // 持仓单ID
	UID                  string             `gorm:"user_id;type:varchar(20);not null" json:"user_id"`                                // 用户ID
	UserType             int                `gorm:"user_type;not null" json:"user_type"`                                             // 用户类型
	AccountType          string             `gorm:"account_type;not null" json:"account_type"`                                       // 合约类型
	Base                 string             `gorm:"base;type:varchar(10);not null" json:"base"`                                      // 交易币 (合约交易对)
	Quote                string             `gorm:"quote;type:varchar(10);not null" json:"quote"`                                    // 计价币 (合约交易对)
	Currency             string             `gorm:"currency;type:varchar(10);not null" json:"currency"`                              // 资产币种 (合约类型)
	OrderType            int                `gorm:"order_type;not null" json:"order_type"`                                           // 委托方式
	MarginMode           domain.MarginMode  `gorm:"margin_mode;type:int(5);not null" json:"margin_mode"`                             // 保证金模式 全仓:1 逐仓:2
	MarginBalance        decimal.Decimal    `gorm:"margin_balance" sql:"type:decimal(30,15);" json:"margin_balance"`                 // 保证金余额
	TotalMargin          decimal.Decimal    `gorm:"total_margin" sql:"type:decimal(30,15);" json:"total_margin"`                     // 保证金
	PosType              int32              `gorm:"pos_type;type:int(5);not null" json:"pos_type"`                                   // 仓位类型 多:1 空:2  (平仓类型)
	PosAmount            decimal.Decimal    `gorm:"pos_amount" sql:"type:decimal(30,15);" json:"pos_amount"`                         // 仓位数量
	PosUnreal            decimal.Decimal    `gorm:"pos_unreal" sql:"type:decimal(30,15);" json:"pos_unreal"`                         // 仓位未实现盈亏
	PosMargin            decimal.Decimal    `gorm:"pos_margin" sql:"type:decimal(30,15);" json:"pos_margin"`                         // 仓位保证金
	PosValue             decimal.Decimal    `gorm:"pos_value" sql:"type:decimal(30,15);" json:"pos_value"`                           // 仓位价值
	Leverage             int                `gorm:"leverage;not null" json:"leverage"`                                               // 杠杆倍数
	Level                int                `gorm:"level;type:int(5);not null" json:"level"`                                         // 风险等级
	LiquidationPrice     decimal.Decimal    `gorm:"liquidation_price" sql:"type:decimal(30,15);" json:"liquidation_price"`           // 预估强平价
	LiquidationType      int                `gorm:"liquidation_type;not null" json:"liquidation_type"`                               // 强平类型
	LiquidationFee       decimal.Decimal    `gorm:"liquidation_fee" sql:"type:decimal(30,15);" json:"liquidation_fee"`               // 爆仓强平费
	LiquidationFeeInfo   string             `gorm:"liquidation_fee_info" sql:"type:text;" json:"liquidation_fee_info"`               // 爆仓强平费明细
	LiquidationDealValue decimal.Decimal    `gorm:"liquidation_deal_value" sql:"type:decimal(30,15);" json:"liquidation_deal_value"` // 强平单成交总额
	IsOverflow           int                `gorm:"is_overflow;type:int(5);not null" json:"is_overflow"`                             // 是否穿仓
	OverflowInfo         string             `gorm:"overflow_info" sql:"type:text;" json:"overflow_info"`                             // 穿仓明细
	OverflowValue        decimal.Decimal    `gorm:"overflow_value" sql:"type:decimal(30,15);" json:"overflow_value"`                 // 补贴数量折合
	IsForceRival         int                `gorm:"is_force_rival;type:int(5);not null" json:"is_force_rival"`                       // 是否对手方强制减仓
	ForceRivalAmount     decimal.Decimal    `gorm:"force_rival_amount" sql:"type:decimal(30,15);" json:"force_rival_amount"`         // 对手方减仓数量
	OpenPrice            decimal.Decimal    `gorm:"open_rate" sql:"type:decimal(30,15);" json:"open_price"`                          // 开仓价格
	PNL                  decimal.Decimal    `gorm:"pnl" sql:"type:decimal(30,15);" json:"pnl"`                                       // 收益率
	MarkPrice            decimal.Decimal    `gorm:"mark_rate" sql:"type:decimal(30,15);" json:"mark_price"`                          // 标记价格
	BurstPrice           decimal.Decimal    `gorm:"burst_rate" sql:"type:decimal(30,15);" json:"burst_price"`                        // 爆仓价格
	CollapsePrice        decimal.Decimal    `gorm:"collapse_price" sql:"type:decimal(30,15);" json:"collapse_price"`                 // 破产价格
	CollapsePriceFormula string             `gorm:"collapse_price_formula" sql:"type:text;" json:"collapse_price_formula"`           // 破产价格公式
	HoldingMarginRate    decimal.Decimal    `gorm:"holding_margin_rate" sql:"type:decimal(30,15);" json:"holding_margin_rate"`       // 维持保证金率
	HoldingMargin        decimal.Decimal    `gorm:"holding_margin" sql:"type:decimal(30,15);" json:"holding_margin"`                 // 维持保证金
	IsMixMargin          int                `gorm:"is_mix_margin;type:int(5);default:''" json:"is_mix_margin"`                       // 是否混合保证金爆仓
	IsTrialPos           int                `gorm:"is_trial_pos;type:int(5);default:'0'" json:"is_trial_pos"`                        // 是否体验金仓位
	OpenTime             int64              `gorm:"open_time" json:"open_time"`                                                      // 开仓时间
	BurstTime            int64              `gorm:"burst_time" json:"burst_time"`                                                    // 平仓时间 (强平触发时间)
	Status               domain.BurstStatus `gorm:"status" json:"status"`                                                            // 平仓状态 (强平状态)
	OverflowTime         int64              `gorm:"overflow_time" json:"overflow_time"`                                              // 穿仓补贴时间
	CreateTime           int64              `gorm:"create_time;not null" json:"create_time"`                                         // 创建时间
}

// func CreateBurstSwapTable() error {
// 	db, err := sqllib.Db()
// 	if err != nil {
// 		return err
// 	}

// 	burst := &BurstSwap{}
// 	tableName := burst.TableName()
// 	if ok := util.TableIsExit(tableName); !ok {
// 		if !db.HasTable(tableName) {
// 			err := db.Table(tableName).CreateTable(burst).Error
// 			if err != nil {
// 				return err
// 			}
// 		}
// 	}
// 	util.SetNewTableName(tableName)
// 	return nil
// }

// TableName get bill swap table name
func (slf *BurstSwap) TableName() string {
	return "burst_swap"
}

// // Insert insert swap bill
// func (slf *BurstSwap) Insert(db *gorm.DB) (err error) {
// 	if db == nil {
// 		db, err = sqllib.Db()
// 		if err != nil {
// 			return err
// 		}
// 	}

// 	tableName := slf.TableName()
// 	if ok := util.TableIsExit(tableName); !ok {
// 		if !db.HasTable(tableName) {
// 			err := db.Table(tableName).CreateTable(slf).Error
// 			if err != nil {
// 				return err
// 			}
// 			util.SetNewTableName(tableName)
// 		}
// 	}
// 	if err := db.Table(tableName).Create(slf).Error; err != nil {
// 		return err
// 	}
// 	return nil
// }

// // CheckDataExists check burst data exists
// func (slf *BurstSwap) CheckDataExists(db *gorm.DB) (num int, err error) {
// 	if db == nil {
// 		db, err = sqllib.Db()
// 		if err != nil {
// 			return 0, err
// 		}
// 	}

// 	data := struct {
// 		Num int `json:"num"`
// 	}{}
// 	if err := db.Table(slf.TableName()).
// 		Select("count(id) as num").
// 		Where("burst_id=? AND user_id=?", slf.BurstId, slf.UID).Find(&data).Error; err != nil {
// 		return 0, err
// 	}
// 	return data.Num, nil
// }

// // UpdateStatus update burst status
// func (slf *BurstSwap) UpdateStatus(db *gorm.DB) (err error) {
// 	if db == nil {
// 		db, err = sqllib.Db()
// 		if err != nil {
// 			return err
// 		}
// 	}

// 	body := map[string]interface{}{
// 		"status": slf.Status,
// 	}
// 	// 如果爆仓强平费大于0, 则更新
// 	if slf.LiquidationFee.Sign() != 0 {
// 		body["liquidation_fee"] = slf.LiquidationFee.Abs()
// 	}
// 	if err := db.Table(slf.TableName()).
// 		Where("burst_id=? AND user_id=?", slf.BurstId, slf.UID).
// 		Update(body).Error; err != nil {
// 		return err
// 	}

// 	return nil
// }

// // UpdateOverflow update burst is_overflow and overflow_amount
// func (slf *BurstSwap) UpdateOverflow(db *gorm.DB) (err error) {
// 	if db == nil {
// 		db, err = sqllib.Db()
// 		if err != nil {
// 			return err
// 		}
// 	}

// 	if err := db.Table(slf.TableName()).
// 		Where("burst_id=? AND user_id=?", slf.BurstId, slf.UID).
// 		Updates(map[string]interface{}{
// 			"is_overflow":    slf.IsOverflow,
// 			"status":         slf.Status,
// 			"overflow_info":  slf.OverflowInfo,
// 			"overflow_value": slf.OverflowValue,
// 			"overflow_time":  slf.OverflowTime,
// 		}).Error; err != nil {
// 		return err
// 	}

// 	return nil
// }

// // UpdateIsForceRival update burst is_force_rival
// func (slf *BurstSwap) UpdateIsForceRival(db *gorm.DB) (err error) {
// 	if db == nil {
// 		db, err = sqllib.Db()
// 		if err != nil {
// 			return err
// 		}
// 	}

// 	if err := db.Table(slf.TableName()).
// 		Where("burst_id=? AND user_id=?", slf.BurstId, slf.UID).
// 		Updates(map[string]interface{}{
// 			"is_force_rival":     slf.IsForceRival,
// 			"force_rival_amount": gorm.Expr("force_rival_amount + cast(? as decimal(30,15))", slf.ForceRivalAmount),
// 		}).Error; err != nil {
// 		return err
// 	}

// 	return nil
// }

// // UpdateReduceFields update liquidation data when reduce pos
// //
// //	此函数以反射方式被调用，搜索引用时请使用函数名称全文搜索  (FuncName: "UpdateReduceFields")
// func (slf *BurstSwap) UpdateReduceFields(db *gorm.DB) (err error) {
// 	if db == nil {
// 		db, err = sqllib.Db()
// 		if err != nil {
// 			return err
// 		}
// 	}

// 	condition := fmt.Sprintf("burst_id=%s AND user_id=%s", slf.BurstId, slf.UID)
// 	if slf.PosType != 0 {
// 		condition = fmt.Sprintf("%s AND pos_type=%d", condition, slf.PosType)
// 	}

// 	body := map[string]interface{}{
// 		"liquidation_deal_value": gorm.Expr("liquidation_deal_value + cast(? as decimal(30,15))", slf.LiquidationDealValue),
// 	}
// 	if slf.Status != 0 {
// 		body["status"] = slf.Status
// 	}
// 	if slf.LiquidationType != 0 {
// 		body["liquidation_type"] = slf.LiquidationType
// 	}
// 	if len(slf.PosId) > 0 {
// 		body["pos_id"] = slf.PosId
// 	}
// 	if err := db.Table(slf.TableName()).
// 		Where(condition).
// 		Updates(body).Error; err != nil {
// 		return err
// 	}

// 	return nil
// }

// // DeleteRecords 根据条件删除记录
// func (slf *BurstSwap) DeleteRecords(db *gorm.DB) (err error) {
// 	if db == nil {
// 		db, err = sqllib.Db()
// 		if err != nil {
// 			return err
// 		}
// 	}
// 	sql := fmt.Sprintf("DELETE FROM `%s` WHERE `create_time`<? AND `user_type`=? AND `liquidation_type`=?;", slf.TableName())
// 	if err := db.Exec(sql, slf.CreateTime, domain.UserTypePlatformRobot, domain.LiquidationTypeRobotSelfReduce).Error; err != nil {
// 		return err
// 	}
// 	return nil
// }

// // MarshalBinary implement encoding.BinaryMarshaler for redis
// func (slf *BurstSwap) MarshalBinary() ([]byte, error) {
// 	return json.Marshal(slf)
// }

// // UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
// func (slf *BurstSwap) UnmarshalBinary(data []byte) error {
// 	return json.Unmarshal(data, &slf)
// }
