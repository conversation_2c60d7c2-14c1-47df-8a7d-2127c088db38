package entity

import (
	"github.com/shopspring/decimal"
)

type OptionTotalProfit struct {
	UID                string          `gorm:"user_id;type:varchar(20);not null"`               // 用户ID
	Currency           string          `gorm:"currency;type:varchar(25);not null"`              // 资产币种
	TotalProfit        decimal.Decimal `gorm:"total_profit" sql:"type:decimal(30,15);"`         // 累计盈利 (累计期权订单已实现盈亏>=0总和)
	TotalLoss          decimal.Decimal `gorm:"total_loss" sql:"type:decimal(30,15);"`           // 累计亏损 (累计期权订单已实现盈亏<0权利金总和)
	TotalProfitPremium decimal.Decimal `gorm:"total_profit_premium" sql:"type:decimal(30,15);"` // 累计盈利订单权利金总和
	Unexercised        decimal.Decimal `gorm:"unexercised" sql:"type:decimal(30,15);"`          // 未行权期权总额

	CreateTime int64 `gorm:"column:create_time;autoCreateTime:milli" json:"create_time"`
	UpdateTime int64 `gorm:"column:update_time;autoUpdateTime:milli" json:"update_time"`
}

func NewOptionTotalProfit() *OptionTotalProfit {
	return &OptionTotalProfit{}
}

func (slf *OptionTotalProfit) TableName() string {
	return "option_total_profit"
}
