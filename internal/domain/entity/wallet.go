package entity

import (
	"strings"

	"github.com/shopspring/decimal"
)

type Wallet struct {
	Id       string          `gorm:"id;PRIMARY_KEY;type:varchar(20);" json:"id"`
	UID      string          `gorm:"uid;type:varchar(20);not null;" json:"uid"`           // 用户ID
	Currency string          `gorm:"currency;type:varchar(25);not null;" json:"currency"` // 资产币种
	Balance  decimal.Decimal `gorm:"balance" sql:"type:decimal(30,15);" json:"balance"`   // 账户余额
	Frozen   string          `gorm:"frozen;type:varchar(8000)" json:"frozen"`             // 冻结

	CreateTime int64 `gorm:"column:create_time;autoCreateTime:milli" json:"create_time"`
	UpdateTime int64 `gorm:"column:update_time;autoUpdateTime:milli" json:"update_time"`
}

func (slf *Wallet) TableName() string {
	return "wallet"
}

func (slf *Wallet) GetId() string {
	return slf.UID + "_" + strings.ToUpper(slf.Currency)
}
