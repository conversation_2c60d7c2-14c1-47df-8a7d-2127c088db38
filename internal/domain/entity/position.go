package entity

import (
	"github.com/shopspring/decimal"
)

type Position struct {
	Id              string          `gorm:"id;PRIMARY_KEY;type:varchar(50);" json:"id"`                       // 持仓ID
	UID             string          `gorm:"user_id;type:varchar(20);not null" json:"uid"`                     // 用户ID
	UserType        int             `gorm:"user_type;type:SMALLINT;" json:"userType"`                         // 用户类型
	PosSide         int32           `gorm:"pos_side;not null" json:"posSide"`                                 // 方向 (1:多仓 2:空仓)
	Leverage        int             `gorm:"leverage;not null" json:"leverage"`                                // 杠杆倍数
	AccountType     string          `gorm:"account_type;type:varchar(20);" json:"accountType"`                // 合约类型
	ContractCode    string          `gorm:"contract_code;type:varchar(50);not null" json:"contractCode"`      // 合约代码
	Currency        string          `gorm:"currency;type:varchar(25);not null" json:"currency"`               // 资产币种
	Pos             decimal.Decimal `gorm:"pos" sql:"type:decimal(30,15);" json:"pos"`                        // 仓位
	PosAvailable    decimal.Decimal `gorm:"pos_available" sql:"type:decimal(30,15);" json:"posAvailable"`     // 可平仓位
	MarginMode      int32           `gorm:"margin_mode" sql:"type:SMALLINT;" json:"marginMode"`               // 保证金模式 (1:全仓模式 2:追仓模式)
	IsolatedMargin  decimal.Decimal `gorm:"isolated_margin" sql:"type:decimal(30,15);" json:"isolatedMargin"` // 逐仓位的保证金
	OpenPriceAvg    decimal.Decimal `gorm:"open_price_avg" sql:"type:decimal(30,15);" json:"openPriceAvg"`    // 开仓均价
	OpenTime        int64           `gorm:"open_time" json:"openTime"`                                        // 开仓时间
	PosStatus       int32           `gorm:"pos_status" sql:"type:SMALLINT;" json:"posStatus"`                 // 持仓状态(1:持仓中 2:已结束)
	LiquidationType int32           `gorm:"liquidation_type" sql:"type:SMALLINT;" json:"liquidationType"`     // 强平类型
	ProfitReal      decimal.Decimal `gorm:"profit_real" sql:"type:decimal(30,15);" json:"profitReal"`         // 持仓已实现盈亏
	Subsidy         decimal.Decimal `gorm:"subsidy" sql:"type:decimal(30,15);" json:"subsidy"`                // 穿仓补贴金额
	RebornId        string          `gorm:"reborn_id;type:varchar(50);not null" json:"rebornId"`              // 复活卡ID
	TrialMargin     decimal.Decimal `gorm:"trial_margin" sql:"type:decimal(30,15);" json:"trialMargin"`       // 体验金保证金
	AwardOpIds      string          `gorm:"award_op_ids;type:varchar(500);not null" json:"awardOpIds"`        // 奖励操作ID

	MaintenMarginRate decimal.Decimal `gorm:"-" json:"maintenMarginRate"` // 维持保证金率
	MaintenMargin     decimal.Decimal `gorm:"-" json:"maintenMargin"`     // 维持保证金
	MarginRate        decimal.Decimal `gorm:"-" json:"marginRate"`        // 保证金率
	MarginBalance     decimal.Decimal `gorm:"-" json:"marginBalance"`     // 保证金余额
	ReturnRate        decimal.Decimal `gorm:"-" json:"returnRate"`        // 回报率
	ProfitUnreal      decimal.Decimal `gorm:"-" json:"profitUnreal"`      // 未实现盈亏
	Liquidation       decimal.Decimal `gorm:"-" json:"liquidation"`       // 预估强评价
	MarkPrice         decimal.Decimal `gorm:"-" json:"markPrice"`         // 标记价格
	RivalScore        decimal.Decimal `gorm:"-" json:"rivalScore"`        // 对手方强制减仓指数

	CreateTime int64 `gorm:"column:create_time;autoCreateTime:milli" json:"create_time"`
	UpdateTime int64 `gorm:"column:update_time;autoUpdateTime:milli" json:"update_time"`
}

func NewPosSwap() *Position {
	return &Position{}
}

func (slf *Position) TableName() string {
	return "position"
}

type PosPartInfo struct {
	Id string `gorm:"id;PRIMARY_KEY;type:varchar(50);" json:"id"`
}
