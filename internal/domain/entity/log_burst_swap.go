package entity

import (
	"encoding/json"
	"time"

	"futures-asset/internal/domain"
	"futures-asset/util"

	"github.com/shopspring/decimal"
)

// LogBurstSwap burst log
type LogBurstSwap struct {
	Id                   int64             `gorm:"id;PRIMARY_KEY;AUTO_INCREMENT" json:"id"`
	BurstId              string            `gorm:"burst_id;type:varchar(30);not null"`                                        // 爆仓ID
	UID                  string            `gorm:"user_id;type:varchar(20);not null" json:"user_id"`                          // 用户ID
	UserType             int               `gorm:"user_type;type:varchar(20);not null" json:"user_type"`                      // 用户类型
	AccountType          string            `gorm:"account_type;type:varchar(20);not null" json:"account_type"`                // 合约类型
	Base                 string            `gorm:"base;type:varchar(10);not null" json:"base"`                                // 交易币
	Quote                string            `gorm:"quote;type:varchar(10);not null" json:"quote"`                              // 计价币
	Currency             string            `gorm:"currency;type:varchar(10);not null" json:"currency"`                        // 合约类型
	PosId                string            `gorm:"pos_id;type:varchar(50);not null" json:"pos_id"`                            // 持仓ID
	PosSide              int32             `gorm:"pos_side;type:int(5);not null" json:"pos_side"`                             // 持仓方向
	LongPos              decimal.Decimal   `gorm:"long_pos" sql:"type:decimal(30,15);" json:"long_pos"`                       // 多仓仓位
	LongPosValue         decimal.Decimal   `gorm:"long_pos_value" sql:"type:decimal(30,15);" json:"long_pos_value"`           // 多仓仓位价值
	PosMargin            decimal.Decimal   `gorm:"pos_margin" sql:"type:decimal(30,15);" json:"pos_margin"`                   // 仓位保证金
	ShortPos             decimal.Decimal   `gorm:"short_pos_value" sql:"type:decimal(30,15);" json:"short_pos_value"`         // 空仓仓位
	ShortPosValue        decimal.Decimal   `gorm:"short_pos" sql:"type:decimal(30,15);" json:"short_pos"`                     // 空仓仓位价值
	Leverage             int               `gorm:"leverage;" sql:"not null" json:"leverage"`                                  // 杠杆倍数
	MarginMode           domain.MarginMode `gorm:"margin_mode;type:int(5);not null" json:"margin_mode"`                       // 保证金模式
	LongOpenPrice        decimal.Decimal   `gorm:"long_open_price" sql:"type:decimal(30,15);" json:"long_open_price"`         // 多仓结算价格
	ShortOpenPrice       decimal.Decimal   `gorm:"short_open_price" sql:"type:decimal(30,15);" json:"short_open_price"`       // 空仓结算价格
	BurstPrice           decimal.Decimal   `gorm:"burst_price" sql:"type:decimal(30,15);" json:"burst_price"`                 // 爆仓价格
	MarkPrice            decimal.Decimal   `gorm:"mark_price" sql:"type:decimal(30,15);" json:"mark_price"`                   // 标记价格
	CollapsePrice        decimal.Decimal   `gorm:"collapse_price" sql:"type:decimal(30,15);" json:"collapse_price"`           // 破产价格
	CollapsePriceFormula string            `gorm:"collapse_price_formula" sql:"type:text;" json:"collapse_price_formula"`     // 破产价格公式
	FrozenMargin         decimal.Decimal   `gorm:"frozen_margin" sql:"type:decimal(30,15);" json:"frozen_margin"`             // 冻结保证金
	MarginBalance        decimal.Decimal   `gorm:"margin_balance" sql:"type:decimal(30,15);" json:"margin_balance"`           // 保证金余额
	TotalMargin          decimal.Decimal   `gorm:"total_margin" sql:"type:decimal(30,15);" json:"total_margin"`               // 保证金
	PNL                  decimal.Decimal   `gorm:"pnl" sql:"type:decimal(30,15);" json:"pnl"`                                 // 收益率
	LongPosUnreal        decimal.Decimal   `gorm:"long_pos_unreal" sql:"type:decimal(30,15);" json:"long_pos_unreal"`         // 多仓未实现盈亏
	ShortPosUnreal       decimal.Decimal   `gorm:"short_pos_unreal" sql:"type:decimal(30,15);" json:"short_pos_unreal"`       // 空仓未实现盈亏
	HoldingMarginRate    decimal.Decimal   `gorm:"holding_margin_rate" sql:"type:decimal(30,15);" json:"holding_margin_rate"` // 维持保证金率
	HoldingMargin        decimal.Decimal   `gorm:"holding_margin" sql:"type:decimal(30,15);" json:"holding_margin"`           // 维持保证金
	LiquidationPrice     decimal.Decimal   `gorm:"liquidation_price" sql:"type:decimal(30,15);" json:"liquidation_price"`     // 预估强评价
	LiquidationType      int               `gorm:"liquidation_type;" sql:"not null" json:"liquidation_type"`                  // 强平类型
	OpenTime             int64             `gorm:"open_time" json:"open_time"`                                                // 开仓时间
	Level                int               `gorm:"level;type:int(5);not null" json:"level"`                                   // 风险等级
	IsTrialPos           int               `gorm:"is_trial_pos;type:int(5);default:'0'" json:"is_trial_pos"`                  // 是否体验金仓位
	Status               int               `gorm:"status;type:int(5);not null" json:"status"`                                 // 爆仓状态
	CreateTime           int64             `gorm:"create_time;not null" json:"create_time"`                                   // 创建时间
	BurstPosData         string            `gorm:"burst_pos_data" sql:"type:text;" json:"burst_pos_data"`
}

// TableName get bill swap table name
func (bs *LogBurstSwap) TableName() string {
	return "log_burst_swap" + util.DayLayout(time.Now().UnixNano(), util.EnumNanosecond)
}

// MarshalBinary implement encoding.BinaryMarshaler for redis
func (bs *LogBurstSwap) MarshalBinary() ([]byte, error) {
	return json.Marshal(bs)
}

// UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
func (bs *LogBurstSwap) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, &bs)
}
