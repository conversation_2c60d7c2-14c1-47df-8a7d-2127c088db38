package entity

import (
	"futures-asset/util"

	"github.com/shopspring/decimal"
)

// BillOption option bill
type BillOption struct {
	Id          int64           `gorm:"id;PRIMARY_KEY;AUTO_INCREMENT" json:"id"`
	UID         string          `gorm:"user_id;type:varchar(20);not null" json:"uid"`          // 用户ID
	OptionId    string          `gorm:"option_id;type:varchar(50);not null" json:"optionId"`   // 期权号
	OperateId   string          `gorm:"operate_id;type:varchar(50);not null" json:"operateId"` // 操作id (订单号)
	BillId      string          `gorm:"bill_id;type:varchar(50);not null" json:"billId"`       // 账单ID
	Base        string          `gorm:"base;type:varchar(20);not null" json:"base"`            // 交易币
	Quote       string          `gorm:"quote;type:varchar(20);not null" json:"quote"`          // 计价币
	Currency    string          `gorm:"currency;type:varchar(25);not null" json:"currency"`    // 资产币种
	BillType    int             `gorm:"bill_type;" sql:"type:SMALLINT;" json:"billType"`       // 账单类型
	Amount      decimal.Decimal `gorm:"amount" sql:"type:decimal(30,15);" json:"amount"`       // 数量
	OperateTime int64           `gorm:"operate_time;not null" json:"operateTime"`              // 创建时间
	FromAccount int             `gorm:"from_account;" json:"fromAccount"`                      // 转出账户
	ToAccount   int             `gorm:"to_account;" json:"toAccount"`                          // 转入账户

	OptionType int             `gorm:"-" json:"optionType"` // 期权类型 (1-实盘 2-模拟盘)
	Premium    decimal.Decimal `gorm:"-" json:"premium"`    // 行权时记录权利金金额
}

// TableName get bill option table name
func (slf *BillOption) TableName() string {
	return "bill_option" + util.MonthLayout(slf.OperateTime, util.EnumNanosecond)
}

// DemoTableName get bill option demo table name
func (slf *BillOption) DemoTableName() string {
	return "bill_option_demo" + util.MonthLayout(slf.OperateTime, util.EnumNanosecond)
}
