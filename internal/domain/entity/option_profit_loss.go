package entity

import (
	"github.com/shopspring/decimal"
)

type OptionProfitLoss struct {
	UID           string          `gorm:"user_id;type:varchar(20);not null"`         // 用户ID
	Currency      string          `gorm:"currency;type:varchar(25);not null"`        // 资产币种
	NetIn         decimal.Decimal `gorm:"net_in" sql:"type:decimal(30,15);"`         // 单日净转入
	TotalFee      decimal.Decimal `gorm:"total_fee" sql:"type:decimal(30,15);"`      // 当日累计手续费
	Profit        decimal.Decimal `gorm:"profit" sql:"type:decimal(30,15);"`         // 当日盈利
	Loss          decimal.Decimal `gorm:"loss" sql:"type:decimal(30,15);"`           // 当日亏损
	ProfitLoss    decimal.Decimal `gorm:"profit_loss" sql:"type:decimal(30,15);"`    // 当日盈亏
	ProfitPremium decimal.Decimal `gorm:"profit_premium" sql:"type:decimal(30,15);"` // 当日盈利订单权利金总和
	ProfitCount   int64           `gorm:"profit_count"`                              // 当日盈利次数
	LossCount     int64           `gorm:"loss_count"`                                // 当日亏损次数
	OperateTime   int64           `gorm:"operate_time"`                              // 统计日的0：0：0 的时间戳

	CreateTime int64 `gorm:"column:create_time;autoCreateTime:milli" json:"create_time"`
	UpdateTime int64 `gorm:"column:update_time;autoUpdateTime:milli" json:"update_time"`
}

func (slf *OptionProfitLoss) TableName() string {
	return "option_profit_loss"
}
