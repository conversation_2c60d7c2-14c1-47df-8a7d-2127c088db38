package entity

import (
	"github.com/shopspring/decimal"
)

type LogFundingRate struct {
	Id          int64           `gorm:"id;PRIMARY_KEY;AUTO_INCREMENT" json:"id"`
	Base        string          `gorm:"base;type:varchar(50);not null" json:"base"`                            // 交易币
	Quote       string          `gorm:"quote;type:varchar(50);not null" json:"quote"`                          // 计价币
	FundRate    decimal.Decimal `gorm:"fund_rate;" sql:"type:decimal(30,15);" json:"fundRate"`                 // 资金费率
	MarkPrice   decimal.Decimal `gorm:"mark_price" sql:"type:decimal(30,15);" json:"markPrice"`                // 标记价格
	IndexPrice  decimal.Decimal `gorm:"index_price;" sql:"type:decimal(30,15);" json:"indexPrice"`             // 指数价格
	OperateTime int64           `gorm:"operate_time;not null" sql:"index:idx_operate_time" json:"operateTime"` // 结算时间
}

func (slf *LogFundingRate) TableName() string {
	return "log_funding_rate"
}
