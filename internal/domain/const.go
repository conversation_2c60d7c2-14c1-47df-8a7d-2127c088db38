package domain

import (
	"futures-asset/pkg/rdmap"

	"github.com/shopspring/decimal"
)

const DefaultNoData = "--"

const (
	FundRateSplit  = "_"
	MaxFundRateNum = 1440 // redis 中 最多保留 最近 MaxFundRate 条 资金费率 1440 大约是1天的量
)

const (
	// 盈亏记录的时间类型
	DayParamNoLimit = iota
	DayParamSevenDay
	DayParamThirtyDay
)

const (
	// 邮箱短信的 type_id
	MSTrialWarning      = "0805"
	MSTrialRecycle      = "0806"
	MSReduce            = "0800"
	MSForceReduceTarget = "0801"
	MSBurst             = "0802"
	MSReduceWarn        = "0803"
	MSBurstWarn         = "0804"
)

// 账户类型
const (
	AccountSpotInt = iota // 现货账户
	AccountL2cInt         // 杠杆账户
	AccountSwapInt        // USDT永续合约账户
	AccountOtcInt         // 法币账户
)

const (
	CnyPrecision      = 2  // cny 精度 2位
	CoinPrecision     = 6  // 币种精度位数
	CurrencyPrecision = 8  // 币种精度位数
	PricePrecision    = 15 // 价格精度位数
	RatePrecision     = 4  // 百分比精度 乘 100 之前
	FundRatePrecision = 6  // 资金费率 乘100 之前保留6位
)

// 通用判断1是2否
const (
	ModelYes = iota + 1
	ModelNo
)

var RobotUsers *rdmap.RdMap
var CnyRate = decimal.NewFromFloat(6.5) // usd_cny 汇率默认6.5
