package es

import (
	"context"
	"futures-asset/internal/domain"
	"futures-asset/pkg/eslib"
	"strings"
	"time"

	"github.com/shopspring/decimal"
	"github.com/sirupsen/logrus"
)

const (
	CONTRACT_TRIAL_LEDGER_TYPE     = 3
	CHARGE_FOR_TROUBLE_LEDGER_TYPE = 9
	TRADE_LEDGER_TYPE              = 7
	DERIVATIVES_ACCOUNT_TYPE       = 5
	FUND_FEE_LEDGER_TYPE           = 12
	ACTIVITY_REWARD_LEDGER_TYPE    = 6
	FUTURE_TRADE_TYPE              = 5
	OPTION_ATRADE_TYPE             = 6
)

type LedgerDetail struct {
	UID             string          `json:"uid"`
	Currency        string          `json:"currency"`
	FromAccountType int             `json:"fromAccountType"` // 源账户类型
	ToAccountType   int             `json:"toAccountType"`   // 目标账户类型
	Type            int             `json:"type"`            // 账单类型
	ChangeAmount    decimal.Decimal `json:"changeAmount"`    // 变化金额
	Balance         decimal.Decimal `json:"balance"`         // 余额
	Year            int             `json:"year"`
	Month           int             `json:"month"`
	OpTime          int64           `json:"opTime"`       // 秒
	ActivityType    int             `json:"activityType"` // 活动类型
	CoinPair        string          `json:"coinPair"`
	TradeType       int             `json:"tradeType"` // 交易类型
	AwardType       int             `json:"awardType"` // 奖励类型
}

func SaveLedgerDetail(uid, currency string, accountType, ledgerType, tradeType int, changeAmount, balance decimal.Decimal) {
	nowTime := time.Now()
	ledgerDetail := &LedgerDetail{
		UID:           uid,
		Currency:      strings.ToLower(currency),
		ToAccountType: accountType,
		Type:          ledgerType,
		ChangeAmount:  changeAmount,
		Balance:       balance,
		Year:          nowTime.Year(),
		Month:         int(nowTime.Month()),
		OpTime:        nowTime.Unix(),
		TradeType:     tradeType,
		AwardType:     0,
	}
	_, err := eslib.ES().Index().Index(domain.EsLedgerStatisticIndex).Type("_doc").BodyJson(ledgerDetail).Do(context.Background())
	if err != nil {
		logrus.Errorf("SaveLedgerDetail fail err: %v", err)
	}
}
