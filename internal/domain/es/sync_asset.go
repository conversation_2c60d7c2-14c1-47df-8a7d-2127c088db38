package es

import (
	"context"
	"futures-asset/internal/domain"
	"futures-asset/pkg/eslib"

	"github.com/pkg/errors"
)

func InitEsBillIndex() error {
	hasBurstIndex, err := eslib.ES().IndexExists(domain.EsBillIndex).Do(context.Background())
	if err != nil {
		return errors.Wrap(err, "swap bill call exists")
	}
	if !hasBurstIndex {
		mapping := `
{
  "mappings": {
    "_doc": {
      "properties": {
        "amount": {
          "type": "scaled_float",
          "scaling_factor": *********
        },
        "billId": {
          "type": "keyword"
        },
        "billType": {
          "type": "long"
        },
        "contractCode": {
          "type": "keyword"
        },
        "currency": {
          "type": "keyword"          
        },
        "fromAccount": {
          "type": "long"
        },
        "fromPair": {
          "type": "keyword"
        },
        "fundRate": {
			"type": "scaled_float",
          	"scaling_factor": *********
        },
        "id": {
          "type": "long"
        },
        "markPrice": {
          "type": "scaled_float",
          "scaling_factor": *********
        },
        "operateId": {
          "type": "keyword"
        },
        "operateTime": {
          "type": "long"
        },
        "recycleOpId": {
          "type": "keyword"
        },
        "toAccount": {
          "type": "long"
        },
        "toPair": {
          "type": "keyword"
        },
        "uid": {
          "type": "keyword"
        }
      }
    }
  }
}
`
		_, err := eslib.ES().CreateIndex(domain.EsBillIndex).BodyJson(mapping).Do(context.Background())
		if err != nil {
			return errors.Wrap(err, "swap bill call create")
		}
	}
	return nil
}
