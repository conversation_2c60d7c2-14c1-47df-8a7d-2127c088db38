package es

import (
	"context"
	"futures-asset/internal/domain"
	"futures-asset/pkg/eslib"

	"github.com/pkg/errors"
)

func InitBillOptionEsIndex() error {
	var (
		client  = eslib.ES()
		mapping = `
		{
		  "mappings": {
			"_doc": {
			  "properties": {
				"amount": {
				  "type": "scaled_float",
				  "scaling_factor": *********
				},
				"optionId": {
				  "type": "keyword"
				},
				"billId": {
				  "type": "keyword"
				},
				"billType": {
				  "type": "long"
				},
				"base": {
				  "type": "keyword"
				},
				"quote": {
				  "type": "keyword"
				},
				"currency": {
				  "type": "keyword"          
				},
				"fromAccount": {
				  "type": "long"
				},
				"id": {
				  "type": "long"
				},
				"operateId": {
				  "type": "keyword"
				},
				"operateTime": {
				  "type": "long"
				},
				"toAccount": {
				  "type": "long"
				},
				"uid": {
				  "type": "keyword"
				}
			  }
			}
		  }
		}`
	)

	hasIndex, err := client.IndexExists(domain.EsOptionBillIndex).Do(context.Background())
	if err != nil {
		return errors.Wrap(err, "option bill call exists")
	}
	demoIndex, err := client.IndexExists(domain.EsOptionBillDemoIndex).Do(context.Background())
	if err != nil {
		return errors.Wrap(err, "option bill demo call exists")
	}

	if !hasIndex {
		_, err := client.CreateIndex(domain.EsOptionBillIndex).BodyJson(mapping).Do(context.Background())
		if err != nil {
			return errors.Wrap(err, "option bill demo call create")
		}
	}
	if !demoIndex {
		_, err := client.CreateIndex(domain.EsOptionBillDemoIndex).BodyJson(mapping).Do(context.Background())
		if err != nil {
			return errors.Wrap(err, "option bill demo call create")
		}
	}

	return nil
}
