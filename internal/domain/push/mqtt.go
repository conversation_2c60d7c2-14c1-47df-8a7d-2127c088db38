package push

import (
	"fmt"
	"futures-asset/util"
)

const (
	_prefixMarkPriceTopic  = "swap.market.mark_price:%v"
	_prefixIndexPriceTopic = "swap.market.index_price:%v"
)

func GetMarkPriceTopic(_base string, _quote string) string {
	return fmt.Sprintf(_prefixMarkPriceTopic, util.ContractCode(_base, _quote))
}

func GetIndexPriceTopic(_base string, _quote string) string {
	return fmt.Sprintf(_prefixIndexPriceTopic, util.ContractCode(_base, _quote))
}
