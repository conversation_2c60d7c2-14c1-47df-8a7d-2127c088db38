package domain

var UserTypeMap = map[int32]string{
	UserTypeNormal:        "普通用户",
	UserTypeMedia:         "媒体渠道",
	UserTypeGroup:         "社区合伙人",
	UserTypeProject:       "项目方",
	UserTypeStaff:         "企业员工",
	UserTypePlatformRobot: "平台机器人",
	UserTypeQuantify:      "量化团队",
}

var PosMap = map[int32]string{
	LongPos:  "多仓",
	ShortPos: "空仓",
}

var ClosePosMap = map[int32]string{
	LongPos:  "平多",
	ShortPos: "平空",
}

var AccountTypeMap = map[string]string{
	"spot":    "币币账户",
	"margin":  "逐仓杠杆账户",
	"swap":    "USDT永续",
	"futures": "交割合约账户",
}

var MarginModeMap = map[int32]string{
	int32(MarginModeCross):    "全仓",
	int32(MarginModeIsolated): "逐仓",
}

var OrderTypeMap = map[int]string{
	OrderTypePostOnly:       "只做Maker",
	OrderTypeNormal:         "普通订单",
	OrderTypePlan:           "计划委托",
	OrderTypeTakeProfitAll:  "全部止盈",
	OrderTypeTakeProfitPart: "部分止盈",
	OrderTypeStopLossAll:    "全部止损",
	OrderTypeStopLossPart:   "部分止损",
}

var IsLimitOrderMap = map[int]string{
	Limit:  "限价",
	Market: "市价",
}

var LiquidationTypeMap = map[LiquidationType]string{
	LiquidationTypeNone:             "",
	LiquidationTypeBurst:            "爆仓",          // 爆仓(撮合成交、直接成交(自己和他人对手方强制减仓))
	LiquidationTypeReduce:           "减仓",          // 减仓(直接成交(自己和自己)、撮合成交(自己和自己、自己和他人))
	LiquidationTypeStopProfitReduce: "止盈减仓",        // 止盈减仓(直接成交(自己和他人对手方强制减仓))
	LiquidationTypeTrialReduce:      "体验金减仓",       // 体验金减仓
	LiquidationTypeDisableSymbol:    "禁用币对减仓",      // 禁用币对减仓
	LiquidationTypeRobotSelfReduce:  "机器人自减仓",      // 机器人自减仓
	LiquidationTypeBurstSelfReduce:  "最后一档爆仓自成交减仓", // 最后一档爆仓自成交减仓
}

var BurstStatusMap = map[int]string{
	int(Bursting):      "强平中",
	int(BurstFinished): "已完成",
}

var PosStatusMap = map[PosStatus]string{
	PosStatusHolding: "持仓中",
	PosStatusEnd:     "已结束",
}
