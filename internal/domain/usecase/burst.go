package usecase

import (
	"context"
	"time"

	"futures-asset/internal/domain/entity"
)

type BurstUseCase interface {
	GetBurstInfoByTableNameAndId(ctx context.Context, id string) (*entity.BurstSwap, error)
	SearchBurstInfos(ctx context.Context, conditions map[string]interface{}, ranges map[string]map[string]interface{}, pageNum, pageSize int) (int64, []entity.BurstSwap)

	StatBurstTimesByTableNameList(ctx context.Context, startTime, endTime time.Time) []entity.StatBurstInfo
}
