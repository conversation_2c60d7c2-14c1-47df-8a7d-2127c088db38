package usecase

import (
	"context"

	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
)

type TrialUseCase interface {
	GetTrialAssetList(ctx context.Context, req *repository.TAListReq) (repository.TAListRes, error)
	TrialAssetList(ctx context.Context, param *repository.TAListReq) ([]*entity.TrialAsset, int64, error)
	GetNoInvalidTrial(ctx context.Context, req *repository.GetNoInvalidTrialReq) ([]*entity.TrialAsset, error)
	GetTrialAssetSummaryList(ctx context.Context, req *repository.GetTrialAssetSummaryListReq) ([]*entity.TrialAssetSummaryInfo, int64, error)
	GetTrialAssetDetailList(ctx context.Context, req *repository.GetTrialAssetDetailListReq) ([]*entity.TrialAssetDetailInfo, int64, error)
	GetLastUpdateTime(ctx context.Context, uid string) (int64, error)

	AddTrialAsset(ctx context.Context, req *repository.TrialBase) error
	RecycleTrialAsset(ctx context.Context, req *repository.OperateRecycleTrialAsset) error
}
