package usecase

import (
	"context"

	"github.com/shopspring/decimal"
)

type OptionUseCase interface {
	Asset(ctx context.Context, param *CurrencyParam) (OptionAssetReply, error)
	Trade(ctx context.Context, param *OptionTradeParam) ([]*OptionTradeReply, error)
	Exercise(ctx context.Context, param *ExerciseParam) ([]*OptionTradeReply, error)

	InitOption(ctx context.Context, param *UIDParam) error
}

type UIDParam struct {
	UID string `json:"uid"`
}

type CurrencyParam struct {
	UID      string `json:"uid"`
	Currency string `json:"currency"`
}

type OptionAssetReply struct {
	Unexercised decimal.Decimal `json:"unexercised"`
	TotalProfit decimal.Decimal `json:"totalProfit"`
	TotalLoss   decimal.Decimal `json:"totalLoss"`
}

type (
	OptionCommon struct {
		UID              string `json:"uid"`
		Base             string `json:"base"`
		Quote            string `json:"quote"`
		UserType         int32  `json:"userType"`    // 用户类型
		UserLevel        int    `json:"userLevel"`   // 用户等级
		OptionType       int    `json:"optionType"`  // 期权类型 (1-实盘 2-模拟盘)
		AccountType      string `json:"accountType"` // 账户类型
		ChannelCode      string `json:"channelCode"` // 渠道码
		AgentUserId      string `json:"agentUserId"`
		AgentStatus      int    `json:"agentStatus"`
		AgentChannelCode string `json:"agentChannelCode"`
		RegisterLanguage string `json:"registerLanguage"` // 注册语言
		Platform         string `json:"platform"`         // 来源 (WEB,APP,H5)
		OperateTime      int64  `json:"operateTime"`      // 操作时间
	}
	OptionTradeItem struct {
		OptionId  string          `json:"optionId"`  // 期权ID
		OrderId   string          `json:"orderId"`   // 委托单ID
		Period    string          `json:"period"`    // 期权时间粒度 (3分钟, 5分钟)
		Direction int             `json:"direction"` // 方向(看涨、看平或看跌)
		Price     decimal.Decimal `json:"price"`     // 委托价格
		Amount    decimal.Decimal `json:"amount"`    // 委托数量
		Premium   decimal.Decimal `json:"premium"`   // 权利金(委托总额)
		FeeRate   decimal.Decimal `json:"feeRate"`   // 手续费率
		Fee       decimal.Decimal `json:"fee"`       // 手续费
	}
	// OptionTradeParam contains binded and validated data.
	OptionTradeParam struct {
		OptionCommon
		Orders []OptionTradeItem `json:"orders"`
	}
	OptionTradeReply struct {
		Code    int    `json:"code"`
		OrderId string `json:"orderId"`
	}
)

type (
	ExerciseBase struct {
		OptionId    string `json:"optionId"` // 期权ID
		UID         string `json:"uid"`
		Base        string `json:"base"`
		Quote       string `json:"quote"`
		OptionType  int    `json:"optionType"`  // 期权类型 (1-实盘 2-模拟盘)
		OperateTime int64  `json:"operateTime"` // 操作时间
	}
	Exercise struct {
		OrderId          string          `json:"orderId"`     // 委托单ID
		UserType         int32           `json:"userType"`    // 用户类型
		UserLevel        int             `json:"userLevel"`   // 用户等级
		Period           string          `json:"period"`      // 期权时间粒度 (3分钟, 5分钟)
		Direction        int             `json:"direction"`   // 方向(看涨、看平或看跌)
		Price            decimal.Decimal `json:"price"`       // 委托价格
		Amount           decimal.Decimal `json:"amount"`      // 委托数量
		Premium          decimal.Decimal `json:"premium"`     // 权利金(委托总额)
		FeeRate          decimal.Decimal `json:"feeRate"`     // 手续费率
		Fee              decimal.Decimal `json:"fee"`         // 手续费
		State            int             `json:"state"`       // 订单状态
		ProfitReal       decimal.Decimal `json:"profitReal"`  // 已实现盈亏
		AccountType      string          `json:"accountType"` // 账户类型
		Platform         string          `json:"platform"`    // 来源 (WEB,APP,H5)
		ChannelCode      string          `json:"channelCode"`
		AgentChannelCode string          `json:"agentChannelCode"`
		AgentUserId      string          `json:"agentUserId"`
		AgentStatus      int             `json:"agentStatus"`
	}
	ExerciseParam struct {
		ExerciseBase
		Orders []Exercise `json:"orders"`
	}
)
