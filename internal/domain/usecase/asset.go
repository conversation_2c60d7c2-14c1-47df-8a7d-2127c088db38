package usecase

import (
	"context"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"

	"github.com/shopspring/decimal"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

// mockgen -source./asset.go -destination=../../mock/usecase/
//
//nolint:interfacebloat
type AssetUseCase interface {
	UserAsset(ctx context.Context, param *repository.SwapParam) (*repository.AssetSwap, error)
	BatchUserAsset(ctx context.Context, param AssetParam) ([]repository.BatchAssetSwap, error)
	GetUserAssetAndPos(ctx context.Context, param *repository.SwapParam) (domain.Code, repository.ReqUserAssetAndPos)
	SumUserTotalAsset(ctx context.Context) (repository.ResSumUserTotalAsset, error)
	TotalBalance(ctx context.Context, param ReqTotalBalance) (domain.Code, repository.ResTotalBalance)
	AssetDetail(ctx context.Context, param ReqAssetDetail) (repository.ResAssetDetail, error)

	LockAsset(ctx context.Context, param *LockParam) (*repository.AssetSwap, error)
	UnLockAsset(ctx context.Context, param *BatchUnlockParam) (*BatchUnlockReply, error)
}

type AssetParam struct {
	UserIds  []string `json:"userIds"`
	Currency string   `json:"currency"`
}

type ReqTotalBalance struct {
	UID      string `json:"uid"`
	Currency string `json:"currency"` // 折合的币种
}

type ReqAssetDetail struct {
	UID string `json:"uid"`
}

type LockParam struct {
	UID             string                    `json:"uid" binding:"required"`    // 用户ID
	UserType        int32                     `json:"user_type"`                 // 用户类型
	OrderID         string                    `json:"order_id"`                  // 委托单ID
	OrderType       futuresassetpb.OrderType  `json:"order_type"`                // 委托类型 (1:限价 2:市价 3-限价止盈 4-市价止盈 5-限价止损 6-市价止损)
	Symbol          string                    `json:"symbol"`                    // 货币对
	Currency        string                    `json:"currency"`                  // 计价币种
	Amount          decimal.Decimal           `json:"amount" binding:"required"` // 金额
	PosSide         futuresassetpb.PosSide    `json:"pos_side"`                  // 仓位方向
	OrderTime       int64                     `json:"order_time"`                // 委托创建时间
	Leverage        int                       `json:"leverage"`                  // 杠杆倍数
	MarginMode      futuresassetpb.MarginMode `json:"margin_mode"`               // 仓位模式 (1:全仓 2:逐仓)
	UnfrozenMargin  decimal.Decimal           `json:"unfrozen_margin"`           // 解锁冻结
	IsInnerCall     int                       `json:"is_inner_call"`             // 是否为服务内部调用下单(爆仓) 1:内部服务下单
	IsErr           bool                      `json:"is_err"`                    // 是否错误单解锁
	PositionMode    int32                     `json:"hold_mode"`                 // 持仓模式 1.双向 2.单向 委托接口需要加参数
	IsLimitOrder    int                       `json:"is_limit_order"`            // 是否是限价 1是 2不是
	LiquidationType int                       `json:"liquidation_type"`          // 强平类型
	AwardOpIds      []string                  `json:"award_op_ids"`              // 体验金券ID
	TrialIsEnd      bool                      `json:"trial_is_end"`              // 体验金是否成交
}

type UnLockParam struct {
	UID             string                      `json:"uid"`              // 用户ID
	UserType        futuresassetpb.UserType     `json:"user_type"`        // 用户类型
	OrderId         string                      `json:"order_id"`         // 委托单ID
	Symbol          string                      `json:"symbol"`           // 币对
	Currency        string                      `json:"currency"`         // 计价币种
	Amount          decimal.Decimal             `json:"amount"`           // 数量
	MarginMode      futuresassetpb.MarginMode   `json:"margin_mode"`      // 仓位模式 (1:全仓 2:逐仓)
	IsInnerCall     int32                       `json:"is_inner_call"`    // 是否为服务内部调用下单(爆仓) 1:内部服务下单
	PositionMode    futuresassetpb.PositionMode `json:"position_mode"`    // 持仓模式 (1.双向 2.单向)
	LiquidationType int32                       `json:"liquidation_type"` // 强平类型
	AwardOpIds      []string                    `json:"award_op_ids"`     // 体验金券ID
	TrialIsEnd      bool                        `json:"trial_is_end"`     // 体验金是否成交
}

type BatchUnlockParam struct {
	UID          string                      `json:"uid"`           // 用户ID
	UserType     futuresassetpb.UserType     `json:"user_type"`     // 用户类型
	Symbol       string                      `json:"symbol"`        // 币对
	Currency     string                      `json:"currency"`      // 计价币种
	MarginMode   futuresassetpb.MarginMode   `json:"margin_mode"`   // 仓位模式 (1:全仓 2:逐仓)
	PositionMode futuresassetpb.PositionMode `json:"position_mode"` // 持仓模式 (1.双向 2.单向)

	Batchs []*UnLockParam `json:"batchs"`
}

type BatchUnlockReply struct {
	AssetSwap   *repository.AssetSwap `json:"asset_swap"`
	SuccessList []*UnLockParam        `json:"success_list"`
	FailedList  []*UnLockParam        `json:"failed_list"`
}
