package usecase

import (
	"context"

	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"

	"github.com/shopspring/decimal"
)

type ProfitLossUseCase interface {
	GetPLRecord(ctx context.Context, req *repository.ReqPLRecord) (repository.ResPLRecord, error)
	GetPLTrade(ctx context.Context, req *repository.ReqPLRecord) ([]entity.ProfitLoss, error)
	GetTotalProfit(ctx context.Context, uid string) decimal.Decimal
	GetTotalSubsidy(ctx context.Context) (entity.TotalProfit, error)
}
