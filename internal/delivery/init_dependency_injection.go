package delivery

import (
	"fmt"

	"futures-asset/internal/repository"
	"futures-asset/internal/usecase"
)

// registerUseCaseAndRepo register use case and repository layer object.
func (a *API) registerUseCaseAndRepo() error {
	if err := a.container.Provide(repository.NewAssetRepository); err != nil {
		return fmt.Errorf("NewAssetRepository failed :%w", err)
	}
	if err := a.container.Provide(repository.NewBillRepository); err != nil {
		return fmt.Errorf("NewBillRepository failed :%w", err)
	}
	if err := a.container.Provide(repository.NewBurstRepository); err != nil {
		return fmt.Errorf("NewBurstRepository failed :%w", err)
	}
	if err := a.container.Provide(repository.NewCacheRepository); err != nil {
		return fmt.Errorf("NewCacheRepository failed :%w", err)
	}
	if err := a.container.Provide(repository.NewFormulaRepository); err != nil {
		return fmt.Errorf("NewFormulaRepository failed :%w", err)
	}
	if err := a.container.Provide(repository.NewFuturesEngineRepository); err != nil {
		return fmt.Errorf("NewFuturesEngineRepository failed :%w", err)
	}
	if err := a.container.Provide(repository.NewSettingRepository); err != nil {
		return fmt.Errorf("NewSettingRepository failed :%w", err)
	}
	if err := a.container.Provide(repository.NewMemberRepository); err != nil {
		return fmt.Errorf("NewMemberRepository failed :%w", err)
	}
	if err := a.container.Provide(repository.NewPositionRepository); err != nil {
		return fmt.Errorf("NewPositionRepository failed :%w", err)
	}
	if err := a.container.Provide(usecase.NewPositionUseCase); err != nil {
		return fmt.Errorf("NewPositionUseCase failed :%w", err)
	}
	if err := a.container.Provide(repository.NewPriceRepository); err != nil {
		return fmt.Errorf("NewPriceRepository failed :%w", err)
	}
	if err := a.container.Provide(repository.NewProfitLossRepository); err != nil {
		return fmt.Errorf("NewProfitLossRepository failed :%w", err)
	}
	if err := a.container.Provide(repository.NewOptionRepository); err != nil {
		return fmt.Errorf("NewOptionRepository failed :%w", err)
	}
	if err := a.container.Provide(repository.NewFundingRepository); err != nil {
		return fmt.Errorf("NewFundingRepository failed :%w", err)
	}
	if err := a.container.Provide(repository.NewTrialRepository); err != nil {
		return fmt.Errorf("NewTrialRepository failed :%w", err)
	}
	if err := a.container.Provide(repository.NewUserRepository); err != nil {
		return fmt.Errorf("NewUserRepository failed :%w", err)
	}

	if err := a.container.Provide(usecase.NewAssetUseCase); err != nil {
		return fmt.Errorf("NewAssetUseCase failed :%w", err)
	}
	if err := a.container.Provide(usecase.NewBurstUseCase); err != nil {
		return fmt.Errorf("NewBurstUseCase failed :%w", err)
	}
	if err := a.container.Provide(usecase.NewFundingUseCase); err != nil {
		return fmt.Errorf("NewFundingUseCase failed :%w", err)
	}
	if err := a.container.Provide(usecase.NewOptionDemoUseCase); err != nil {
		return fmt.Errorf("NewOptionDemoUseCase failed :%w", err)
	}
	if err := a.container.Provide(usecase.NewOptionUseCase); err != nil {
		return fmt.Errorf("NewOptionUseCase failed :%w", err)
	}
	if err := a.container.Provide(usecase.NewProfitLossUseCase); err != nil {
		return fmt.Errorf("NewProfitLossUseCase failed :%w", err)
	}
	if err := a.container.Provide(usecase.NewUserUseCase); err != nil {
		return fmt.Errorf("NewUserUseCase failed :%w", err)
	}
	if err := a.container.Provide(usecase.NewTrialUseCase); err != nil {
		return fmt.Errorf("NewTrialUseCase failed :%w", err)
	}

	return nil
}
