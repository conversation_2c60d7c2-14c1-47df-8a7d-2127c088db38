package response

import (
	"errors"
	"net/http"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/usecase"
)

var errUnexpected = errors.New("unexpected error")

func ServiceErrorToErrorResp(err error) (int, *Basic) {
	var serviceError usecase.ServiceError
	if errors.As(err, &serviceError) {
		resp := New(serviceError.ErrorCode(),
			WithMsg(serviceError.Error()),
			WithErrorParam(serviceError.ErrorParam()),
		)

		return serviceError.HTTPStatusCode(), resp
	}

	return http.StatusInternalServerError, NewError(domain.InternalError, err)
}
