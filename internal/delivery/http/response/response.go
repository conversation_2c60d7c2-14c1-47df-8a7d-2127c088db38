package response

import "futures-asset/internal/domain"

type Basic struct {
	Status *Status `json:"status"`

	Data interface{} `json:"data"`
}

func NewSuccess(data interface{}) *Basic {
	return &Basic{
		Status: NewStatus(domain.CodeOk).WithMsg("success"),
		Data:   data,
	}
}

func NewError(code domain.Code, err error) *Basic {
	return &Basic{
		Status: NewStatus(code).WithMsg(err.Error()),
		Data:   struct{}{},
	}
}

type Option func(basic *Basic)

func New(code domain.Code, opts ...Option) *Basic {
	resp := &Basic{
		Status: NewStatus(code),
		Data:   struct{}{},
	}

	for _, opt := range opts {
		opt(resp)
	}

	return resp
}

func WithData(data interface{}) Option {
	return func(basic *Basic) {
		basic.Data = data
	}
}

func WithMsg(msg string) Option {
	return func(basic *Basic) {
		basic.Status.WithMsg(msg)
	}
}

func WithErrorParam(errorParam interface{}) Option {
	return func(basic *Basic) {
		if errorParam == nil {
			errorParam = struct{}{}
		}

		basic.Status.Error = errorParam
	}
}
