package response

import "futures-asset/internal/domain"

type Status struct {
	Code     domain.Code `json:"code"`
	Error    interface{} `json:"error"`
	Messages string      `json:"messages"`
}

func NewStatus(code domain.Code) *Status {
	return &Status{
		Code:  code,
		Error: struct{}{},
	}
}

func (s *Status) WithMsg(msg string) *Status {
	s.Messages = msg

	return s
}

func (s *Status) WithErrorParam(data interface{}) *Status {
	if data != nil {
		s.Error = data
	}

	return s
}
