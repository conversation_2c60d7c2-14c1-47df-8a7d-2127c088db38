package http

import (
	"errors"
	"fmt"
	"net/http"

	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/delivery/http/request"
	"futures-asset/internal/delivery/http/response"
	"futures-asset/internal/domain"
	"futures-asset/internal/libs/pager"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func (handler *dataHandler) GetTrialAssetList(c *gin.Context) {
	var param payload.TAListReq
	if err := request.ShouldBindQuery(c, &param); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	code := param.ParamCheck()
	if code != http.StatusOK {
		logrus.Error(fmt.Sprintf("check param err.code:%+v, param:%+v", code, param))
		c.JSON(http.StatusBadRequest, response.NewError(code, errors.New(domain.ErrMsg[code])))

		return
	}
	data, err := handler.trialUseCase.GetTrialAssetList(c.Request.Context(), param.ToUsecase())
	if err != nil {
		logrus.Error(fmt.Sprintf("GetTrialAssetList err.code:%+v, param:%+v", code, param))
		c.JSON(http.StatusBadRequest, response.NewError(code, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(data))
}

func (handler *dataHandler) GetNoInvalidTrial(c *gin.Context) {
	var param payload.GetNoInvalidTrialReq
	if err := request.ShouldBindQuery(c, &param); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	code := param.ParamCheck()
	if code != http.StatusOK {
		logrus.Error(fmt.Sprintf("check param err.code:%+v, param:%+v", code, param))
		c.JSON(http.StatusBadRequest, response.NewError(code, errors.New(domain.ErrMsg[code])))
		return
	}

	data, err := handler.trialUseCase.GetNoInvalidTrial(c.Request.Context(), param.ToUsecase())
	if err != nil {
		logrus.Error(fmt.Sprintf("GetNoInvalidTrial err.code:%+v, param:%+v", code, param))
		c.JSON(http.StatusBadRequest, response.NewError(code, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(data))
}

func (handler *dataHandler) GetTrialAssetSummaryList(c *gin.Context) {
	var req payload.GetTrialAssetSummaryListReq
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}
	// 参数检查
	if req.PageSize <= 0 || req.PageIndex <= 0 {
		logrus.Error(fmt.Sprintf("GetTrialAssetSummaryList page error pageNUm: %d pageSize: %d", req.PageIndex, req.PageSize))
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(domain.ErrMsg[domain.CodeParamInvalid])))

		return
	}
	// 体验金汇总数据
	list, total, err := handler.trialUseCase.GetTrialAssetSummaryList(c.Request.Context(), req.ToUsecase())
	if err != nil {
		logrus.Error(fmt.Sprintf("GetTrialAssetSummaryList err:%+v", err))
		c.JSON(http.StatusBadRequest, response.NewError(domain.InternalError, err))

		return
	}

	res := &payload.GetTrialAssetSummaryListRes{
		SummaryInfos: list,
		Page: pager.Page{
			PageIndex: req.PageIndex,
			PageSize:  req.PageSize,
			Total:     total,
		},
	}
	c.JSON(http.StatusOK, res)
}

func (handler *dataHandler) GetTrialAssetDetailList(c *gin.Context) {
	var req payload.GetTrialAssetDetailListReq
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	// 参数检查
	if req.PageSize <= 0 || req.PageIndex <= 0 {
		logrus.Error(fmt.Sprintf("GetTrialAssetDetailList page error pageNUm: %d pageSize: %d", req.PageIndex, req.PageSize))
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(domain.ErrMsg[domain.CodeParamInvalid])))
		return
	}

	// 体验金券类型
	var trialTicketType int
	switch req.TrialTicketType {
	case domain.TrialAssetTypeLoop, domain.TrialAssetTypeOnce:
		trialTicketType = req.TrialTicketType
		break
	default:
		trialTicketType = 0
	}
	// 体验金明细数据
	list, total, err := handler.trialUseCase.GetTrialAssetDetailList(c.Request.Context(), req.ToUsecase(trialTicketType))
	if err != nil {
		logrus.Error(fmt.Sprintf("GetTrialAssetDetailList get db err: %v", err))
		c.JSON(http.StatusBadRequest, response.NewError(domain.InternalError, err))

		return
	}
	// 持仓单号
	for _, trialAssetDetail := range list {
		posPartInfos, err := handler.positionRepo.GetPosPartInfo(c.Request.Context(), trialAssetDetail.TrialTicketOrderId)
		if err != nil {
			logrus.Error(fmt.Sprintf("GetTrialAssetDetailList GetPosPartInfo err: %v", err))
			continue
		}
		ids := make([]string, 0)
		for _, posPartInfo := range posPartInfos {
			ids = append(ids, posPartInfo.Id)
		}
		trialAssetDetail.PosOrderIds = ids
	}

	res := &payload.GetTrialAssetDetailListRes{
		DetailInfos: list,
		Page: pager.Page{
			PageIndex: req.PageIndex,
			PageSize:  req.PageSize,
			Total:     total,
		},
	}

	c.JSON(http.StatusOK, res)
}

func (handler *dataHandler) GetTrialAssetLastUpdate(c *gin.Context) {
	var req payload.GetTrialAssetLastUpdateTimeReq
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	res := &payload.GetTrialAssetLastUpdateTimeRes{}
	lastTime, err := handler.trialUseCase.GetLastUpdateTime(c.Request.Context(), req.UID)
	if err != nil {
		logrus.Errorf("GetTrialAssetLastUpdate GetLastUpdateTime err:%+v", err)
		res.LastTime = 0
	} else {
		res.LastTime = lastTime
	}

	c.JSON(http.StatusOK, res)
}
