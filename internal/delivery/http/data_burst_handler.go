package http

import (
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/delivery/http/request"
	"futures-asset/internal/delivery/http/response"
	"futures-asset/internal/domain"
	"futures-asset/internal/libs/pager"
	"futures-asset/util"

	"github.com/gin-gonic/gin"
)

func (handler *dataHandler) BurstList(c *gin.Context) {
	var req payload.BurstListParam

	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	burstList := payload.BurstListReply{
		Page: pager.Page{
			Total:     0,
			PageIndex: req.PageIndex,
			PageSize:  req.PageSize,
		},
		List: make([]payload.BurstRow, 0),
	}

	if req.PageSize < 1 {
		req.PageSize = 10
	}

	conditions := make(map[string]interface{})
	if len(req.Currency) > 0 {
		conditions["currency"] = req.Currency
	}
	if len(req.Base) > 0 {
		conditions["base"] = strings.ToUpper(req.Base)
	}
	if len(req.Quote) > 0 {
		conditions["quote"] = strings.ToUpper(req.Quote)
	}
	if req.PosSide > 0 {
		conditions["pos_type"] = req.PosSide
	}
	if len(req.PosId) > 0 {
		conditions["pos_id"] = req.PosId
	}
	if req.MarginMode > 0 {
		conditions["margin_mode"] = req.MarginMode
	}
	if len(req.LiquidationType) > 0 {
		conditions["liquidation_type"] = req.LiquidationType
	}
	if len(req.UID) > 0 {
		conditions["user_id"] = req.UID
	}
	if len(req.UserType) > 0 {
		conditions["user_type"] = req.UserType
	}
	if len(req.BurstId) > 0 {
		conditions["burst_id"] = req.BurstId
	}
	if req.Status > 0 {
		conditions["status"] = req.Status
	}
	ranges := make(map[string]map[string]interface{})
	if _, ok := ranges["burst_time"]; !ok {
		ranges["burst_time"] = make(map[string]interface{})
	}
	if req.BurstStartTime > 0 {
		ranges["burst_time"]["gte"] = fmt.Sprintf("%d000000000", req.BurstStartTime)
	}
	if req.BurstEndTime > 0 {
		ranges["burst_time"]["lte"] = fmt.Sprintf("%d999999999", req.BurstEndTime)
	}
	if len(ranges["burst_time"]) < 2 {
		if _, startTimeOk := ranges["burst_time"]["gte"]; startTimeOk {
			if _, endTimeOk := ranges["burst_time"]["lte"]; !endTimeOk {
				startTimestamp := req.BurstStartTime + (30 * 86400)
				ranges["burst_time"]["lte"] = fmt.Sprintf("%d999999999", startTimestamp)
			}
		} else {
			if _, endTimeOk := ranges["burst_time"]["lte"]; endTimeOk {
				endTimestamp := req.BurstEndTime - (30 * 86400)
				ranges["burst_time"]["gte"] = fmt.Sprintf("%d000000000", endTimestamp)
			} else {
				nowTime := time.Now()
				endTimestamp := time.Date(
					nowTime.Year(), nowTime.Month(), nowTime.Day(),
					0, 0, 0, 0, time.Local)
				startTimestamp := endTimestamp.AddDate(0, 0, -30)
				ranges["burst_time"]["gte"] = fmt.Sprintf("%d", startTimestamp.UnixNano())
				ranges["burst_time"]["lte"] = fmt.Sprintf("%d", endTimestamp.AddDate(0, 0, 1).Add(-time.Nanosecond).UnixNano())
			}
		}
	}

	total, burstInfos := handler.burstUseCase.SearchBurstInfos(c.Request.Context(), conditions, ranges, req.PageIndex, req.PageSize)
	burstList.Total = total
	burstRowList := make([]payload.BurstRow, 0)
	for _, info := range burstInfos {
		burstRowList = append(burstRowList, payload.BurstRow{
			Id: fmt.Sprintf("%s", info.Id),
			// 强平触发时间
			BurstTime: info.BurstTime / 1e9,
			// 持仓单号
			PosId: info.PosId,
			// UID
			UID: info.UID,
			// 用户类型
			UserType: info.UserType,
			// 强平单号
			BurstId: info.BurstId,
			// 合约类型
			AccountType: info.AccountType,
			// 合约交易对
			ContractCode: util.ContractCode(info.Base, info.Quote),
			// 委托方式
			OrderType: info.OrderType,
			// 保证金模式
			MarginMode: int32(info.MarginMode),
			// 平仓类型
			PosSide: info.PosType,
			// 杠杆倍数
			Leverage: info.Leverage,
			// 强平类型
			LiquidationType: info.LiquidationType,
			// 强平状态
			Status: info.Status,
			// 强平单成交总额
			LiquidationDealValue: info.LiquidationDealValue,
			// 爆仓强平费
			LiquidationFee: info.LiquidationFee,
			// 对手方减仓数量
			ForceRivalAmount: info.ForceRivalAmount,
			// 穿仓补贴时间
			OverflowTime: info.OverflowTime / 1e9,
			// 穿仓补贴信息
			OverflowInfo: info.OverflowInfo,
			// 补贴数量折合
			OverflowValue: info.OverflowValue,
		})
	}
	burstList.List = burstRowList

	c.JSON(http.StatusOK, burstList)
}

func (handler *dataHandler) BurstStat(c *gin.Context) {
	var req payload.BurstStatParam
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	if req.StartTime < 1 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("startTime not found")))

		return
	}
	if req.EndTime < 1 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("endTime not found")))
		return
	}
	reqStartTime := time.Unix(req.StartTime, 0).Local().Truncate(time.Second)
	reqEndTime := time.Unix(req.EndTime, 0).Local().Truncate(time.Second)
	tableNameList := make([]string, 0)
	endTime := time.Unix(req.EndTime+1, 0).Local()
	tempTime := time.Unix(req.StartTime, 0).Local()
	for tempTime.Before(endTime) {
		tempTable := fmt.Sprintf("burst_swap%s", tempTime.Format("20060102"))
		tableNameList = append(tableNameList, tempTable)
		tempTime = tempTime.AddDate(0, 0, 1)
	}
	statBurstInfo := handler.burstUseCase.StatBurstTimesByTableNameList(c.Request.Context(), reqStartTime, reqEndTime)

	c.JSON(http.StatusOK, statBurstInfo)
}

// func (handler *dataHandler) IndexPriceInfo(c *gin.Context) {
// 	var req payload.IndexPriceInfoParam
// 	if err := request.ShouldBindQuery(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

// 		return
// 	}

// 	indexPriceListReply := payload.IndexPriceInfoReply{
// 		Total: 0,
// 		List:  make([]payload.IndexPriceRow, 0),
// 	}

// 	contractSettings, err := setting.Service.GetAllPairSettingInfo()
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

// 		return
// 	}
// 	totalDataMap := make(map[string]payload.IndexPriceRow)
// 	for contractCode, settingConfig := range contractSettings {
// 		settingBase, settingQuote := util.BaseQuote(contractCode)
// 		if len(req.Base) > 0 && strings.ToUpper(req.Base) != settingBase {
// 			continue
// 		}
// 		if len(req.Quote) > 0 && strings.ToUpper(req.Quote) != settingQuote {
// 			continue
// 		}

// 		rowData := payload.IndexPriceRow{
// 			ContractCode: contractCode,
// 		}

// 		markInfoStr, err := redislib.Redis().HGet(cache.GetUsingMarkPriceRedisKey(), contractCode)
// 		if err != nil {
// 			logrus.Errorln(fmt.Sprintf("IndexPriceList HGet %s %s error: %s", cache.GetUsingMarkPriceRedisKey(), contractCode, err.Error()))
// 			markInfoStr = "{}"
// 		}
// 		/*
// 			{"":"the price difference is too large","ma_basis":"374.9655828766735967","":"36193.86324"}
// 		*/
// 		markInfo := struct {
// 			IndexPrice     decimal.Decimal `json:"index_price"`
// 			MarkPrice      decimal.Decimal `json:"mark_price"`
// 			MaBasis        decimal.Decimal `json:"ma_basis"`
// 			IndexException string          `json:"index_exception"`
// 		}{}
// 		if settingConfig.PricePrecision > 0 {
// 			markInfo.MarkPrice = markInfo.MarkPrice.Truncate(settingConfig.PricePrecision)
// 			markInfo.MaBasis = markInfo.MaBasis.Truncate(settingConfig.PricePrecision)
// 		}
// 		json.Unmarshal([]byte(markInfoStr), &markInfo)
// 		rowData.MarkPrice = markInfo.MarkPrice.String()
// 		rowData.MaBasis = markInfo.MaBasis.String()

// 		if len(markInfo.IndexException) > 0 {
// 			if req.Status > 0 && req.Status == 1 {
// 				continue
// 			}
// 			rowData.Platform = "fameex"
// 			rowData.IndexPrice = markInfo.IndexPrice.String()
// 			rowData.Status = 2
// 			rowData.Exception = markInfo.IndexException
// 		} else {
// 			if req.Status > 0 && req.Status == 2 {
// 				continue
// 			}
// 			indexInfoStr, err := redislib.Redis().HGet(cachekey.GetPlatformIndexPriceComposeInfoRedisKey(), contractCode)
// 			if err != nil {
// 				logrus.Errorln(fmt.Sprintf("IndexPriceList HGet %s %s error: %s", cachekey.GetPlatformIndexPriceComposeInfoRedisKey(), contractCode, err.Error()))
// 				indexInfoStr = "{}"
// 			}
// 			/*
// 				{"index_price":"1217.66238176","platforms":["binance"],"type":"index"}
// 			*/
// 			indexInfo := struct {
// 				IndexPrice decimal.Decimal `json:"index_price"`
// 				Platforms  []string        `json:"platforms"`
// 				Type       string          `json:"type"`
// 			}{}
// 			err = json.Unmarshal([]byte(indexInfoStr), &indexInfo)
// 			if err != nil {
// 				logrus.Errorln(fmt.Sprintf("IndexPriceList Unmarshal %s %s error: %s", indexInfoStr, contractCode, err.Error()))
// 				continue
// 			}
// 			if settingConfig.PricePrecision > 0 {
// 				indexInfo.IndexPrice = indexInfo.IndexPrice.Truncate(settingConfig.PricePrecision)
// 			}
// 			platNames := []string{}
// 			if len(indexInfo.Platforms) > 0 {
// 				for _, pInfo := range indexInfo.Platforms {
// 					temp := struct {
// 						Name   string
// 						Price  decimal.Decimal
// 						Weight decimal.Decimal
// 					}{}
// 					err = json.Unmarshal([]byte(pInfo), &temp)
// 					if err != nil {
// 						logrus.Errorln(fmt.Sprintf("IndexPriceList Unmarshal %s %s error: %s", pInfo, contractCode, err.Error()))
// 						continue
// 					}
// 					if len(temp.Name) > 0 {
// 						platNames = append(platNames, temp.Name)
// 					}
// 				}
// 			}
// 			rowData.Platform = strings.Join(platNames, ", ")
// 			rowData.IndexPrice = indexInfo.IndexPrice.String()
// 			rowData.Status = 1
// 		}

// 		totalDataMap[contractCode] = rowData
// 	}

// 	coinPairList := make([]string, 0)
// 	for coinPair := range totalDataMap {
// 		coinPairList = append(coinPairList, coinPair)
// 	}
// 	sort.Strings(coinPairList)
// 	total := len(coinPairList)
// 	if req.PageSize == 0 {
// 		req.PageSize = 10
// 	}
// 	totalPage := int(math.Ceil(float64(total) / float64(req.PageSize)))
// 	if req.PageIndex == 0 {
// 		req.PageIndex = 1
// 	}
// 	if req.PageIndex > totalPage {
// 		req.PageIndex = totalPage
// 	}
// 	start := (req.PageIndex - 1) * req.PageSize
// 	end := start + req.PageSize
// 	// 最终币对列表
// 	coinPairList = coinPairList[start:end]

// 	indexPriceList := make([]payload.IndexPriceRow, 0)
// 	for _, coinPair := range coinPairList {
// 		indexPriceList = append(indexPriceList, totalDataMap[strings.ToUpper(coinPair)])
// 	}

// 	indexPriceListReply.Total = int64(total)
// 	indexPriceListReply.List = indexPriceList

// 	c.JSON(http.StatusOK, indexPriceListReply)
// }

// func (handler *dataHandler) IndexPriceList(c *gin.Context) {
// 	var req payload.IndexPriceListParam
// 	if err := request.ShouldBindQuery(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

// 		return
// 	}

// 	contractSettings, err := setting.Service.GetAllPairSettingInfo()
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

// 		return
// 	}

// 	dictMap := make(map[string]string)
// 	for _, currency := range domain.CurrencyList {
// 		dictMap[util.ContractCode(strings.ToUpper(currency), "USDT")] = currency
// 	}
// 	for _, settingInfo := range contractSettings {
// 		_, ok := dictMap[util.ContractCode(settingInfo.Base, settingInfo.Quote)]
// 		if !ok {
// 			dictMap[util.ContractCode(settingInfo.Base, settingInfo.Quote)] = settingInfo.Base
// 		}
// 	}

// 	platformIndexPriceMap, err := redislib.Redis().HGetAll(cachekey.GetPlatformIndexPriceRedisKey())
// 	indexPriceMap := make(map[string]string)

// 	for contractCode := range dictMap {
// 		if indexPrice, ok := platformIndexPriceMap[contractCode]; ok {
// 			indexPriceMap[contractCode] = indexPrice
// 		}
// 	}

// 	c.JSON(http.StatusOK, indexPriceMap)
// }
