package http

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"strings"

	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/delivery/http/request"
	"futures-asset/internal/delivery/http/response"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"
	"futures-asset/internal/libs/pager"
	"futures-asset/pkg/eslib"

	"github.com/gin-gonic/gin"
	"go.uber.org/dig"
)

type OptionHandlerParam struct {
	dig.In

	OptionUseCase usecase.OptionUseCase
	OptionRepo    repository.OptionRepository
}

type optionHandler struct {
	optionUseCase usecase.OptionUseCase
	optionRepo    repository.OptionRepository
}

func newOptionHandler(param OptionHandlerParam) *optionHandler {
	return &optionHandler{
		optionUseCase: param.OptionUseCase,
		optionRepo:    param.OptionRepo,
	}
}

func (handler *optionHandler) transactionHour(c *gin.Context) {
	c.<PERSON>(http.StatusOK, response.NewSuccess(gin.H{}))
}

// initOption 期权初始化
func (handler *optionHandler) initOption(c *gin.Context) {
	var req payload.UserParam
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	if len(req.UID) <= 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("param err: "+req.UID)))

		return
	}

	err := handler.optionUseCase.InitOption(c.Request.Context(), req.ToUseCaseParam())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(gin.H{}))
}

// asset 期权资产
func (handler *optionHandler) asset(c *gin.Context) {
	var req payload.CurrencyParam
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	if len(req.UID) <= 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("param err: "+req.UID)))

		return
	}

	if len(req.Currency) <= 0 {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("param err: "+req.Currency)))

		return
	}

	reply, err := handler.optionUseCase.Asset(c.Request.Context(), req.ToUseCaseParam())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrOptionAsset, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(reply))
}

// func (handler *optionHandler) trade(c *gin.Context) {
// 	var req payload.OptionTradeParam
// 	if err := request.ShouldBindQuery(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

// 		return
// 	}

// 	if checkOptionTradeParam(&req) {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("option param err")))

// 		return
// 	}
// 	req.Base, req.Quote = strings.ToUpper(req.Base), strings.ToUpper(req.Quote)
// 	code, reply, err := binaryoption.GetTrader(req.OptionType).Trade(req)
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(code, err))

// 		return
// 	}
// 	c.JSON(http.StatusOK, response.NewSuccess(reply))
// }

// func (handler *optionHandler) exercise(c *gin.Context) {
// 	var req payload.ExerciseParam
// 	if err := request.ShouldBindQuery(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

// 		return
// 	}

// 	if checkExerciseParam(&req) {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New("option param err")))

// 		return
// 	}

// 	req.Base, req.Quote = strings.ToUpper(req.Base), strings.ToUpper(req.Quote)
// 	code, reply, err := binaryoption.GetTrader(req.OptionType).Exercise(req)
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(code, err))

// 		return
// 	}

// 	c.JSON(http.StatusOK, response.NewSuccess(reply))
// }

// func (handler *optionHandler) demoAsset(c *gin.Context) {
// 	var req payload.UserParam
// 	if err := request.ShouldBindQuery(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

// 		return
// 	}

// 	balance, err := binaryoption.DemoAsset(req.UID)
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrLoadDemoAsset, err))

// 		return
// 	}

// 	c.JSON(http.StatusOK, response.NewSuccess(balance))
// }

func (handler *optionHandler) profitLossRecord(c *gin.Context) {
	var req payload.ReqPLRecord
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}
	code := req.CheckProcessParam()
	if code != http.StatusOK {
		c.JSON(http.StatusBadRequest, response.NewError(code, errors.New(domain.ErrMsg[code])))

		return
	}

	data, err := handler.optionRepo.OptionPnlRecord(c.Request.Context(), req.ToUseCase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrGetPLTrade, err))

		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(data))
}

func (handler *optionHandler) billOption(c *gin.Context) {
	var req payload.BillOptionParam
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

		return
	}

	index := domain.EsOptionBillIndex
	if req.OptionType == domain.OptionTypeDemo {
		index = domain.EsOptionBillDemoIndex
	}

	result := payload.BillOptionReply{Page: pager.Page{PageIndex: req.PageIndex, PageSize: req.PageSize}, List: make([]payload.BillOption, 0)}

	conditions := make(map[string]interface{})
	terms := make(map[string][]interface{})
	if len(req.UID) > 0 {
		conditions["uid"] = req.UID
	}
	if len(req.OperateId) > 0 {
		conditions["operateId"] = req.OperateId
	}
	if len(req.Base) > 0 {
		conditions["base"] = strings.ToUpper(req.Base)
	}
	if len(req.Quote) > 0 {
		conditions["quote"] = strings.ToUpper(req.Quote)
	}
	if req.BillType > 0 {
		conditions["billType"] = req.BillType
	}

	ranges := make(map[string]map[string]interface{})
	if req.StartTime > 0 {
		if _, ok := ranges["operateTime"]; !ok {
			ranges["operateTime"] = make(map[string]interface{})
		}
		ranges["operateTime"]["gte"] = req.StartTime * 1e9
	}
	if req.EndTime > 0 {
		if _, ok := ranges["operateTime"]; !ok {
			ranges["operateTime"] = make(map[string]interface{})
		}
		ranges["operateTime"]["lte"] = req.EndTime * 1e9
	}

	billOption := eslib.New(index).SetFrom((req.PageIndex - 1) * req.PageSize).SetSize(req.PageSize).SetConditions(conditions).
		SetTerms(terms).
		SetRanges(ranges).
		SetSortFields(
			map[string]bool{
				"operateTime": false,
			})

	searchResult, err := billOption.Search(context.Background())
	if err != nil || searchResult == nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrEsSearch, err))

		return
	}

	// 查询总量
	total := searchResult.TotalHits()
	if total > 0 {
		result.Total = total
		for _, hit := range searchResult.Hits.Hits {
			itemByte, err := hit.Source.MarshalJSON()
			if err != nil {
				continue
			}
			var item payload.BillOption
			err = json.Unmarshal(itemByte, &item)
			if err != nil {
				continue
			}

			item.OperateTime = item.OperateTime / 1e9
			result.List = append(result.List, item)
		}
	}

	c.JSON(http.StatusOK, response.NewSuccess(result))
}

func checkOptionTradeParam(param *payload.OptionTradeParam) bool {
	return len(param.Orders) == 0 ||
		len(param.UID) == 0 ||
		len(param.Base) == 0 ||
		len(param.Quote) == 0 ||
		(param.OptionType != domain.OptionTypeFirm && param.OptionType != domain.OptionTypeDemo)
}

func checkExerciseParam(param *payload.ExerciseParam) bool {
	return len(param.Orders) == 0 ||
		len(param.UID) == 0 ||
		len(param.Base) == 0 ||
		len(param.Quote) == 0 ||
		len(param.OptionId) == 0 ||
		(param.OptionType != domain.OptionTypeFirm && param.OptionType != domain.OptionTypeDemo)
}
