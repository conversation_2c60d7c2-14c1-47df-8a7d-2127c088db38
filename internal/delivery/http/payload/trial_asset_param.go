package payload

import (
	"net/http"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/libs/pager"
)

type OperateRecycleTrialAsset struct {
	RecycleOpId string `json:"recycleOpId" binding:"required"` // 回收操作的唯一id 跟后台回收表一一对应
	Currency    string `json:"currency" binding:"required"`    // 回收币种
	UID         string `json:"uid" binding:"required"`         // 回收的用户id
	AwardOpId   string `json:"awardOpId" binding:"required"`   // 体验金领取的唯一id
}

func (slf *OperateRecycleTrialAsset) ToUseCase() *repository.OperateRecycleTrialAsset {
	return &repository.OperateRecycleTrialAsset{
		RecycleOpId: slf.RecycleOpId,
		Currency:    slf.Currency,
		UID:         slf.UID,
		AwardOpId:   slf.AwardOpId,
	}
}

type TAListReq struct {
	pager.Condition

	Type             int    `json:"type"`
	UID              string `json:"uid"`              // 用户id
	Currency         string `json:"currency"`         // 币种 必传
	TrialAssetStatus int    `json:"trialAssetStatus"` // enum TrialAssetStatus
	DisplayStatus    int8   `json:"displayStatus"`    // enum TrialAssetDisplayStatus
	TimeType         int    `json:"timeType"`         // 时间类型 1.领取 2.生效 3.失效
	TimeStart        int64  `json:"timeStart"`        // 起始时间
	TimeEnd          int64  `json:"timeEnd"`          // 结束时间
}

func (slf *TAListReq) ToUsecase() *repository.TAListReq {
	return &repository.TAListReq{
		Condition: pager.Condition{
			PageIndex: slf.PageIndex,
			PageSize:  slf.PageSize,
		},
		Type:             slf.Type,
		UID:              slf.UID,
		Currency:         slf.Currency,
		TrialAssetStatus: slf.TrialAssetStatus,
		DisplayStatus:    slf.DisplayStatus,
		TimeType:         slf.TimeType,
		TimeStart:        slf.TimeStart,
		TimeEnd:          slf.TimeEnd,
	}
}

func (slf *TAListReq) ParamCheck() (code domain.Code) {
	if slf.PageIndex*slf.PageSize == 0 {
		return domain.ErrPageErr
	}
	if slf.TimeStart != 0 || slf.TimeEnd != 0 {
		if slf.TimeType == domain.TATimeTypeAll || slf.TimeType > domain.TATTInvalid {
			// type 给的预期之外的值 就给默认领取时间
			slf.TimeType = domain.TATTAward
		}
	}

	return http.StatusOK
}

type GetNoInvalidTrialReq struct {
	UID        string `json:"uid"`        // 用户id
	Currency   string `json:"currency"`   // 币种
	ActivityId int64  `json:"activityId"` // 活动id
}

func (p *GetNoInvalidTrialReq) ToUsecase() *repository.GetNoInvalidTrialReq {
	return &repository.GetNoInvalidTrialReq{
		UID:        p.UID,
		Currency:   p.Currency,
		ActivityId: p.ActivityId,
	}
}

func (slf *GetNoInvalidTrialReq) ParamCheck() (code domain.Code) {
	if slf.Currency == "" {
		slf.Currency = "USDT"
	}
	if slf.Currency == "" {
		return domain.ErrCurrencyErr
	}
	if slf.UID == "" {
		return domain.ErrUserIdNull
	}

	return http.StatusOK
}

type GetTrialAssetSummaryListReq struct {
	pager.Condition

	UID      string `json:"uid"`
	LastTime int64  `json:"lastTime"`
}

func (p *GetTrialAssetSummaryListReq) ToUsecase() *repository.GetTrialAssetSummaryListReq {
	return &repository.GetTrialAssetSummaryListReq{
		Condition: pager.Condition{
			PageIndex: p.PageIndex,
			PageSize:  p.PageSize,
		},
		UID:      p.UID,
		LastTime: p.LastTime,
	}
}

type GetTrialAssetSummaryListRes struct {
	pager.Page

	SummaryInfos []*entity.TrialAssetSummaryInfo `json:"summaryInfos"`
}

type GetTrialAssetDetailListReq struct {
	pager.Condition

	UID                string `json:"uid"`
	TrialTicketOrderId string `json:"trialTicketOrderId"` // 体验金券码
	ActivityId         int64  `json:"activityId"`         // 活动id
	GetType            int    `json:"getType"`            // 领取方式
	TrialTicketType    int    `json:"trialTicketType"`    // 体验金券类型
	TrialAssetStatus   int    `json:"trialAssetStatus"`   // 体验金券状态
	LastTime           int64  `json:"lastTime"`
}

func (p *GetTrialAssetDetailListReq) ToUsecase(trialTicketType int) *repository.GetTrialAssetDetailListReq {
	return &repository.GetTrialAssetDetailListReq{
		Condition: pager.Condition{
			PageIndex: p.PageIndex,
			PageSize:  p.PageSize,
		},
		UID:                p.UID,
		TrialTicketOrderId: p.TrialTicketOrderId,
		ActivityId:         p.ActivityId,
		GetType:            p.GetType,
		TrialTicketType:    trialTicketType,
		TrialAssetStatus:   p.TrialAssetStatus,
		LastTime:           p.LastTime,
	}
}

type GetTrialAssetDetailListRes struct {
	pager.Page

	DetailInfos []*entity.TrialAssetDetailInfo `json:"list"`
}

type GetTrialAssetLastUpdateTimeReq struct {
	UID string `json:"uid"`
}

type GetTrialAssetLastUpdateTimeRes struct {
	LastTime int64 `json:"lastTime"`
}
