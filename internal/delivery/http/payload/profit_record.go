package payload

import (
	"net/http"
	"time"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/util"

	"github.com/shopspring/decimal"
)

type ReqPLRecord struct {
	UID       string `json:"uid"`       // 用户id
	DaysParam int    `json:"daysParam"` // 默认传0；1.代表最近7天；2.代表最近30天
	StartTime int64  `json:"startTime"` // 起始时间
	EndTime   int64  `json:"endTime"`   // 结束时间
	ValType   int    `json:"valType"`   // 估值类型1.CNY 2.USDT 3.BTC
}

func (slf *ReqPLRecord) ToUseCase() *repository.ReqPLRecord {
	return &repository.ReqPLRecord{
		UID:       slf.UID,
		DaysParam: slf.DaysParam,
		StartTime: slf.StartTime,
		EndTime:   slf.EndTime,
		ValType:   slf.ValType,
	}
}

// CheckProcessParam 参数检测及处理
func (slf *ReqPLRecord) CheckProcessParam() domain.Code {
	if slf.UID == "" {
		return domain.ErrUserIdNull
	}
	if slf.ValType < domain.ValTypeCny || slf.ValType > domain.ValTypeBtc {
		return domain.ErrValType
	}

	// 时间取前一天的数据
	switch slf.DaysParam {
	case domain.DayParamNoLimit:
		if slf.StartTime == 0 && slf.EndTime == 0 {
			return domain.ErrTimeParamErr
		}
	case domain.DayParamSevenDay:
		slf.EndTime = util.OneDayBeginAndEndTimeStamp(time.Now().AddDate(0, 0, -1))
		slf.StartTime = util.OneDayBeginAndEndTimeStamp(time.Now().AddDate(0, 0, -7))
	case domain.DayParamThirtyDay:
		slf.EndTime = util.OneDayBeginAndEndTimeStamp(time.Now().AddDate(0, 0, -1))
		slf.StartTime = util.OneDayBeginAndEndTimeStamp(time.Now().AddDate(0, 0, -30))
	default:
		return domain.ErrDayParam
	}

	return http.StatusOK
}

type (
	ResPLRecord struct {
		TotalProfitLoss   decimal.Decimal    `json:"totalProfitLoss"`   // 自开通合约的累计盈亏
		Valuation         decimal.Decimal    `json:"valuation"`         // 折合
		DaysAnalysis      ProfitLossAnalysis `json:"daysAnalysis"`      // 盈亏分析
		ProfitLossRecords []ProfitLossRecord `json:"profitLossRecords"` // 盈亏数据
	}
	ProfitLossAnalysis struct {
		DaysNum          int             `json:"daysNum"`          // 统计天数
		CumulativeProfit decimal.Decimal `json:"cumulativeProfit"` // 累计盈利
		CumulativeLoss   decimal.Decimal `json:"cumulativeLoss"`   // 累计亏损
		TotalProfitLoss  decimal.Decimal `json:"totalProfitLoss"`  // 净盈亏
		ProfitDays       int64           `json:"profitDays"`       // 盈利天数
		LossDays         int64           `json:"lossDays"`         // 亏损天数
		EqualDays        int64           `json:"equalDays"`        // 盈利和亏损相等的天数
		AverageProfit    decimal.Decimal `json:"averageProfit"`    // 平均盈利
		AverageLoss      decimal.Decimal `json:"averageLoss"`      // 平均亏损
	}
	ProfitLossRecord struct {
		DayTime          int64           `json:"dayTime"`          // 日期时间戳
		NetIn            decimal.Decimal `json:"netIn"`            // 当日净转入
		TodayProfitLoss  decimal.Decimal `json:"todayProfitLoss"`  // 当日盈亏
		CumulativeProfit decimal.Decimal `json:"cumulativeProfit"` // 累计盈亏
	}
)
