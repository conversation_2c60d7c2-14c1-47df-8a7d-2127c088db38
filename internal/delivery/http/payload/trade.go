package payload

import (
	"encoding/json"

	"futures-asset/internal/domain"

	"github.com/shopspring/decimal"
	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
)

type (
	Trade struct {
		UID      string                  `json:"uid"`       // 用户ID
		UserType futuresassetpb.UserType `json:"user_type"` // 用户类型
		Symbol   string                  `json:"symbol"`    // 交易对，如 BTCUSDT
		Currency string                  `json:"currency"`  // 计价币币种，如 USDT

		// 下单维度
		OrderID         string                      `json:"order_id"`
		OrderType       futuresassetpb.OrderType    `json:"order_type"`       // 订单类型：limit / market
		OrderSubType    futuresassetpb.OrderSubType `json:"order_sub_type"`   // 附加类型：stop-limit / ioc / fok / post-only
		Side            futuresassetpb.Side         `json:"side"`             // 委托方向：buy-买 / sell-卖
		PosSide         futuresassetpb.PosSide      `json:"pos_side"`         // 多仓(1), 空仓(2), 双向持仓默认值(3)
		Price           string                      `json:"price"`            // 委托价格（限价单必填）
		Amount          string                      `json:"amount"`           // 委托数量
		Volume          string                      `json:"volume"`           // 委托金额（市价买时使用）
		Leverage        int                         `json:"leverage"`         // 委托金额（市价买时使用）
		LiquidationType domain.LiquidationType      `json:"liquidation_type"` // 爆仓类型 (1:爆仓 2:减仓 3:止盈减仓)

		// 撮合维度
		UnfilledAmount string `json:"unfilled_amount"` // 未成交数量
		UnfilledVolume string `json:"unfilled_volume"` // 未成交金额
		FilledAmount   string `json:"filled_amount"`   // 已成交数量
		FilledVolume   string `json:"filled_volume"`   // 已成交金额
		AvgPrice       string `json:"avg_price"`       // 平均成交价

		// 状态维度
		OrderStatus  futuresassetpb.OrderStatus  `json:"order_status"`  // 委托状态：submitted / filled / canceled / ...
		OrderSource  futuresassetpb.OrderSource  `json:"order_source"`  // 下单来源标记（APP、API、策略等）
		CancelSource futuresassetpb.CancelSource `json:"cancel_source"` // 记录撤销来源

		// 仓位纬度
		PositionMode futuresassetpb.PositionMode `json:"position_mode"` // 仓位模式：双向持仓(Hedge); 单向持仓 (One Way)
		MarginMode   futuresassetpb.MarginMode   `json:"margin_mode"`   // 保证金模式：全仓（Cross）或逐仓（Isolated）

		// 手续费维度
		FeeRateMaker string `json:"fee_rate_maker"` // Maker手续费率
		FeeRateTaker string `json:"fee_rate_taker"` // Taker手续费率

		// 时间纬度
		CreatedAt   int64 `json:"created_at"`   // 创建时间（时间戳）
		UpdatedAt   int64 `json:"updated_at"`   // 更新时间（时间戳）
		SubmittedAt int64 `json:"submitted_at"` // 提交时间（时间戳）

		StrategyId       string          `json:"strategy_id"`        // 策略ID
		BurstId          string          `json:"burst_id"`           // 爆仓ID
		BurstTime        int64           `json:"burst_time"`         // 爆仓时间
		DeductFee        decimal.Decimal `json:"deduct_fee"`         // 抵扣手续费
		DeductAwardId    string          `json:"deduct_award_id"`    // 抵扣奖品id
		DeductType       int             `json:"deduct_type"`        // 抵扣类型
		AgentUserId      string          `json:"agent_user_id"`      // 代理人id
		AgentStatus      int             `json:"agent_status"`       // 代理人状态-1:启用,2:禁用
		AgentChannelCode string          `json:"agent_channel_code"` // 代理人渠道码
		AwardOpIds       []string        `json:"award_op_ids"`       // 体验金资产ID
		IsRobotSelfTrade bool            // 是否机器人类型自成交
	}
	// TradeCommon contains trade common fields.
	TradeCommon struct {
		Symbol       string          `json:"symbol"`        // 币对
		Currency     string          `json:"currency"`      // 计价币币种 (USDT, USDC)
		TradeId      string          `json:"trade_id"`      // 成交ID
		Price        decimal.Decimal `json:"price"`         // 成交价格
		Amount       decimal.Decimal `json:"amount"`        // 成交数量
		ConvertMoney decimal.Decimal `json:"convert_money"` // 转换金额
		TradeType    int32           `json:"trade_type"`    // 是否暗成交(撮合不进深度)
		OperateTime  int64           `json:"operate_time"`  // 操作时间
		IsErr        bool            `json:"is_err"`        // 是否成交错误单
	}
	// TradeParam contains binded and validated data.
	TradeParam struct {
		TradeCommon
		Taker Trade `json:"taker"` // Taker
		Maker Trade `json:"maker"` // Maker
	}
	TradeReply struct {
		TradeId      string `json:"trade_id"`   // 成交ID
		TradeType    int32  `json:"trade_type"` // 是否暗成交(撮合不进深度)
		ContractCode string `json:"contract_code"`
		Taker        Reply  `json:"taker"`
		Maker        Reply  `json:"maker"`
	}
)

// MarshalBinary implement encoding.BinaryMarshaler for redis
func (slf TradeParam) MarshalBinary() ([]byte, error) {
	return json.Marshal(slf)
}

// UnmarshalBinary implement encoding.BinaryUnmarshaler for redis
func (slf TradeParam) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, &slf)
}
