package payload

import (
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/usecase"

	"github.com/shopspring/decimal"
)

type (
	ReqTotalBalance struct {
		UID      string `json:"uid" binding:"required"`      // 用户id
		Currency string `json:"currency" binding:"required"` // 折合的币种
	}

	ReqAssetDetail struct {
		UID string `json:"uid" binding:"required"` // 用户id
	}
)

func (p *ReqTotalBalance) ToUseCase() usecase.ReqTotalBalance {
	return usecase.ReqTotalBalance{
		UID:      p.UID,
		Currency: p.Currency,
	}
}

func (p *ReqAssetDetail) ToUseCase() usecase.ReqAssetDetail {
	return usecase.ReqAssetDetail{
		UID: p.UID,
	}
}

type (
	ReqAsset struct {
		Currency string `json:"currency"`
		UID      string `json:"uid"`
		ValType  int    `json:"valType"` // 估值类型1.CNY 2.USDT 3.BTC
	}

	RespAssetsData struct {
		Code int     `json:"code"`
		Msg  string  `json:"msg"`
		Data []Asset `json:"data"`
	}

	Asset struct {
		Currency       string          `json:"currency"`       // 币种名称
		TotalTotal     decimal.Decimal `json:"totalTotal"`     // 币种总量 加上总的未实现盈亏
		BTCValuation   decimal.Decimal `json:"btcValuation"`   // 币种BTC总量折合
		TotalValuation decimal.Decimal `json:"totalValuation"` // 币种折合：根据valType决定
		TotalProfit    decimal.Decimal `json:"totalProfit"`    // 累计盈亏
	}
	ContractTransAmount struct {
		Currency    string          `json:"currency"` // 币种名称
		TransAmount decimal.Decimal `json:"transAmount"`
		Valuation   decimal.Decimal `json:"valuation"`
	}
)

func (slf *ReqAsset) ParamCheck() (code domain.Code) {
	if slf.UID == "" {
		return domain.ErrUserIdNull
	}
	if slf.ValType < domain.ValTypeCny || slf.ValType > domain.ValTypeBtc {
		return domain.ErrValType
	}

	return domain.CodeOk
}
