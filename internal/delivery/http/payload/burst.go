package payload

import (
	"futures-asset/internal/domain"
	"futures-asset/internal/libs/pager"

	"github.com/shopspring/decimal"
)

type (
	BurstListParam struct {
		pager.Condition
		Currency        string `json:"currency"`
		Base            string `json:"base"`
		Quote           string `json:"quote"`
		PosSide         int    `json:"posSide"`
		PosId           string `json:"posId"`
		MarginMode      int    `json:"marginMode"`
		LiquidationType []int  `json:"liquidationType"`
		UID             string `json:"uid"`
		UserType        []int  `json:"userType"`
		BurstId         string `json:"burstId"`
		BurstStartTime  int    `json:"startTime"`
		BurstEndTime    int    `json:"endTime"`
		Status          int    `json:"status"`
		IsExcel         int    `json:"isExcel"` // 是否导出
	}

	BurstListReply struct {
		pager.Page
		List []BurstRow `json:"list"`
	}

	BurstRow struct {
		Id                   string             `json:"id"`                   // id
		BurstId              string             `json:"burstId"`              // 爆仓id
		PosId                string             `json:"posId"`                // 持仓id
		UID                  string             `json:"uid"`                  // 用户id
		UserType             int                `json:"userType"`             // 用户类型
		ContractCode         string             `json:"contractCode"`         // 合约交易对
		AccountType          string             `json:"accountType"`          // 合约类型
		OrderType            int                `json:"orderType"`            // 委托方式
		MarginMode           int32              `json:"marginMode"`           // 保证金模式
		LiquidationType      int                `json:"liquidationType"`      // 平仓类型
		LiquidationDealValue decimal.Decimal    `json:"liquidationDealValue"` // 强平单成交总额
		LiquidationFee       decimal.Decimal    `json:"liquidationFee"`       // 爆仓强平费
		PosSide              int32              `json:"posSide"`              // 仓位类型
		Leverage             int                `json:"leverage"`             // 杠杆倍数
		MarkPrice            decimal.Decimal    `json:"markPrice"`            // 标记价格
		LastPrice            decimal.Decimal    `json:"lastPrice"`            // 最新成交价
		PosOverflow          int                `json:"posOverflow"`          // 是否穿仓
		ForceRival           int                `json:"forceRival"`           // 是否对手方减仓
		ForceRivalAmount     decimal.Decimal    `json:"forceRivalAmount"`     // 对手方减仓数量
		OverflowInfo         string             `json:"overflowInfo"`         // 穿仓补贴信息
		OverflowValue        decimal.Decimal    `json:"overflowValue"`        // 补贴数量折合
		OverflowTime         int64              `json:"overflowTime"`         // 穿仓补贴时间
		RiskLevel            int                `json:"RiskLevel"`            // 风险等级
		BurstTime            int64              `json:"burstTime"`            // 强平时间
		Status               domain.BurstStatus `json:"status"`               // 状态
	}

	BurstExcelRow struct {
		BurstTime            string `json:"burstTime"`            // 强平时间
		UID                  string `json:"uid"`                  // 用户id
		UserType             string `json:"userType"`             // 用户类型
		BurstId              string `json:"burstId"`              // 爆仓id
		PosId                string `json:"posId"`                // 持仓id
		AccountType          string `json:"accountType"`          // 合约类型
		ContractCode         string `json:"contractCode"`         // 合约交易对
		OrderType            string `json:"orderType"`            // 委托方式
		MarginMode           string `json:"marginMode"`           // 保证金模式
		PosSide              string `json:"posSide"`              // 仓位类型
		Leverage             string `json:"leverage"`             // 杠杆倍数
		LiquidationType      string `json:"liquidationType"`      // 强平类型
		Status               string `json:"status"`               // 强平状态
		LiquidationDealValue string `json:"liquidationDealValue"` // 强平单成交总额
		LiquidationFee       string `json:"liquidationFee"`       // 爆仓强平费
		ForceRivalAmount     string `json:"forceRivalAmount"`     // 对手方减仓数量
		OverflowTime         string `json:"overflowTime"`         // 穿仓补贴时间
		OverflowInfo         string `json:"overflowInfo"`         // 穿仓补贴信息
		OverflowValue        string `json:"overflowValue"`        // 补贴数量折合
	}

	BurstStatParam struct {
		StartTime int64 `json:"startTime"` // startTime
		EndTime   int64 `json:"endTime"`   // endTime
	}

	BurstInfoReply struct {
		UID                  string            `json:"uid"`                  // 用户id
		ContractTradeLevel   string            `json:"contractTradeLevel"`   // 合约交易等级
		ContractCode         string            `json:"contractCode"`         // 交易合约
		AccountType          string            `json:"accountType"`          // 账户类型
		PosSide              int32             `json:"posSide"`              // 仓位类型
		PosAmount            decimal.Decimal   `json:"posAmount"`            // 仓位数量
		PosUnreal            decimal.Decimal   `json:"posUnreal"`            // 仓位未实现盈亏
		OpenPrice            decimal.Decimal   `json:"openPrice"`            // 开仓价格
		MarkPrice            decimal.Decimal   `json:"markPrice"`            // 标记价格
		CollapsePrice        decimal.Decimal   `json:"collapsePrice"`        // 破产价格
		CollapsePriceFormula string            `json:"collapsePriceFormula"` // 破产价格公式
		LiquidationPrice     decimal.Decimal   `json:"liquidationPrice"`     // 预估强平价
		MarginBalance        decimal.Decimal   `json:"marginBalance"`        // 保证金
		HoldingMargin        decimal.Decimal   `json:"holdingMargin"`        // 维持保证金
		ProfitRate           decimal.Decimal   `json:"profitRate"`           // 回报率
		RiskRate             decimal.Decimal   `json:"riskRate"`             // 风险率
		MarginMode           domain.MarginMode `json:"marginMode"`           // 保证金模式
		Leverage             int               `json:"leverage"`             // 杠杆倍数
		RivalScoreRate       decimal.Decimal   `json:"rivalScoreRate"`       // 对手方评分率
		OpenTime             int64             `json:"openTime"`             // 开仓时间
		BurstTime            int64             `json:"burstTime"`            // 强平时间
		ForceRival           int               `json:"forceRival"`           // 是否对手方减仓
		RiskLevel            int               `json:"riskLevel"`            // 风险等级
		OverflowInfo         string            `json:"overflowInfo"`         // 穿仓补贴信息
	}

	IndexPriceInfoParam struct {
		pager.Condition
		Base     string `json:"base"`
		Quote    string `json:"quote"`
		Platform string `json:"platform"`
		Status   int    `json:"status"`
	}

	IndexPriceListParam struct {
		Base  string `json:"base"`
		Quote string `json:"quote"`
	}

	IndexPriceInfoReply struct {
		Total int64           `json:"total"`
		List  []IndexPriceRow `json:"list"`
	}

	IndexPriceRow struct {
		ContractCode string `json:"contractCode"` // 交易合约
		Platform     string `json:"platform"`     // 取值平台
		IndexPrice   string `json:"indexPrice"`   // 指数价格
		MaBasis      string `json:"maBasis"`      // 基差移动平均值
		MarkPrice    string `json:"markPrice"`    // 标记价格
		Status       int    `json:"status"`       // 指数状态
		Exception    string `json:"exception"`    // 异常
	}
)
