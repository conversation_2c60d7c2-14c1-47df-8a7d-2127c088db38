package payload

import (
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"

	"github.com/shopspring/decimal"
)

type IncrParam struct {
	UID         string          `json:"uid" binding:"required"`      // 用户ID
	OrderId     string          `json:"order_id" binding:"required"` // 订单ID
	FromPair    string          `json:"from_pair"`                   // 转出币对
	ToPair      string          `json:"to_pair"`                     // 转入币对
	Currency    string          `json:"currency"`                    // 币种
	FromAccount int             `json:"from_account"`                // 转出账户 0:现货账户 1:杠杆账户 2:USDT永续合约账户 3:法币账户
	ToAccount   int             `json:"to_account"`                  // 转入账户 0:现货账户 1:杠杆账户 2:USDT永续合约账户 3:法币账户
	Amount      decimal.Decimal `json:"amount"`                      // 转账数量
}

type (
	FeeDetail struct {
		Currency string          `json:"currency"`
		Amount   decimal.Decimal `json:"amount"`
		Price    decimal.Decimal `json:"price"`
	}
	Reply struct {
		UID                   string          `json:"uid"`                  // 用户id
		PosId                 string          `json:"pos_id"`               // 仓位(持仓)唯一ID
		ProfitReal            decimal.Decimal `json:"profit_real"`          // 平仓已实现盈亏
		Pos                   decimal.Decimal `json:"pos"`                  // pos
		OpenPriceAvg          decimal.Decimal `json:"open_price_avg"`       // 开仓均价
		PosSide               int32           `json:"pos_side"`             // 仓位方向
		FeeDetail             []FeeDetail     `json:"fee_detail"`           // 手续费明细
		HaveTrial             int             `json:"have_trial"`           // 使用体验金
		IsReverse             bool            `json:"is_reverse"`           // 成交是否有先平仓后开仓的类反手操作
		ReverseClosePosAmount decimal.Decimal `json:"reverse_close_amount"` // 单向持仓时平仓数量
	}
	BalanceUpdate struct {
		ContractCode     string          `json:"contract_code"`      // 合约代码
		Currency         string          `json:"currency"`           // 资产币种
		OrderId          string          `json:"order_id"`           // 订单ID
		UID              string          `json:"uid"`                // 用户id
		UserType         int32           `json:"user_type"`          // 用户类型
		ChannelCode      string          `json:"channel_code"`       // 用户渠道码
		AgentUserId      string          `json:"agent_user_id"`      // 代理人id
		AgentStatus      int             `json:"agent_status"`       // 代理人状态-1:启用,2:禁用
		AgentChannelCode string          `json:"agent_channel_code"` // 代理人渠道码
		RegisterLanguage string          `json:"register_language"`  // 用户注册语言
		Platform         string          `json:"platform"`           // 来源 (WEB,APP,H5)
		StrategyType     int32           `json:"strategy_type"`      // 策略类型 (0:合约交易)
		DealAmount       decimal.Decimal `json:"deal_amount"`        // 成交仓位数量
		DealPrice        decimal.Decimal `json:"deal_price"`         // 成交价格
		Amount           decimal.Decimal `json:"amount"`             // 财务记账数量(按照domain.ContractTradeOpenFee等交易类型区分)
		OperateType      int             `json:"operate_type"`       // 操作类型
		OperateTime      int64           `json:"operate_time"`       // 操作时间
		AwardOpIds       []string        `json:"award_op_ids"`       // 体验金资产ID
		// 只有期权行权时,wallet返佣逻辑中会使用
		OptionId string          `json:"option_id"` // option-期权ID
		Premium  decimal.Decimal `json:"premium"`   // option-权利金总额 (只有期权行权时,wallet返佣逻辑中会使用(通知枚举值为:OptionRealFee-20))
		Period   string          `json:"period"`    // 期权时间粒度 (3分钟, 5分钟) (只有期权行权时,wallet返佣逻辑中使用)
	}
	BalanceRes struct {
		AssetLogs      []*repository.MqCmsAsset   // 财务日志记录
		BillAssetLogs  []repository.BillAssetSync // 账单日志记录
		TrialAssetLogs []*entity.TrialAsset       // 账单日志记录
	}
)

func (p Trade) IsClose() bool {
	return (p.Side == domain.Buy && p.PosSide == domain.Short) ||
		(p.Side == domain.Sell && p.PosSide == domain.Long)
}

func (p Trade) IsOpen() bool {
	return (p.Side == domain.Buy && p.PosSide == domain.Long) ||
		(p.Side == domain.Sell && p.PosSide == domain.Short)
}

type (
	InnerTransfer struct {
		FromUserId string          `json:"from_user_id"`
		ToUserId   string          `json:"to_user_id"`
		Currency   string          `json:"currency"`
		Amount     decimal.Decimal `json:"amount"`
	}
)

type AssetReply struct {
	// Before  decimal.Decimal `json:"before"`
	// After   decimal.Decimal `json:"after"`
	OrderId string `json:"order_id"`
}

type TranCny struct {
	Msg      string          `json:"msg"`
	Code     int             `json:"code"`
	UsdToCny decimal.Decimal `json:"data"`
}
