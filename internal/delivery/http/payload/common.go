package payload

import "futures-asset/internal/domain/usecase"

type Page struct {
	PageNum  int `json:"pageNum"`
	PageSize int `json:"pageSize"`
}

type UserParam struct {
	UID string `json:"uid"`
}

type CurrencyParam struct {
	UID      string `json:"uid"`
	Currency string `json:"currency"`
}

func (p UserParam) ToUseCaseParam() *usecase.UIDParam {
	return &usecase.UIDParam{UID: p.UID}
}

func (p CurrencyParam) ToUseCaseParam() *usecase.CurrencyParam {
	return &usecase.CurrencyParam{UID: p.UID, Currency: p.Currency}
}
