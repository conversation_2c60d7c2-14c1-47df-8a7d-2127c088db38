package payload

import (
	"futures-asset/internal/domain/entity"
	"futures-asset/internal/domain/repository"

	"github.com/shopspring/decimal"
)

type (
	// OpenReply 开仓函数返回值
	// 使用详情可查看 cache/swapcache/pos.go 文件中 OpenLongPos 或 OpenShortPos 方法
	OpenReply struct {
		TrialLogs     []*entity.TrialAsset       // 体验金日志记录
		BillAssetLogs []repository.BillAssetSync // 账单日志记录
	}

	// CloseReply 平仓函数返回值
	// 使用详情可查看 cache/swapcache/pos.go 文件中 CloseLongPos 或 CloseShortPos 方法
	CloseReply struct {
		ProfitReal    decimal.Decimal            // 平仓已实现盈亏
		AssetLogs     []*repository.MqCmsAsset   // 财务日志记录
		TrialLogs     []*entity.TrialAsset       // 体验金日志记录
		BillAssetLogs []repository.BillAssetSync // 账单日志记录
	}

	PosReply struct {
		Reply
		AssetLogs     []*repository.MqCmsAsset   // 财务日志记录
		BillAssetLogs []repository.BillAssetSync // 账单日志记录
		ClearPos      repository.PosSwap         // 清空的仓位
		LogPos        *repository.LogPosSync     // 仓位变化
		TrialLogs     []*entity.TrialAsset       // 体验金日志记录
	}
)
