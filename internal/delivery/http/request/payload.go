package request

import (
	"errors"
	"strings"

	"futures-asset/internal/domain/usecase"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

func ShouldBindJSON(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBind<PERSON>(obj); err != nil {
		var messages string
		var errs validator.ValidationErrors
		if errors.As(err, &errs) {
			for _, err2 := range errs {
				messages += err2.Translate(translator) + ", "
			}

			// 過濾最後字尾 ", "
			messages = messages[:len(messages)-2]
		} else {
			messages = err.Error()
		}

		return errors.New(messages)
	}

	return nil
}

func ShouldBindForm(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBind(obj); err != nil {
		var messages string
		var errs validator.ValidationErrors
		if errors.As(err, &errs) {
			for _, err2 := range errs {
				messages += err2.Translate(translator) + ", "
			}

			// 過濾最後字尾 ", "
			messages = messages[:len(messages)-2]
		} else {
			messages = err.Error()
		}

		return errors.New(messages)
	}

	return nil
}

func ShouldBindQuery(c *gin.Context, obj interface{}) error {
	if err := c.ShouldBindQuery(obj); err != nil {
		var messages string
		var errs validator.ValidationErrors
		if errors.As(err, &errs) {
			for _, err2 := range errs {
				messages += err2.Translate(translator) + ", "
			}

			// 過濾最後字尾 ", "
			messages = messages[:len(messages)-2]
		} else {
			messages = err.Error()
		}

		return usecase.ParamInvalidError{Msg: messages}
	}

	return nil
}

func BearerToken(c *gin.Context) (string, error) {
	auth := c.Request.Header.Get("Authorization")
	prefix := "Bearer "

	if !(auth != "" && strings.HasPrefix(auth, prefix)) {
		return "", errors.New("authentication bearer fail")
	}

	return auth[len(prefix):], nil
}
