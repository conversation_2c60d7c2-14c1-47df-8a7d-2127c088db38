package http

import (
	"futures-asset/internal/delivery/http/response"
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/dig"
)

type CronParam struct {
	dig.In
}

type cronHandler struct {
}

func newCronHandler(param CronParam) *cronHandler {
	return &cronHandler{}
}

func (handler *cronHandler) transactionHour(c *gin.Context) {
	// var req payload.TransactionHourReport
	// if err := request.ShouldBindJSON(c, &req); err != nil {
	// 	c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))

	// 	return
	// }

	// if req.Time.IsZero() {
	// 	req.Time = time.Now()
	// }

	// logrus.Infof("start transactionCronUseCase.Hour")

	// err := handler.transactionCronUseCase.Hour(c.Request.Context(), req.Time)
	// if err != nil {
	// 	logrus.WithFields(logrus.Fields{
	// 		"err": err,
	// 	}).Error("transactionCronUseCase.Hour")

	// 	c.J<PERSON>(response.ServiceErrorToErrorResp(err))

	// 	return
	// }

	// logrus.Infof("end transactionCronUseCase.Hour")
	c.JSON(http.StatusOK, response.NewSuccess(gin.H{}))
}
