package http

import (
	"errors"
	"fmt"
	"net/http"
	"strings"

	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/delivery/http/request"
	"futures-asset/internal/delivery/http/response"
	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"

	"github.com/gin-gonic/gin"
	"go.uber.org/dig"
)

type PositionHandlerParam struct {
	dig.In

	PositionUseCase usecase.PositionUseCase
	BurstRepo       repository.BurstRepository
	SettingRepo     repository.SettingRepository
}

type positionHandler struct {
	positionUseCase usecase.PositionUseCase
	burstRepo       repository.BurstRepository
	settingRepo     repository.SettingRepository
}

func newPositionHandler(param PositionHandlerParam) *positionHandler {
	return &positionHandler{
		positionUseCase: param.PositionUseCase,
		burstRepo:       param.BurstRepo,
		settingRepo:     param.SettingRepo,
	}
}

// UserPos 获取用户持仓
func (handler *positionHandler) UserPos(c *gin.Context) {
	var req payload.SwapParam
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	if len(req.UID) <= 0 {
		msg := fmt.Sprintf("param err: %+v", req)
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(msg)))

		return
	}

	pos, err := handler.positionUseCase.UserPos(c.Request.Context(), req.ToUseCase())
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(pos))
}

func (handler *positionHandler) UserIsBurst(c *gin.Context) {
	var req payload.UserIsBurstReq
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}

	if len(req.UID) <= 0 {
		msg := fmt.Sprintf("param err: %+v", req)
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(msg)))
		return
	}

	checkBurst := repository.CheckBurstParam{
		ContractCode: strings.ToUpper(req.ContractCode),
		UID:          req.UID,
		MarginMode:   req.MarginMode,
		IsTrialPos:   req.IsTrialPos,
	}
	isBursting, err := handler.burstRepo.IsBursting(c.Request.Context(), checkBurst)
	if err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))
		return
	}
	if isBursting {
		c.JSON(http.StatusBadRequest, response.NewError(domain.ErrUserBurst, errors.New(domain.ErrMsg[domain.ErrUserBurst])))
		return
	}

	c.JSON(http.StatusOK, response.NewSuccess(false))
}

// SendRebornCard 发放重生卡
// func (handler *positionHandler) SendRebornCard(c *gin.Context) {
// 	var req payload.SendRebornCardParam
// 	if err := request.ShouldBindQuery(c, &req); err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
// 		return
// 	}

// 	if len(req.UID) <= 0 {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("param no user id err: %+v", req))))
// 		return
// 	}

// 	if req.ExpireTime <= 0 {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("param no expire time err: %+v", req))))
// 		return
// 	}

// 	if req.Period <= 0 {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, errors.New(fmt.Sprintf("param no period time err: %+v", req))))
// 		return
// 	}

// 	card := cache.RebornCard{
// 		Id:               util.GenerateId(),
// 		ContractCodeList: make([]string, 0),
// 		UID:              req.UID,
// 		ExpireTime:       req.ExpireTime,
// 		TriggerPosIds:    make([]string, 0),
// 		Period:           req.Period,
// 	}
// 	err := cache.SaveUserRebornCard(req.UID, card)
// 	if err != nil {
// 		c.JSON(http.StatusBadRequest, response.NewError(domain.Code252407, errors.New(fmt.Sprintf("save reborn card err: %+v", req))))
// 		return
// 	}
// 	c.JSON(http.StatusOK, response.NewSuccess(map[string]interface{}{"id": card.Id}))
// }

// TestSettlement 测试时使用，
func (handler *positionHandler) TestSettlement(c *gin.Context) {
	var req payload.TestSettlement
	if err := request.ShouldBindQuery(c, &req); err != nil {
		c.JSON(http.StatusBadRequest, response.NewError(domain.CodeParamInvalid, err))
		return
	}
	req.ContractCode = strings.ToUpper(req.ContractCode)

	// usersKey := domain.AssetPrefix.Key(domain.RedisAllUser)
	// redis := redislib.Redis()
	// users, err := redis.HGetAll(usersKey)
	// if err != nil {
	//	logrus.Error("burstService HGetAll", usersKey, "error:", err)
	//	return
	// }
	// logrus.Info(fmt.Sprintf("Settlement All._contractConfig:%+v", param.ContractCode))
	// logrus.Info(fmt.Sprintf("Settlement pCache:%+v", pCache))
	// logrus.Info(fmt.Sprintf("Settlement users:%+v", users))
	// contractConfig, err := handler.settingRepo.GetPairSettingInfo(c.Request.Context(), req.ContractCode)
	// if err != nil {
	// 	logrus.Error("TestSettlement", err)
	// }
	// fundRate := swapcache.FundRate(req.ContractCode, *contractConfig)

	// go cron.SettlementAll(fundRate, req.ContractCode, *contractConfig, nil)

	c.JSON(http.StatusOK, response.NewSuccess(""))
}
