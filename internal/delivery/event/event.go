package event

import (
	"context"
	"fmt"

	"go.uber.org/dig"

	"futures-asset/configs"
	client "futures-asset/internal/libs/kafka"

	cfg "yt.com/backend/common.git/config"
	"yt.com/backend/common.git/kafka"
)

type Server struct {
	container *dig.Container
	conf      *cfg.Config[configs.Config]
	cluster   *client.Cluster

	subscribers map[string]*kafka.ConsumerGroup
}

func NewServer(conf *cfg.Config[configs.Config], container *dig.Container, messageCluster *client.Cluster) *Server {
	return &Server{
		conf:        conf,
		container:   container,
		cluster:     messageCluster,
		subscribers: make(map[string]*kafka.ConsumerGroup),
	}
}

func (s *Server) Start(ctx context.Context) error {
	if err := s.registerSubscriberHandler(ctx); err != nil {
		return fmt.Errorf("registerSubscriberHandler failed: %w", err)
	}

	return nil
}

func (s *Server) Shutdown(_ context.Context) error {
	for _, subscriber := range s.subscribers {
		if err := subscriber.Close(); err != nil {
			return err
		}
	}

	return nil
}
