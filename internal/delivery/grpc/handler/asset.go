package handler

import (
	"context"

	"futures-asset/internal/delivery/grpc/response"
	"futures-asset/internal/delivery/http/payload"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/domain/usecase"

	"github.com/shopspring/decimal"
	"go.uber.org/dig"

	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
	"yt.com/backend/common.git/transport/grpcinit"
)

type NewAssetHandlerParam struct {
	dig.In

	AssetUseCase usecase.AssetUseCase
	BurstRepo    repository.BurstRepository
}

type AssetHandler struct {
	assetUseCase usecase.AssetUseCase
	burstRepo    repository.BurstRepository

	futuresassetpb.FuturesAssetServiceServer
}

func NewAssetHandler(param NewAssetHandlerParam) *AssetHandler {
	return &AssetHandler{
		assetUseCase: param.AssetUseCase,
		burstRepo:    param.BurstRepo,
	}
}

func (h *As<PERSON>Handler) LockAsset(ctx context.Context, req *futuresassetpb.LockAssetRequest) (*futuresassetpb.LockAssetResponse, error) {
	amount, err := decimal.NewFromString(req.GetAmount())
	if err != nil {
		return nil, grpcinit.Error(response.ServiceErrorToGrpcError(err))
	}

	param := &payload.LockParam{
		UID:        req.Uid,
		UserType:   req.UserType,
		Currency:   req.Currency,
		Symbol:     req.Symbol,
		Amount:     amount,
		Leverage:   int(req.Leverage),
		OrderID:    req.OrderId,
		OrderTime:  req.OrderTime,
		MarginMode: req.MarginMode,
		AwardOpIds: req.AwardOpIds,
		TrialIsEnd: req.TrialIsEnd,
	}
	_, err = h.assetUseCase.LockAsset(ctx, param.ToUseCaseParam())
	if err != nil {
		return nil, grpcinit.Error(response.ServiceErrorToGrpcError(err))
	}

	return &futuresassetpb.LockAssetResponse{
		// Amount:         reply.Amount.String(),
		// FrozenMargin:   reply.FrozenMargin.String(),
		// HaveTrial:      int32(reply.HaveTrial),
		// PositionMode:   reply.PositionMode,
		// JoinMarginRate: reply.JoinMarginRate.String(),
		// OrderId: reply.OrderId,
	}, nil
}

func (h *AssetHandler) UnLockAsset(ctx context.Context, req *futuresassetpb.UnLockAssetRequest) (*futuresassetpb.UnLockAssetResponse, error) {
	param := &payload.BatchUnlockParam{
		UID: req.Uid,
		// UserType:   req.UserType,
		Symbol:       req.Symbol,
		Currency:     req.Currency,
		MarginMode:   req.MarginMode,
		PositionMode: req.PositionMode,
		Batchs:       []*payload.UnLockParam{},
	}
	_, err := h.assetUseCase.UnLockAsset(ctx, param.ToUseCaseParam())
	if err != nil {
		return nil, grpcinit.Error(response.ServiceErrorToGrpcError(err))
	}

	return &futuresassetpb.UnLockAssetResponse{
		// SuccessList: reply.SuccessList,
		// FailedList:  reply.FailedList,
	}, nil
}

func (h *AssetHandler) GetUserAsset(ctx context.Context, req *futuresassetpb.LockAssetRequest) (*futuresassetpb.LockAssetResponse, error) {
	return nil, nil
}
