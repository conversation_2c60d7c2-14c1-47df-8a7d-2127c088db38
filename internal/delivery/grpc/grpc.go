package grpc

import (
	"context"
	"fmt"

	"futures-asset/configs"
	"futures-asset/internal/delivery/grpc/handler"

	"github.com/sirupsen/logrus"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"go.uber.org/dig"
	"google.golang.org/grpc"

	futuresassetpb "yt.com/backend/common.git/business/grpc/gen/futures/asset/v1"
	cfg "yt.com/backend/common.git/config"
	"yt.com/backend/common.git/transport/grpcinit"
)

type Server struct {
	conf         *cfg.Config[configs.Config]
	container    *dig.Container
	grpcShutdown func(ctx context.Context) error
}

func NewServer(conf *cfg.Config[configs.Config], container *dig.Container) *Server {
	return &Server{
		conf:      conf,
		container: container,
	}
}

func (s *Server) Start(_ context.Context) error {
	// 註冊 pb server handler
	registerFunc, err := s.registerHandler()
	if err != nil {
		return fmt.Errorf("register handler error: %w", err)
	}

	opt := []grpc.ServerOption{
		grpc.StatsHandler(otelgrpc.NewServerHandler()),
		grpc.ChainUnaryInterceptor(
			grpcinit.UnaryServerRecover(logrus.StandardLogger()),
			grpcinit.UnaryServerTimeout(logrus.StandardLogger(), grpcinit.DefaultTimeout),
		),
	}

	addr := fmt.Sprintf(":%d", s.conf.CustomConfig.Grpc.Port)

	shutdown, err := grpcinit.NewServerTCP(addr, registerFunc, opt...)
	if err != nil {
		return fmt.Errorf("grpcinit.NewServerTCP error: %w", err)
	}

	s.grpcShutdown = shutdown

	return nil
}

func (s *Server) Shutdown(ctx context.Context) error {
	if err := s.grpcShutdown(ctx); err != nil {
		return fmt.Errorf("grpc server shutdown with err: %w", err)
	}

	return nil
}

func (s *Server) registerHandler() (func(server grpc.ServiceRegistrar), error) {
	var assetHandler *handler.AssetHandler

	err := s.container.Invoke(func(param handler.NewAssetHandlerParam) {
		assetHandler = handler.NewAssetHandler(param)
	})
	if err != nil {
		return nil, fmt.Errorf("NewAssetHandler fail :%w", err)
	}

	return func(server grpc.ServiceRegistrar) {
		futuresassetpb.RegisterFuturesAssetServiceServer(server, assetHandler)
	}, nil
}
