package response

import (
	"errors"

	"google.golang.org/grpc/codes"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/usecase"

	"yt.com/backend/common.git/transport/grpcinit"
)

func ServiceErrorToGrpcError(err error) (codes.Code, grpcinit.APIServiceError) {
	var serviceError usecase.ServiceError
	if !errors.As(err, &serviceError) {
		serviceError = usecase.InternalError{
			Err: err,
		}
	}

	return serviceError.GRPCStatusCode(), GrpcResponseError{
		Err:        serviceError.Error(),
		ErrCode:    serviceError.ErrorCode(),
		HTTPStatus: serviceError.HTTPStatusCode(),
	}
}

type GrpcResponseError struct {
	Err        string
	ErrCode    domain.Code
	HTTPStatus int
}

func (e GrpcResponseError) Error() string {
	return e.Err
}

func (e GrpcResponseError) ErrorCode() grpcinit.Code {
	return grpcinit.Code(e.ErrCode)
}

func (e GrpcResponseError) HTTPStatusCode() int {
	return e.HTTPStatus
}
