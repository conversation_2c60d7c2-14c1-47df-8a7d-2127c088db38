package cron

import (
	"futures-asset/configs"

	"github.com/robfig/cron/v3"
	"github.com/sirupsen/logrus"
	"yt.com/backend/common.git/config"
)

// NOTE: 盡量不要使用這個, 要寫 Cron 改用 K8s CronJob
func Init(conf *config.Config[configs.Config]) error {
	cronLog := cron.VerbosePrintfLogger(logrus.StandardLogger())

	// corn 全域的設定，每個加入的 job 都會有的設定，cron.Recover 處理 panic 場景
	c := cron.New(cron.WithSeconds(),
		cron.WithLogger(cronLog),
		cron.WithChain(cron.Recover(cronLog)),
	)

	if err := registerCronJobs(c, cronLog, conf); err != nil {
		return err
	}

	c.Start()

	return nil
}

func registerCronJobs(c *cron.Cron, cronLog cron.Logger, conf *config.Config[configs.Config]) error {
	// Crypto to USDT
	// if err := container.Get().Invoke(func(uc usecase.CryptoToBaseUseCase) {
	// 	var err error

	// 	_, err = c.AddJob(conf.CustomConfig.CronJobCrypto,
	// 		cron.NewChain(cron.SkipIfStillRunning(cronLog)).
	// 			Then(uc.RunCrypto()),
	// 	)
	// 	if err != nil {
	// 		log.Error(err)

	// 		return
	// 	}
	// }); err != nil {
	// 	return fmt.Errorf("Invoke(registerCryptoToBaseUseCase), err: %w", err)
	// }

	// Average Price 每整點小時記錄一次
	// if err := container.Get().Invoke(func(uc usecase.AveragePriceUseCase) {
	// 	var err error

	// 	_, err = c.AddJob(conf.CustomConfig.RecordAverageFrequency,
	// 		cron.NewChain(cron.SkipIfStillRunning(cronLog)).
	// 			Then(uc.RunAverageHistoryPrice()),
	// 	)
	// 	if err != nil {
	// 		log.Error(err)

	// 		return
	// 	}

	// 	_, err = c.AddJob(conf.CustomConfig.CalculateAverageFrequency,
	// 		cron.NewChain(cron.SkipIfStillRunning(cronLog)).
	// 			Then(uc.CalculateAveragePrice()),
	// 	)
	// 	if err != nil {
	// 		log.Error(err)

	// 		return
	// 	}
	// }); err != nil {
	// 	return fmt.Errorf("Invoke(registerAveragePriceUseCase), err: %w", err)
	// }

	return nil
}
