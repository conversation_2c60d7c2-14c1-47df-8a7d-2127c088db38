package cron

// import (
// 	"encoding/json"
// 	"fmt"
// 	"strconv"
// 	"time"

// 	"futures-asset/cache/cachekey"
// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/entity"
// 	"futures-asset/pkg/redislib"
// 	"futures-asset/pkg/setting"
// 	"futures-asset/pkg/sqllib"
// 	"futures-asset/util"

// 	"github.com/robfig/cron"
// 	"github.com/shopspring/decimal"
// 	"github.com/sirupsen/logrus"
// )

// // StartProfitStaticData 利润管理
// func StartProfitStaticData() {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(fmt.Sprintf("cron StartProfitStaticData failed,err:%v", err))
// 			go StartProfitStaticData()
// 		}
// 	}()
// 	c := cron.New()
// 	// 每分钟起一次定时任务
// 	_ = c.AddFunc("0 30 23 * * ?", profitStatic)
// 	_ = c.AddFunc("0 30 0 * * ?", syncProfitToSql)
// 	_ = c.AddFunc("0 0 * * * ?", syncTotalProfitToSql)
// 	c.Start()
// }

// // 把redis 数据同步到 sql 中
// func syncProfitToSql() {
// 	profitMutex := redislib.NewMutex(domain.MutesSwapCronSyncProfit, 30*time.Second)
// 	if profitMutex.LockOnce() != nil {
// 		logrus.Error(fmt.Sprintf("syncProfitToSql NewMutex err."))
// 		return
// 	}
// 	defer profitMutex.Unlock()
// 	redis := redislib.Redis()

// 	profitDates, err := redis.SMembers(domain.ProfitLossDate)
// 	if err != nil {
// 		logrus.Error(fmt.Sprintf("get Get Profit Dates err"))
// 		return
// 	}
// 	profitTime := util.OneDayBeginAndEndTimeStamp(time.Now())
// 	if len(profitDates) > 0 {
// 		for _, dateStr := range profitDates {
// 			dataTime, _ := strconv.Atoi(dateStr)
// 			if int64(dataTime) >= profitTime {
// 				continue
// 			}
// 			err := onSyncProfit(dateStr)
// 			if err == nil {
// 				_, err = redis.SRem(domain.ProfitLossDate, dateStr)
// 				if err != nil {
// 					logrus.Error(fmt.Sprintf("ProfitLossDate res dateStr:%s, err:%+v", dateStr, err))
// 				}
// 			}
// 		}
// 	}
// }

// func onSyncProfit(timeStr string) error {
// 	redis := redislib.Redis()
// 	allProfitData, err := redis.HGetAll(domain.ProfitLossData + timeStr)
// 	if err != nil {
// 		return err
// 	}
// 	db, _ := sqllib.Db()
// 	for key, profitData := range allProfitData {
// 		dbProfitData := entity.ProfitLoss{}
// 		err := json.Unmarshal([]byte(profitData), &dbProfitData)
// 		if err != nil {
// 			logrus.Error(fmt.Sprintf("onSyncProfit Unmarshal err.profitData:%s", profitData))
// 		}
// 		err = dbProfitData.UpsertProfitLoss(db)
// 		if err != nil {
// 			logrus.Error(fmt.Sprintf("profit Data Upsert err:%+v,timeStr:%s,dbProfitData:%+v", err, timeStr, dbProfitData))
// 			return err
// 		}
// 		_ = redis.HDel(domain.ProfitLossData+timeStr, key)
// 	}
// 	return nil
// }

// // 生成新一天的数据
// func profitStatic() {
// 	profitMutex := redislib.NewMutex(domain.MutexSwapProfitStatic, 30*time.Second)
// 	if profitMutex.LockOnce() != nil {
// 		logrus.Error(fmt.Sprintf("profitStatic NewMutex err."))
// 		return
// 	}
// 	defer profitMutex.Unlock()
// 	// 获取下一天的起始时间
// 	profitTime := util.OneDayBeginAndEndTimeStamp(time.Now().AddDate(0, 0, 1))
// 	profitTimeStr := strconv.FormatInt(profitTime, 10)
// 	redis := redislib.Redis()
// 	_, err := redis.SAdd(domain.ProfitLossDate, profitTimeStr)
// 	if err != nil {
// 		logrus.Error(fmt.Sprintf("SAdd ProfitDate:%+v, err:%+v", profitTimeStr, err))
// 	}

// 	profitHashKey := domain.ProfitLossData + profitTimeStr

// 	// 获取所有用户
// 	usersKey := domain.AssetPrefix.Key(domain.RedisAllUser)
// 	users, err := redis.HGetAll(usersKey)
// 	if err != nil {
// 		logrus.Error("profitStatic HGetAll", usersKey, "error:", err)
// 		return
// 	}

// 	for _, currency := range domain.CurrencyList {
// 		key := util.ProfitLossBassKey(currency)
// 		profitLossData := entity.ProfitLoss{
// 			Currency:    currency,
// 			NetIn:       decimal.Zero,
// 			ProfitLoss:  decimal.Zero,
// 			OperateTime: profitTime,
// 		}
// 		profitLossData.CreateTime = time.Now().UnixNano()

// 		for uid := range users {
// 			profitLossData.UID = uid
// 			hashKey := uid + key
// 			profitData, _ := json.Marshal(&profitLossData)
// 			err = redis.HSet(profitHashKey, hashKey, string(profitData))
// 			if err != nil {
// 				logrus.Error(fmt.Sprintf("create profit redis err.hash:%s, key:%s, data:%+v", profitHashKey, hashKey, profitLossData))
// 			}
// 		}
// 	}
// }

// func syncTotalProfitToSql() {
// 	profitMutex := redislib.NewMutex(domain.MutesSwapCronSyncTotalProfit, 30*time.Second)
// 	if profitMutex.LockOnce() != nil {
// 		logrus.Error(fmt.Sprintf("syncTotalProfitToSql NewMutex err."))
// 		return
// 	}
// 	defer profitMutex.Unlock()
// 	redis := redislib.Redis()
// 	totalProfit, err := redis.HGetAll(domain.TotalProfitHash)
// 	if err != nil {
// 		logrus.Error(fmt.Sprintf("get total profit err:%+v", err))
// 		return
// 	}
// 	db, err := sqllib.Db()
// 	if err != nil {
// 		logrus.Error(fmt.Sprintf("get db err:%+v", err))
// 		return
// 	}
// 	for _, profitData := range totalProfit {
// 		dbTotalProfit := entity.TotalProfit{}
// 		err = json.Unmarshal([]byte(profitData), &dbTotalProfit)
// 		if err != nil {
// 			logrus.Error(fmt.Sprintf("unmarshal total profit data err:%+v,profitData:%s", err, profitData))
// 			continue
// 		}
// 		err = dbTotalProfit.UpsertTotalProfit(db)
// 		if err != nil {
// 			logrus.Error(fmt.Sprintf("UpsertTotalProfit err:%+v,profitData:%s", err, profitData))
// 		}
// 	}
// }
