package cron

// import (
// 	"fmt"
// 	"strings"

// 	"futures-asset/cache/swapcache"
// 	"futures-asset/internal/domain"
// 	"futures-asset/pkg/setting"

// 	"github.com/robfig/cron"
// 	"github.com/shopspring/decimal"
// 	"github.com/sirupsen/logrus"
// )

// // StartUpdateAccountPrincipal 更新用户本金
// func StartUpdateAccountPrincipal() {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(fmt.Sprintf("cron plat pos push failed,err:%v", err))
// 			go StartUpdateAccountPrincipal()
// 		}
// 	}()
// 	c := cron.New()
// 	_ = c.AddFunc("0 0 8 * * ?", updateAccountPrincipal) // 每天8点更新本金
// 	c.Start()
// }

// func updateAccountPrincipal() {
// 	logrus.Infof("start initialize user principal")
// 	data := handlerSwapAsset()
// 	// 当天本金重置更新
// 	for userID, value := range data {
// 		profit, err := setting.GetProfitFromRedis(userID)
// 		if err != nil || profit == nil {
// 			logrus.Warnf("user[%v] profit is nil, init it", userID)
// 			profit = new(setting.Profit)
// 		}
// 		profit.InitialFund = value
// 		profit.CurrentFund = value
// 		profit.ProfitRate = 0

// 		err = profit.Flush(userID)
// 		if err != nil {
// 			logrus.Infof("init user[%v] profit err: %v", userID, err)
// 		}
// 	}
// }

// func handlerSwapAsset() map[string]decimal.Decimal {
// 	var (
// 		userID string
// 		res    = make(map[string]decimal.Decimal)
// 	)

// 	posCache := new(swapcache.PosCache)
// 	keys, _ := posCache.GetAllHashKey()

// 	for _, key := range keys {
// 		str := strings.Split(key, ":")
// 		if len(str) <= 1 {
// 			logrus.Warnf("swap key[%v] is invalid", key)
// 			continue
// 		}
// 		userID = str[1]

// 		userCache := swapcache.NewPosCache(swapcache.CacheParam{}, userID)
// 		asset, err := userCache.Load()
// 		if err != nil {
// 			logrus.Errorf("query asset err: %s", err.Error())
// 			continue
// 		}
// 		totalBalance := asset.CBalance(domain.CurrencyUSDT)
// 		res[userID] = totalBalance
// 	}
// 	return res
// }
