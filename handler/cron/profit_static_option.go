package cron

// import (
// 	"encoding/json"
// 	"fmt"
// 	"strconv"
// 	"time"

// 	"futures-asset/cache/optioncache"
// 	"futures-asset/conf"
// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/entity"
// 	"futures-asset/pkg/redislib"
// 	"futures-asset/pkg/setting"
// 	"futures-asset/pkg/sqllib"
// 	"futures-asset/util"

// 	"gorm.io/gorm"
// 	"github.com/robfig/cron"
// 	"github.com/sirupsen/logrus"
// )

// // StartSyncOptionProfitData 利润管理
// func StartSyncOptionProfitData() {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(fmt.Sprintf("cron StartSyncOptionProfitData failed,err:%v", err))
// 			go StartSyncOptionProfitData()
// 		}
// 	}()
// 	var (
// 		dayCron   = "0 30 23 * * ?" // 每天 23:30:00 执行
// 		userCorn  = "0 30 0 * * ?"  // 每天 00:30:00 执行
// 		totalCorn = "0 0 * * * ?"   // 每小时执行一次
// 	)
// 	// 如果是测试环境修改cron
// 	if conf.Conf.Biz.Debug {
// 		userCorn = "0 0/30 * * * ?"  // 每半小时执行一次
// 		totalCorn = "0 0/30 * * * ?" // 每半小时执行一次
// 	}

// 	c := cron.New()
// 	_ = c.AddFunc(dayCron, generateNewDayData)          // 生成新一天的数据
// 	_ = c.AddFunc(userCorn, syncOptionProfitToDb)       // 将 Redis 中缓存的用户每日盈亏数据持久化到 DB
// 	_ = c.AddFunc(totalCorn, syncOptionTotalProfitToDb) // 将 Redis 中缓存的数据持久化到 DB
// 	c.Start()
// }

// // 把redis 数据同步到 sql 中
// func syncOptionProfitToDb() {
// 	var (
// 		day         int64
// 		db, _       = sqllib.Db()
// 		setKey      = setting.GetOptionProfitDateKey()
// 		client      = redislib.Redis()
// 		dayBegin    = util.OneDayBeginAndEndTimeStamp(time.Now())
// 		profitMutex = redislib.NewMutex(domain.MutesOptionCronSyncProfit, 30*time.Second)
// 	)
// 	if profitMutex.LockOnce() != nil {
// 		logrus.Errorf("syncOptionProfitToDb NewMutex err.")
// 		return
// 	}
// 	defer profitMutex.Unlock()

// 	// 获取时间列表
// 	days, err := client.SMembers(setKey)
// 	if err != nil {
// 		logrus.Errorf("get option profit date err")
// 		return
// 	}
// 	if len(days) > 0 {
// 		for _, v := range days {
// 			if day, err = strconv.ParseInt(v, 10, 64); err != nil {
// 				err = fmt.Errorf("string to int64 err value:%s", v)
// 				return
// 			}
// 			if day >= dayBegin {
// 				continue
// 			}
// 			if err = dealDayProfitLoss(v, client, db); err != nil {
// 				logrus.Error("%s", err)
// 				continue
// 			}
// 			_, err = client.SRem(setKey, v)
// 			if err != nil {
// 				logrus.Error(fmt.Sprintf("redis srem err:%s data:%s", err, setKey))
// 			}
// 		}
// 	}
// }

// func dealDayProfitLoss(tm string, client *redislib.Config, db *gorm.DB) (err error) {
// 	var (
// 		allProfit map[string]string
// 		hashKey   = setting.GetOptionProfitDataKeyByTime(tm)
// 	)
// 	// 根据日期获取当天所有 profit loss data
// 	if allProfit, err = client.HGetAll(hashKey); err != nil {
// 		err = fmt.Errorf("redis hgetall err: %s", err)
// 		return
// 	}
// 	for key, item := range allProfit {
// 		profit := entity.OptionProfitLoss{}
// 		if err := json.Unmarshal([]byte(item), &profit); err != nil {
// 			logrus.Error(fmt.Sprintf("unmarshal option profit loss err:%s data:%+v", err, item))
// 			continue
// 		}
// 		if err = profit.UpsertProfitLoss(db); err != nil {
// 			logrus.Errorf("upsert option profit err:%s timeStr:%s profit:%+v", err, tm, profit)
// 			continue
// 		}
// 		if err = client.HDel(hashKey, key); err != nil {
// 			logrus.Errorf("hdel err:%s key:%s", err, key)
// 		}
// 	}
// 	return nil
// }

// // 生成新一天的数据
// func generateNewDayData() {
// 	profitMutex := redislib.NewMutex(domain.MutexOptionProfitStatic, 30*time.Second)
// 	if profitMutex.LockOnce() != nil {
// 		logrus.Error(fmt.Sprintf("option profit static lock err."))
// 		return
// 	}
// 	defer profitMutex.Unlock()

// 	var (
// 		// 获取下一天的起始时间
// 		profitTime    = util.OneDayBeginAndEndTimeStamp(time.Now().AddDate(0, 0, 1))
// 		profitTimeStr = strconv.FormatInt(profitTime, 10)
// 		redis         = redislib.Redis()
// 	)
// 	_, err := redis.SAdd(setting.GetOptionProfitDateKey(), profitTimeStr)
// 	if err != nil {
// 		logrus.Error(fmt.Sprintf("SAdd OptionProfitDate:%+v, err:%+v", profitTimeStr, err))
// 	}

// 	profitHashKey := setting.GetOptionProfitDataKeyByTime(profitTimeStr)
// 	// 获取所有用户
// 	usersKey := domain.AllOptionUsersKey()
// 	users, err := redis.HGetAll(usersKey)
// 	if err != nil {
// 		logrus.Error("generateNewDayData HGetAll", usersKey, "error:", err)
// 		return
// 	}

// 	key := util.ProfitLossBassKey(domain.CurrencyUSDT)
// 	profitLossData := entity.OptionProfitLoss{
// 		Currency:    domain.CurrencyUSDT,
// 		OperateTime: profitTime,
// 	}
// 	profitLossData.CreateTime = time.Now().UnixNano()

// 	for uid := range users {
// 		profitLossData.UID = uid
// 		hashKey := uid + key
// 		profitData, _ := json.Marshal(&profitLossData)
// 		err = redis.HSet(profitHashKey, hashKey, string(profitData))
// 		if err != nil {
// 			logrus.Error(fmt.Sprintf("create option profit redis err.hash:%s, key:%s, data:%+v", profitHashKey, hashKey, profitLossData))
// 		}
// 	}
// }

// func syncOptionTotalProfitToDb() {
// 	profitMutex := redislib.NewMutex(domain.MutesOptionCronSyncTotalProfit, 30*time.Second)
// 	if profitMutex.LockOnce() != nil {
// 		logrus.Error(fmt.Sprintf("sync option total profit to db lock err."))
// 		return
// 	}
// 	defer profitMutex.Unlock()
// 	redis := redislib.Redis()
// 	total, err := redis.HGetAll(domain.OptionTotalProfitHash)
// 	if err != nil {
// 		logrus.Error(fmt.Sprintf("get option total profit err:%+v", err))
// 		return
// 	}
// 	db, err := sqllib.Db()
// 	if err != nil {
// 		logrus.Error(fmt.Sprintf("get db err:%+v", err))
// 		return
// 	}
// 	for _, profitData := range total {
// 		dbTotalProfit := entity.OptionTotalProfit{}
// 		err = json.Unmarshal([]byte(profitData), &dbTotalProfit)
// 		if err != nil {
// 			logrus.Error(fmt.Sprintf("unmarshal option total profit data err:%+v, data:%s", err, profitData))
// 			continue
// 		}
// 		err = dbTotalProfit.UpsertTotalProfit(db)
// 		if err != nil {
// 			logrus.Error(fmt.Sprintf("option db upsert total profit err:%+v, data:%s", err, profitData))
// 		}
// 	}
// }
