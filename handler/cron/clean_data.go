package cron

// import (
// 	"context"
// 	"log"
// 	"time"

// 	"futures-asset/cache/optioncache"
// 	"futures-asset/conf"
// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/db/swap"
// 	"futures-asset/internal/domain/entity"
// 	"futures-asset/pkg/eslib"
// 	"futures-asset/pkg/redislib"

// 	"github.com/robfig/cron"
// 	"github.com/shopspring/decimal"
// )

// func StartCleanData() {
// 	// 每月清理一次
// 	var (
// 		swapTask     = cron.New()
// 		optionTask   = cron.New()
// 		cornStr      = "0 0 8 1 * ? *" // 每月1日
// 		swapInterval = "0 0 8 1/5 * ?" // 每5天清理一次
// 	)
// 	if conf.Conf.Biz.Debug {
// 		cornStr = "0 0 * * * ? *" // 每小时
// 	}

// 	_ = swapTask.AddFunc(swapInterval, cleanSwapData)
// 	swapTask.Start()

// 	_ = optionTask.AddFunc(cornStr, clearnOptionDemo)
// 	optionTask.Start()
// }

// func cleanSwapData() {
// 	// 只保留5天内机器人已结束的持仓记录
// 	swapPos := swap.NewPosSwap()
// 	swapPos.CreateTime = time.Now().AddDate(0, 0, -5).UnixNano()
// 	if err := swapPos.DeleteRecords(nil); err != nil {
// 		log.Printf("delete pos_swap data err:%s", err.Error())
// 	}
// 	// 只保留5天内机器人自减仓强平记录
// 	burstSwap := &entity.BurstSwap{}
// 	burstSwap.CreateTime = time.Now().AddDate(0, 0, -5).UnixNano()
// 	if err := burstSwap.DeleteRecords(nil); err != nil {
// 		log.Printf("delete burst_swap data err:%s", err.Error())
// 	}
// }

// func clearnOptionDemo() {
// 	// 获取所有期权模拟盘用户
// 	balance := decimal.NewFromInt(100000)
// 	userIds, err := optioncache.GetAllDemoUsers()
// 	if err != nil {
// 		log.Printf("get all option demo user err: %s", err.Error())
// 		return
// 	}

// 	fields := make(map[string]interface{})
// 	for i, uid := range userIds {
// 		fields[uid] = balance.String()
// 		// 每10个人一起清空一次
// 		if i != 0 && i%10 == 0 {
// 			if err := redislib.Redis().HMSet(optioncache.OptionDemoBalancePrefix, fields); err != nil {
// 				log.Printf("init user option demo asset err: %s uid: %+v", err.Error(), fields)
// 			}
// 			fields = make(map[string]interface{})
// 		}
// 	}
// 	// 清空剩余的人
// 	if len(fields) > 0 {
// 		if err := redislib.Redis().HMSet(optioncache.OptionDemoBalancePrefix, fields); err != nil {
// 			log.Printf("init user option demo asset err: %s uid: %+v", err.Error(), fields)
// 		}
// 	}

// 	// 删除模拟盘账单记录
// 	esClient := eslib.New(domain.EsOptionBillDemoIndex)
// 	if err := esClient.DeleteIndex(context.Background()); err != nil {
// 		log.Printf("delete es index about option demo bill err: %s index:%s", err.Error(), domain.EsOptionBillDemoIndex)
// 	}
// }
