package cron

// import (
// 	"encoding/json"
// 	"fmt"

// 	"futures-asset/cache/pair"
// 	"futures-asset/pkg/setting"

// 	"github.com/robfig/cron"
// 	"github.com/sirupsen/logrus"
// )

// // StartSyncPairInfo 更新币对配置
// func StartSyncPairInfo() {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Error(fmt.Sprintf("cron sync contract pair info err: %v", err))
// 			go StartSyncPairInfo()
// 		}
// 	}()

// 	syncPairInfo()
// 	// 每30s执行一次定时任务
// 	c := cron.New()
// 	_ = c.AddFunc("0/30 * * * * ?", syncPairInfo)
// 	c.Start()
// }

// func syncPairInfo() {
// 	err := setting.Service.UpdateAllPairInfo()
// 	if err != nil {
// 		logrus.Error(0, "cron sync contract pair info update all pair info err:", err)
// 		return
// 	}

// 	pairSettingMap, err := setting.Service.GetAllPairSettingInfo()
// 	if err != nil {
// 		logrus.Error(0, "cron sync contract pair info get all err:", err, fmt.Sprintf("%+v", pairSettingMap))
// 		return
// 	}

// 	for contractCode, contractSetting := range pairSettingMap {
// 		b, _ := json.Marshal(contractSetting)
// 		pair.SetPair(contractCode, b)
// 	}
// }
