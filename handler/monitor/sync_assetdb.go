package monitor

// import (
// 	"context"
// 	"encoding/json"
// 	"errors"
// 	"fmt"
// 	"log"
// 	"sync"
// 	"time"

// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/repository"
// 	"futures-asset/internal/message"
// 	"futures-asset/pkg/mqlib"
// 	"futures-asset/pkg/redislib"

// 	"github.com/redis/go-redis/v9"
// 	"github.com/shopspring/decimal"
// 	"github.com/sirupsen/logrus"
// )

// func SyncAssetDb(ctx context.Context, wg *sync.WaitGroup, userIndex string) {
// 	_amqp, err := mqlib.New()
// 	if err != nil {
// 		log.Println("mqlib.New() err in sync asset db")
// 	}
// 	count := 0
// 	sig := make(chan bool)
// 	go func() {
// 		<-ctx.Done()
// 		log.Println("sync asset monitor stopped")
// 		time.Sleep(5 * time.Second)
// 		sig <- true
// 		wg.Done()
// 		if _amqp != nil {
// 			log.Printf("close mq client %+v", _amqp.Close())
// 		}
// 	}()
// 	go func() {
// 		db, err := sqllib.Db()
// 		if err != nil {
// 			log.Println("get db err in sync asset db")
// 		}
// 		for {
// 			select {
// 			case <-ctx.Done():
// 				log.Println("redis brpop asset stopped")
// 				return
// 			default:
// 				results, err := redislib.Redis().BRPop(domain.SyncListSwapAsset+userIndex, 0)
// 				if err != nil {
// 					if !errors.Is(err, redis.Nil) {
// 						logrus.Error(fmt.Sprintf("brpop asset err:%v", err))
// 					}
// 					continue
// 				}
// 				asset := new(repository.LogAssetSync)
// 				if len(results) > 1 {
// 					err := json.Unmarshal([]byte(results[1]), asset)
// 					if err != nil {
// 						logrus.Error(fmt.Sprintf("swap asset json unmarshal err:%v  data:%+v", err, results[1]))
// 						continue
// 					}

// 					if asset.AssetSwap.UID == "" {
// 						logrus.Error(fmt.Sprintf("swap asset uid nil:%v  data:%+v", err, *asset))
// 						continue
// 					}

// 					if domain.RobotUsers.HasKey(asset.AssetSwap.UID) {
// 						count++
// 						if count%10000 > 0 {
// 							continue
// 						}
// 						count = 1
// 					}

// 					frozen := make(map[string]decimal.Decimal)
// 					_ = json.Unmarshal([]byte(asset.AssetSwap.Frozen), &frozen)
// 					assetMsg := message.AssetMsg{
// 						Currency: asset.AssetSwap.Currency,
// 						Balance:  asset.AssetSwap.Balance,
// 						Frozen:   frozen,
// 					}
// 					go message.New(asset.AssetSwap.UID, message.AssetQueueIndex, _amqp).
// 						TopicAsset(message.SwapAccount).Push(message.AccountMsg{
// 						MsgType: message.AccountMsgTypeAsset,
// 						MsgData: assetMsg,
// 					})

// 					asset.AssetSwap.TotalFrozen = decimal.Zero
// 					// TODO 方法已迁移到 repository wallet.go 中
// 					err = asset.AssetSwap.UpsertAsset(db)
// 					if err != nil {
// 						logrus.Error(fmt.Sprintf("swap asset update db err:%v  data:%+v", err, *asset))
// 					}
// 				}
// 			}
// 		}
// 	}()
// 	<-sig
// }
