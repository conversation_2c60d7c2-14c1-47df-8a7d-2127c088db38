package monitor

// import (
// 	"context"
// 	"encoding/json"
// 	"errors"
// 	"fmt"
// 	"log"
// 	"sync"
// 	"time"

// 	"futures-asset/cache/memorycache"
// 	"futures-asset/internal/domain"
// 	"futures-asset/internal/domain/entity"
// 	"futures-asset/internal/message"
// 	"futures-asset/pkg/mqlib"
// 	"futures-asset/pkg/redislib"
// 	"futures-asset/pkg/sqllib"
// 	"futures-asset/util"

// 	"github.com/redis/go-redis/v9"
// 	"github.com/shopspring/decimal"
// 	"github.com/sirupsen/logrus"
// )

// func SyncUserWinRate(ctx context.Context, wg *sync.WaitGroup) {
// 	_amqp, err := mqlib.New()
// 	if err != nil {
// 		log.Println("mqlib.New() err in sync asset db")
// 	}
// 	db, err := sqllib.Db()
// 	if err != nil {
// 		log.Println("get db err in sync user win rate")
// 	}

// 	for {
// 		select {
// 		case <-ctx.Done():
// 			time.Sleep(5 * time.Second)
// 			if _amqp != nil {
// 				log.Println(_amqp.Close())
// 			}
// 			log.Println("sync user win rate to db monitor stopped")
// 			wg.Done()
// 			return
// 		default:
// 			results, err := redislib.Redis().BRPop(domain.SyncListUserWinRate, domain.RedisBRPOPTimeout)
// 			if err != nil {
// 				if !errors.Is(err, redis.Nil) {
// 					logrus.Error(fmt.Sprintf("brpop sync user win rate err:%v", err))
// 				}
// 				continue
// 			}
// 			winRate := new(entity.UserWinRate)
// 			if len(results) <= 1 {
// 				continue
// 			}
// 			err = json.Unmarshal([]byte(results[1]), winRate)
// 			if err != nil {
// 				logrus.Error(fmt.Sprintf("sync user win rate json unmarshal err:%v  data:%+v", err, results[1]))
// 				continue
// 			}

// 			if len(winRate.UID) == 0 {
// 				continue
// 			}
// 			if len(winRate.AccountType) == 0 {
// 				winRate.AccountType = message.SwapAccount
// 			}
// 			cachedRate := memorycache.GetCachedWinRate(winRate.ContractCode, winRate.UID)
// 			cachedRate.Id = util.RateId(winRate.UID, winRate.AccountType, winRate.ContractCode)
// 			cachedRate.UID = winRate.UID
// 			cachedRate.AccountType = winRate.AccountType
// 			cachedRate.ContractCode = winRate.ContractCode
// 			cachedRate.Times += winRate.Times
// 			cachedRate.WinTimes += winRate.WinTimes
// 			cachedRate.TotalProfit = cachedRate.TotalProfit.Add(winRate.TotalProfit)
// 			cachedRate.TotalClose = cachedRate.TotalClose.Add(winRate.TotalClose)
// 			if cachedRate.Times != 0 {
// 				cachedRate.WinRate = decimal.NewFromFloat(float64(cachedRate.WinTimes) / float64(cachedRate.Times)).Truncate(domain.RatePrecision)
// 			}
// 			if !cachedRate.TotalClose.IsZero() {
// 				cachedRate.TotalProfitRate = cachedRate.TotalProfit.Div(cachedRate.TotalClose).Truncate(domain.RatePrecision)
// 			}
// 			cachedRate.TotalCloseMargin = cachedRate.TotalCloseMargin.Add(winRate.TotalCloseMargin)
// 			memorycache.CacheWinRate(cachedRate.ContractCode, cachedRate.UID, cachedRate)

// 			err = cachedRate.Upsert(db)
// 			if err != nil {
// 				logrus.Error(fmt.Sprintf("user win rate update err:%+v,UserWinRate:%+v", err, winRate))
// 			}
// 			message.New("", message.PosQueueIndex, _amqp).TopicUserWinRate(message.SwapAccount, cachedRate.ContractCode).Push(memorycache.GetCachedWinRateByCode(cachedRate.ContractCode))
// 		}
// 	}
// }
