package monitor

// import (
// 	"context"
// 	"encoding/json"
// 	"errors"
// 	"fmt"
// 	"futures-asset/cache/cachekey"
// 	"futures-asset/pkg/user"
// 	"log"
// 	"sync"
// 	"time"

// 	"github.com/redis/go-redis/v9"
// 	"github.com/sirupsen/logrus"
// )

// // BurstMonitor
// //
// // @desc: 监控爆仓队列, 收取强平费或者穿仓补贴费用
// //
// // @date 1/7/2021 10:22 AM
// func BurstMonitor(ctx context.Context, wg *sync.WaitGroup) {
// 	sig := make(chan bool)
// 	// esBill := eslib.New(domain.EsPosBillIndex)
// 	go func() {
// 		<-ctx.Done()
// 		log.Println("burst monitor stopped")
// 		time.Sleep(5 * time.Second)
// 		sig <- true
// 		wg.Done()
// 	}()
// 	go func() {
// 		for {
// 			select {
// 			case <-ctx.Done():
// 				log.Println("redis burst monitor stopped")
// 				return
// 			default:
// 				results, err := redislib.Redis().BRPop(cachekey.GetBurstUnlockListRedisKey(), 0)
// 				if err != nil {
// 					if !errors.Is(err, redis.Nil) {
// 						logrus.Error(fmt.Sprintf("brpop burst monitor err:%v", err))
// 					}
// 					continue
// 				}

// 				event := new(BurstEvent)
// 				if len(results) > 1 {
// 					err := json.Unmarshal([]byte(results[1]), event)
// 					if err != nil {
// 						logrus.Error(fmt.Sprintf("burst monitor json unmarshal err:%v  data:%+v", err, results[1]))
// 						continue
// 					}

// 					if event.UID == "" {
// 						logrus.Error(fmt.Sprintf("burst monitor uid is nil:%v  data:%+v", err, results[1]))
// 						continue
// 					}

// 					if event.ContractCode == "" {
// 						logrus.Error(fmt.Sprintf("burst monitor contractCode is nil:%v  data:%+v", err, results[1]))
// 						continue
// 					}

// 					userLevelRate, err := user.Service.GetUserLevelRate(event.UID)
// 					if err != nil {
// 						logrus.Errorln(fmt.Sprintf("get user type error: uid:%s %s", event.UID, err))
// 						userLevelRate = user.LevelRate{}
// 					}
// 					event.UserType = int32(userLevelRate.UserType)
// 					logrus.Info(fmt.Sprintf("burst event:%+v", event))
// 					// 执行爆仓操作
// 					if err := event.Burst(); err != nil {
// 						logrus.Error("event.Burst() error:" + err.Error())
// 					}
// 				}
// 			}
// 		}
// 	}()
// 	<-sig
// }
