package monitor

// import (
// 	"fmt"
// 	"futures-asset/internal/domain"
// 	"futures-asset/pkg/mqlib"
// 	"futures-asset/pkg/redislib"
// 	"time"

// 	"github.com/sirupsen/logrus"
// )

// func PublishTrade() {
// 	defer exceptionFunc(PublishTrade)
// 	_amp, err := mqlib.New()
// 	if err != nil {
// 		logrus.Error(fmt.Sprintf("mqlib.New err:%+v", err))
// 		return
// 	}
// 	for {
// 		data, err := redislib.Redis().BRPop(domain.ContractTradeKey, domain.RedisBRPOPTimeout)
// 		if err != nil {
// 			if err.Error() != "redis: nil" {
// 				logrus.Error(fmt.Sprintf("get ContractAssetKey data err:%+v", err))
// 			}
// 			continue
// 		}
// 		if len(data) > 1 {
// 			err = _amp.Send(domain.ContractExchange, domain.ContractTradeBindKey, []byte(data[1]))
// 			if err != nil {
// 				logrus.Error(fmt.Sprintf("Contract Mq Publish Asset err:%+v,byte:%s", err, data[1]))
// 				_ = redislib.Redis().LPush(domain.ContractTradeKey, data[1])
// 			}
// 		}
// 		time.Sleep(time.Millisecond * 40)
// 	}
// }
