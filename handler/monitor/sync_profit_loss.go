package monitor

// import (
// 	"context"
// 	"encoding/json"
// 	"errors"
// 	"fmt"
// 	"futures-asset/internal/domain"
// 	repository "futures-asset/internal/domain/entity"
// 	"futures-asset/pkg/redislib"
// 	"futures-asset/pkg/sqllib"
// 	"log"
// 	"sync"
// 	"time"

// 	"github.com/redis/go-redis/v9"
// 	"github.com/sirupsen/logrus"
// )

// func SyncProfitLoss(ctx context.Context, wg *sync.WaitGroup) {
// 	db, err := sqllib.Db()
// 	if err != nil {
// 		log.Println("get db err in sync user proft and loss")
// 	}
// 	for {
// 		select {
// 		case <-ctx.Done():
// 			time.Sleep(5 * time.Second)
// 			log.Println("sync proft and loss monitor stopped")
// 			wg.Done()
// 			return
// 		default:
// 			results, err := redislib.Redis().BRPop(domain.SyncListSwapProfitLoss, domain.RedisBRPOPTimeout)
// 			if err != nil {
// 				if !errors.Is(err, redis.Nil) {
// 					logrus.Error(fmt.Sprintf("brpop user proft and loss err:%v", err))
// 				}
// 				continue
// 			}
// 			pos := new(repository.LogPosSync)
// 			if len(results) > 1 {
// 				err := json.Unmarshal([]byte(results[1]), pos)
// 				if err != nil {
// 					logrus.Error(fmt.Sprintf("user proft and loss json unmarshal err:%v  data:%+v", err, results[1]))
// 					continue
// 				}

// 				if pos.PosSwap.UID == "" {
// 					logrus.Error(fmt.Sprintf("user proft and loss uid nil:%v  data:%+v", err, *pos))
// 					continue
// 				}

// 				err = pos.PosSwap.UpsertPos(db)
// 				if err != nil {
// 					logrus.Error(fmt.Sprintf("user proft and loss update db err:%v  data:%+v", err, *pos))
// 				}
// 			}
// 		}
// 	}
// }
