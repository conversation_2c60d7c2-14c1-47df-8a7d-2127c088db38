// Package monitor /**
package monitor

// import (
// 	"context"
// 	"encoding/json"
// 	"runtime/debug"
// 	"sync"
// 	"time"

// 	"futures-asset/internal/domain"
// 	"futures-asset/pkg/redislib"
// 	"futures-asset/pkg/setting"
// 	"futures-asset/util"

// 	"github.com/redis/go-redis/v9"
// 	"github.com/sirupsen/logrus"
// )

// const swapContractPairChannel = "contract_swap_update_pair"

// func SubscribePair(ctx context.Context, wg *sync.WaitGroup) {
// 	defer func() {
// 		if err := recover(); err != nil {
// 			logrus.Errorf("SubscribePair err:%+v", err)
// 			debug.PrintStack()
// 			go SubscribePair(ctx, wg)
// 		}
// 	}()

// 	pubSub := redislib.RedisSpot().Client().Subscribe(context.Background(), swapContractPairChannel)
// 	for {
// 		msg, err := pubSub.ReceiveMessage(context.Background())
// 		if err != nil {
// 			time.Sleep(1 * time.Second)
// 			pubSub = redislib.RedisSpot().Client().Subscribe(context.Background(), swapContractPairChannel)
// 			continue
// 		}

// 		logrus.Infof("Receive update contract pair:%+v", msg)
// 		dealMessage(ctx, msg, wg)
// 	}
// }

// func dealMessage(ctx context.Context, msg *redis.Message, wg *sync.WaitGroup) {
// 	switch msg.Channel {
// 	case swapContractPairChannel:
// 		pairInfo := setting.ContractPair{}
// 		err := json.Unmarshal([]byte(msg.Payload), &pairInfo)
// 		if err != nil {
// 			logrus.Errorf("Unmarshal err:%s", err.Error())
// 		}

// 		if pairInfo.OptType == domain.PairOptTypeAdd {
// 			logrus.Infof("new contract pairInfo:%+v", pairInfo)
// 			wg.Add(1)
// 			go SyncPosDb(ctx, wg, util.ContractCode(pairInfo.Base, pairInfo.Quote))
// 			// go SyncAssetDb(ctx, wg, util.ContractCode(pairInfo.Base, pairInfo.Quote))
// 		}
// 	}
// }
