package monitor

// import (
// 	"errors"
// 	"fmt"
// 	"futures-asset/internal/domain"
// 	"futures-asset/pkg/mqlib"
// 	"futures-asset/pkg/redislib"
// 	"time"

// 	"github.com/redis/go-redis/v9"
// 	"github.com/sirupsen/logrus"
// )

// func ContractMqPublishAsset() {
// 	defer exceptionFunc(ContractMqPublishAsset)
// 	_amp, err := mqlib.New()
// 	if err != nil {
// 		logrus.Error(fmt.Sprintf("mqlib.New err:%+v", err))
// 		return
// 	}
// 	redisClient := redislib.Redis()
// 	for {
// 		data, err := redisClient.BRPop(domain.ContractAssetKey, domain.RedisBRPOPTimeout)
// 		if err != nil {
// 			if !errors.Is(err, redis.Nil) {
// 				logrus.Error(fmt.Sprintf("get ContractAssetKey data err:%+v", err))
// 			}
// 			continue
// 		}
// 		if len(data) > 1 {
// 			err = _amp.Send(domain.ContractExchange, domain.ContractAssetBindKey, []byte(data[1]))
// 			if err != nil {
// 				logrus.Error(fmt.Sprintf("Contract Mq Publish Asset err:%+v,byte:%s", err, data[1]))
// 				_ = redisClient.LPush(domain.ContractAssetKey, data[1])
// 			}
// 		}
// 	}
// }

// func exceptionFunc(_func func()) {
// 	if err := recover(); err != nil {
// 		logrus.Error(fmt.Sprintf("rabbit save data is err:%v", err))
// 		for {
// 			if err := mqlib.Init(); err != nil {
// 				time.Sleep(time.Second)
// 				continue
// 			}
// 			break
// 		}
// 		_func()
// 	}
// }
