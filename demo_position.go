package main

import (
	"fmt"

	"futures-asset/internal/domain"
	"futures-asset/internal/domain/repository"
	"futures-asset/internal/usecase"

	"github.com/shopspring/decimal"
)

func main() {
	fmt.Println("=== OpenLongPos 方法演示 ===")

	// 创建PositionUseCase实例
	uc := &usecase.PositionUseCase{}

	// 创建测试用的用户资产
	userAsset := &repository.AssetSwap{
		UID:     "demo_user_001",
		Balance: make(repository.BalanceAsset),
		LongPos: repository.PosSwap{
			PosStatus: domain.PosStatusNone, // 初始状态为无仓位
		},
	}

	// 初始化余额
	userAsset.Balance.Add("USDT", decimal.NewFromFloat(10000))
	fmt.Printf("初始余额: %s USDT\n", userAsset.Balance.Get("USDT"))

	// 执行开多仓操作
	fmt.Println("\n执行开多仓操作...")
	err := uc.OpenLongPos(userAsset, nil)
	if err != nil {
		fmt.Printf("错误: %v\n", err)
		return
	}

	// 显示结果
	fmt.Println("\n=== 开仓结果 ===")
	fmt.Printf("仓位状态: %v\n", userAsset.LongPos.PosStatus)
	fmt.Printf("仓位方向: %v (1=多仓, 2=空仓)\n", userAsset.LongPos.PosSide)
	fmt.Printf("合约代码: %s\n", userAsset.LongPos.ContractCode)
	fmt.Printf("计价币种: %s\n", userAsset.LongPos.Currency)
	fmt.Printf("仓位数量: %s\n", userAsset.LongPos.Pos)
	fmt.Printf("可平仓位: %s\n", userAsset.LongPos.PosAvailable)
	fmt.Printf("开仓均价: %s\n", userAsset.LongPos.OpenPriceAvg)
	fmt.Printf("杠杆倍数: %d\n", userAsset.LongPos.Leverage)
	fmt.Printf("保证金模式: %d (1=全仓, 2=逐仓)\n", userAsset.LongPos.MarginMode)

	if userAsset.LongPos.Isolated() {
		fmt.Printf("逐仓保证金: %s\n", userAsset.LongPos.IsolatedMargin)
	}

	// 显示余额变化
	fmt.Printf("\n=== 资产变化 ===\n")
	fmt.Printf("当前余额: %s USDT\n", userAsset.Balance.Get("USDT"))
	fmt.Printf("手续费扣除: %s USDT\n", decimal.NewFromFloat(5.0))

	// 再次开仓演示
	fmt.Println("\n=== 第二次开仓演示 ===")
	originalPos := userAsset.LongPos.Pos
	originalAvg := userAsset.LongPos.OpenPriceAvg

	err = uc.OpenLongPos(userAsset, nil)
	if err != nil {
		fmt.Printf("错误: %v\n", err)
		return
	}

	fmt.Printf("原仓位数量: %s -> 新仓位数量: %s\n", originalPos, userAsset.LongPos.Pos)
	fmt.Printf("原开仓均价: %s -> 新开仓均价: %s\n", originalAvg, userAsset.LongPos.OpenPriceAvg)
	fmt.Printf("当前余额: %s USDT\n", userAsset.Balance.Get("USDT"))

	fmt.Println("\n=== 演示完成 ===")
}
