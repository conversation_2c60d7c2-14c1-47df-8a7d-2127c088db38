package configs

import "time"

type Config struct {
	Grpc struct {
		Port int `json:"port" yaml:"port"`
	} `json:"grpc" yaml:"grpc"`

	ServiceURL struct {
		CryptoQuote string `json:"cryptoQuote" yaml:"cryptoQuote"`
		Wallet      string `json:"wallet" yaml:"wallet"`
		Campaign    string `json:"campaign" yaml:"campaign"`
		LotusFinger string `json:"lotusFinger" yaml:"lotusFinger"`
	} `json:"serviceURL" yaml:"serviceURL"`

	Simulation struct {
		InitBalance int64 `json:"initBalance" yaml:"initBalance"`
	} `json:"simulation" yaml:"simulation"`

	Switch struct {
		SkipCloseImmediatelyCheck bool `json:"skipCloseImmediatelyCheck" yaml:"skipCloseImmediatelyCheck"`
	} `json:"switch" yaml:"switch"`

	S3 struct {
		BucketName string `json:"bucketName" yaml:"bucketName"`
		Path       string `json:"path" yaml:"path"`
		Credential string `json:"credential" yaml:"credential"`
		ProjectID  string `json:"projectId" yaml:"projectId"`
		Domain     string `json:"domain" yaml:"domain"`
	} `json:"s3" yaml:"s3"`

	Kafka struct {
		Debug  bool `json:"debug" yaml:"debug"`
		Worker struct {
			Size   int `json:"size" yaml:"size"`
			Buffer int `json:"buffer" yaml:"buffer"`
		} `json:"worker" yaml:"worker"`
		TopicRetentionPolicy struct {
			RetentionMs     string `json:"retentionMs" yaml:"retentionMs"`
			RetentionMemory string `json:"retentionMemory" yaml:"retentionMemory"`
			SegmentBytes    string `json:"segmentBytes" yaml:"segmentBytes"`
		} `json:"topicRetentionPolicy" yaml:"topicRetentionPolicy"`
	} `json:"kafka" yaml:"kafka"`

	MatchLiquidationTimeout  time.Duration `json:"match_liquidation_timeout" yaml:"match_liquidation_timeout"`
	MatchTPSLTimeout         time.Duration `json:"match_tpsl_timeout" yaml:"match_tpsl_timeout"`
	InitLongShortPropTimeout time.Duration `json:"init_long_short_prop_timeout" yaml:"init_long_short_prop_timeout"`
	SocketWorkerCount        int           `json:"socketWorkerCount" yaml:"socketWorkerCount"`

	HttpClientTransportConfig struct { //nolint:revive,stylecheck
		IdleConnTimeout       int `json:"idleConnTimeout" yaml:"idleConnTimeout"`
		TLSHandshakeTimeout   int `json:"tLSHandshakeTimeout" yaml:"tLSHandshakeTimeout"`
		ExpectContinueTimeout int `json:"expectContinueTimeout" yaml:"expectContinueTimeout"`
		ResponseHeaderTimeout int `json:"responseHeaderTimeout" yaml:"responseHeaderTimeout"`
		MaxIdleConns          int `json:"maxIdleConns" yaml:"maxIdleConns"`
		MaxIdleConnsPerHost   int `json:"naxIdleConnsPerHost" yaml:"maxIdleConnsPerHost"`
		MaxConnsPerHost       int `json:"maxConnsPerHost" yaml:"maxConnsPerHost"`
		NetDialerTimeOut      int `json:"netDialerTimeOut" yaml:"netDialerTimeOut"`
		NetDialerKeepAlive    int `json:"netDialerKeepAlive" yaml:"netDialerKeepAlive"`
	} `json:"httpClientTransportConfig" yaml:"httpClientTransportConfig"`
}
