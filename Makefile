.PHONY: help test-race lint sec-scan gci-format db-mysql-init build docker-image-build db-mysql-down

help: ## show this help
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z0-9_-]+:.*?## / {sub("\\\\n",sprintf("\n%22c"," "), $$2);printf "\033[36m%-25s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

PROJECT_NAME?=gateway

########
# test #
########

test-race: ## launch all tests with race detection
	go test ./... -cover -race

########
# lint #
########

lint: ## lints the entire codebase
	@golangci-lint run ./... --config=./.golangci.yaml

#######
# sec #
#######

sec-scan: trivy-scan vuln-scan ## scan for security and vulnerability issues

trivy-scan: ## scan for sec issues with trivy (trivy binary needed)
	trivy fs --exit-code 1 --no-progress --severity CRITICAL ./

vuln-scan: ## scan for vulnerability issues with govulncheck (govulncheck binary needed)
	govulncheck ./...

######
# db #
######
MYSQL_SQL_PATH="./database/migrations/mysql"
MYSQL_SEEDERS_SQL_PATH="./database/migrations/seeders"

db-mysql-seeders-init:
	@( \
	printf "Enter seeder name: "; read -r SEEDER_NAME && \
	migrate create -ext sql -dir ${MYSQL_SEEDERS_SQL_PATH} $${SEEDER_NAME} \
	)

db-mysql-init:
	@( \
	printf "Enter migrate name: "; read -r MIGRATE_NAME && \
	migrate create -ext sql -dir ${MYSQL_SQL_PATH} $${MIGRATE_NAME} \
	)

db-mysql-up:
	@( \
	printf "Enter pass for db: \n"; read -rs DB_PASSWORD && \
	printf "Enter port(3306...): \n"; read -r DB_PORT &&\
	migrate --database "mysql://root:$${DB_PASSWORD}@tcp(localhost:$${DB_PORT})/$(PROJECT_NAME)?charset=utf8&parseTime=True&loc=Local" --path ${MYSQL_SQL_PATH} up \
	)

db-mysql-down:
	@( \
	printf "Enter pass for db: \n"; read -s DB_PASSWORD && \
	printf "Enter port(3306...): \n"; read -r DB_PORT &&\
	migrate --database "mysql://root:$${DB_PASSWORD}@tcp(localhost:$${DB_PORT})/$(PROJECT_NAME)?charset=utf8&parseTime=True&loc=Local" --path ${MYSQL_SQL_PATH} down \
	)

SQL_FILE_TIMESTAMP=$(shell date '+%Y%m%d%H%M%S')

gen-migrate-sql:
	@( \
	printf "Enter file name: "; read -r FILE_NAME; \
	touch database/migrations/mysql/$(SQL_FILE_TIMESTAMP)_$$FILE_NAME.up.sql; \
	touch database/migrations/mysql/$(SQL_FILE_TIMESTAMP)_$$FILE_NAME.down.sql; \
	)

###########
#   GCI   #
###########
gci-format:
	gci write --skip-generated -s standard -s default -s "prefix(yt.com/backend)" -s "prefix($(PROJECT_NAME))" ./

#########
# build #
#########

GitCommit=$(shell git rev-parse HEAD)
Date=$(shell date -Iseconds)

build:
	@( \
	printf "Enter file name: "; read -r VERSION; \
	go build -ldflags "-s -w -X 'main.Version=$$VERSION' -X 'main.Built=$(Date)' -X 'main.GitCommit=$(GitCommit)'" -o ./bin/$(PROJECT_NAME) ./cmd/$(PROJECT_NAME) \
	)

docker-image-build:
	docker build \
		-f ./build/Dockerfile \
		-t $(PROJECT_NAME) \
		--platform linux/amd64 \
		--build-arg BUILT=$(Date) \
        --build-arg GIT_COMMIT=$(GitCommit) \
        --ssh default=$$HOME/.ssh/id_rsa \
		./


###################
#   GO GENERATE   #
###################

go-generate:
	go generate ./...

go-generate-clean:
	rm -rf ./internal/domain/mock ;\
	rm -rf ./internal/usecase/mock ;\
	rm -rf ./internal/repository/mock

#####################
#   GIT MULTIREPO   #
#####################

git-cnx-gateway-add-remote:
	git remote add cnx-gateway ../cnx-gateway &&\
	git pull cnx-gateway

git-cnx-gateway-merge:
	@( \
	printf "Enter cnx-gateway branch name: "; read -r BRANCH && \
	git merge --allow-unrelated-histories cnx-wallet/$$BRANCH \
	)

git-cnx-gateway-rm-remote:
	git remote rm cnx-gateway