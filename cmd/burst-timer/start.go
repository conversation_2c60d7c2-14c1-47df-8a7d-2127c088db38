package main

import (
	"context"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	"futures-asset/cache"
	"futures-asset/cache/sharedcache"
	"futures-asset/conf"
	"futures-asset/internal/router"
	"futures-asset/pkg/eslib"
	"futures-asset/pkg/maintain"
	"futures-asset/pkg/mqlib"
	"futures-asset/pkg/redislib"
	"futures-asset/pkg/sqllib"
	"futures-asset/pkg/wslib"
	"futures-asset/service/burst"
)

func main() {
	if err := sqllib.Init(c); err != nil {
		log.Fatalf("init mysql err: " + err.<PERSON>rror())
	}

	// init redis
	if err := redislib.Init(c.Mid.Redis.Contract, ""); err != nil {
		log.Fatalf("init redis err: " + err.<PERSON>rror())
	}
	// init spot redis
	if err := redislib.InitSpot(c.Mid.Redis.Spot, ""); err != nil {
		log.Fatalf("init redis err: " + err.Error())
	}

	cache.Init()
	sharedcache.Init()
	burst.Init()

	// init es
	if err := eslib.Init(c.Mid.ES.Contract); err != nil {
		log.Fatalf("init es err: " + err.Error())
	}

	// init mq
	if err := mqlib.Init(); err != nil {
		log.Fatalf("init mq err: " + err.Error())
	}

	// init mqtt
	if err := mqlib.InitMQTT("burst-timer"); err != nil {
		log.Fatalf("init mqtt err: " + err.Error())
	}

	wg := new(sync.WaitGroup)

	// 启动ws同步器
	WsSyncStart()

	// 启动停机监听
	maintain.InitMemoryCache()

	ctx, cancel := context.WithCancel(context.Background())
	go burst.CronServiceStart(ctx, wg, false, false)

	time.Sleep(time.Second)

	// init router
	engine := router.InitIndexRouter()
	srv := &http.Server{
		Addr:    ":" + c.Biz.Server.Port,
		Handler: engine,
	}
	go func() {
		if err := srv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Fatalln("main函数异常", err.Error())
		}
	}()

	ch := make(chan os.Signal, 1)
	signal.Notify(ch, syscall.SIGINT, syscall.SIGKILL, syscall.SIGQUIT, syscall.SIGTERM)
	sig := <-ch
	fmt.Println(sig)
	cancel()
	wg.Wait()
}

func WsSyncStart() {
	wslib.InitBinanceWs("fstream.binance.com")
	wslib.BinanceWs.Subscribe("!markPrice@arr")
	burstContracts := sharedcache.GetBurstServerContracts()
	if len(burstContracts) < 1 {
		log.Fatalf("burst contracts is empty")
	}
	wslib.InitHuobiWs("api.hbdm.com")
	wslib.InitOkexWs("ws.okx.com:8443")
	for _, coinPair := range burstContracts {
		time.Sleep(time.Millisecond * 100)
		wslib.HuobiWs.Subscribe(fmt.Sprintf("market.%s.index.1min", strings.ToUpper(coinPair)))

		wslib.OkexWs.Subscribe(map[string]string{"channel": "index-tickers", "instId": strings.ToUpper(coinPair)})
	}
}
