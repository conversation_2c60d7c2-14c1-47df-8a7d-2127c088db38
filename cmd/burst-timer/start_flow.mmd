flowchart TB
    InitConfig["Init Config"] --> InitLogger["Init Logger"]
    InitLogger --> InitMySQL["Init MySQL"]
    InitMySQL --> InitRedis["Init Redis"]
    InitRedis --> InitSpotRedis["Init Spot Redis"]
    InitSpotRedis --> CacheInit["cache.Init()"]
    CacheInit --> SharedCacheInit["sharedcache.Init()"]
    SharedCacheInit --> BurstInit["burst.Init()"]
    BurstInit --> InitES["Init ES"]
    InitES --> InitMQ["mqlib.Init()"]
    InitMQ --> InitMQTT["mqlib.InitMQTT()"]
    InitMQTT --> WsSyncStart["WsSyncStart()"]
    WsSyncStart --> InitMemoryCache["maintain.InitMemoryCache()"]
    InitMemoryCache --> CronService["burst.CronServiceStart()"]
    CronService --> HTTPServer["Start HTTP Server"]
    HTTPServer --> WaitSignal["Wait for Shutdown Signal"]
    WaitSignal --> Shutdown["Cancel Context & wg.Wait()"]

    subgraph WsSyncStartFunction[WsSyncStart Function]
        WSBinanceInit["wslib.InitBinanceWs()"] --> WSSubscribe1["wslib.BinanceWs.Subscribe(!markPrice@arr)"]
        WSSubscribe1 --> GetContracts["sharedcache.GetBurstServerContracts()"]
        GetContracts --> WSHuobiInit["wslib.InitHuobiWs()"]
        GetContracts --> WSOkexInit["wslib.InitOkexWs()"]
        WSHuobiInit --> WSSubscribe2["wslib.HuobiWs.Subscribe(market.PAIR.index.1min)"]
        WSOkexInit --> WSSubscribe3["wslib.OkexWs.Subscribe(index-tickers)"]
    end

    WsSyncStart --> WSBinanceInit
    WSSubscribe3 --> InitMemoryCache
