package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"futures-asset/cache"
	"futures-asset/cache/sharedcache"
	"futures-asset/conf"
	"futures-asset/internal/domain/db/swap"
	"futures-asset/pkg/eslib"
	"futures-asset/pkg/mqlib"
	"futures-asset/service/burst"

	"github.com/sirupsen/logrus"
)

func main() {
	// init config
	c, err := conf.Init()
	if err != nil {
		log.Fatalf("init config err: %s", err.Error())
	}
	defer c.Close()

	// init logger
	loglib.InitLog(c.Biz.Log.LogDir, c.Biz.Log.SaveTime, c.Biz.Log.LogReduce)

	if err := sqllib.Init(c); err != nil {
		log.Fatalf("init mysql err: " + err.<PERSON>rror())
	}

	// swap.CreateRivalBurstSwapTable()
	swap.CreateBurstSwapTable()
	swap.CreateLogBurstSwapTable()

	// init redis
	if err := redislib.Init(c.Mid.Redis.Contract, ""); err != nil {
		log.Fatalf("init redis err: " + err.Error())
	}
	// init spot redis
	if err := redislib.InitSpot(c.Mid.Redis.Spot, ""); err != nil {
		log.Fatalf("init redis err: " + err.Error())
	}

	cache.Init()
	sharedcache.Init()
	burst.Init()

	// init es
	if err := eslib.Init(c.Mid.ES.Contract); err != nil {
		log.Fatalf("init es err: " + err.Error())
	}

	// init mq
	if err := mqlib.Init(); err != nil {
		log.Fatalf("init mq err: " + err.Error())
	}

	wg := new(sync.WaitGroup)
	cronCtx, cronCtxCancel := context.WithCancel(context.Background())
	go burst.CronServiceStart(cronCtx, wg, true, false)

	time.Sleep(time.Second)

	// 爆仓扫描服务启动
	scannerCtx, scannerCtxCancel := context.WithCancel(context.Background())
	go burst.ScannerStart(scannerCtx, wg, false)

	// 监听用户费率更新
	go burst.UserLevelRateListener()

	// 监听默认用户费率更新
	go burst.UserLevelSettingListener()

	// 定时清理用户等级
	cronCleanUserLevelCtx, cronCleanUserLevelCtxCancel := context.WithCancel(context.Background())
	go burst.CronCleanUserLevelMap(cronCleanUserLevelCtx, wg, false)

	ch := make(chan os.Signal, 1)
	signal.Notify(ch, os.Interrupt, os.Kill, syscall.SIGQUIT, syscall.SIGINT, syscall.SIGTERM)
	sig := <-ch
	fmt.Println(sig)

	wg.Add(1)
	burst.RunningServices.Lock()
	for coinPair, service := range burst.RunningServices.Map {
		service.Close()
		logrus.Infoln(coinPair, "burst service closed")
	}
	burst.RunningServices.Unlock()
	wg.Done()

	cronCtxCancel()
	scannerCtxCancel()
	cronCleanUserLevelCtxCancel()
	wg.Wait()
}
