package swapcache

import (
	"fmt"
	"runtime/debug"
	"strings"

	"futures-asset/cache"
	"futures-asset/internal/domain/repository"
	"futures-asset/util"
)

// CacheParam contains binded and validated data.
type CacheParam struct {
	repository.TradeCommon
	UID string `json:"uid"` // 用户ID
}

type AssetCacheKey struct {
	// redis key
	HashKey        string // 用户哈希Key
	LeverageKey    string // 杠杆倍数
	HoldModeKey    string // 持仓模式 1.双向持仓 2.单向持仓
	AssetMode      string // 保证金模式 1.单币保证金 2.联合保证金
	FrozenKey      string // 全仓冻结保证金
	BalanceKey     string // 余额
	LongPosKey     string // 多仓仓位
	ShortPosKey    string // 空仓仓位
	BothPosKey     string // 单向持仓仓位
	TrialDetailKey string // 体验金结构存储的key

	TrialBalanceKey  string // 体验金余额
	TrialConsumeKey  string // 体验金消耗
	TrialLossKey     string // 体验金亏损
	TrialRecoveryKey string // 体验金回收
	TrialLongPosKey  string // 体验金多仓仓位
	TrialShortPosKey string // 体验金空仓仓位
	TrialBothPosKey  string // 体验金单向持仓仓位
}

// GetRedisKey get contract asset redis key
func (slf *AssetCacheKey) GetRedisKey(uid, base, quote string) {
	if len(base) > 0 && strings.ToLower(base) == strings.ToLower(quote) {
		fmt.Println(strings.ToLower(base), "==", strings.ToLower(quote))
		debug.PrintStack()
	}
	slf.HashKey = cache.Prefix + uid
	slf.LeverageKey = cache.LeverageSuffix
	slf.HoldModeKey = cache.HoldModeSuffix
	slf.AssetMode = cache.JoinMarginSuffix
	slf.BalanceKey = cache.BalanceSuffix
	slf.FrozenKey = cache.FrozenSuffix
	if len(base) != 0 && len(quote) != 0 && (strings.ToUpper(base) != strings.ToUpper(quote)) {
		slf.LongPosKey = fmt.Sprintf("%s%s", util.ContractCode(base, quote), cache.LongPosSuffix)
		slf.ShortPosKey = fmt.Sprintf("%s%s", util.ContractCode(base, quote), cache.ShortPosSuffix)
		slf.BothPosKey = fmt.Sprintf("%s%s", util.ContractCode(base, quote), cache.BothPosSuffix)

		slf.TrialLongPosKey = fmt.Sprintf("%s%s%s", cache.TrialAssetPrefix, util.ContractCode(base, quote), cache.LongPosSuffix)
		slf.TrialShortPosKey = fmt.Sprintf("%s%s%s", cache.TrialAssetPrefix, util.ContractCode(base, quote), cache.ShortPosSuffix)
		slf.TrialBothPosKey = fmt.Sprintf("%s%s%s", cache.TrialAssetPrefix, util.ContractCode(base, quote), cache.BothPosSuffix)
	}
	slf.TrialDetailKey = fmt.Sprintf("%s%s", cache.TrialAssetPrefix, cache.DetailSuffix)
	slf.TrialBalanceKey = fmt.Sprintf("%s%s", cache.TrialAssetPrefix, cache.BalanceSuffix)
	slf.TrialConsumeKey = fmt.Sprintf("%s%s", cache.TrialAssetPrefix, cache.ConsumeSuffix)
	slf.TrialLossKey = fmt.Sprintf("%s%s", cache.TrialAssetPrefix, cache.LossSuffix)
	slf.TrialRecoveryKey = fmt.Sprintf("%s%s", cache.TrialAssetPrefix, cache.RecoverySuffix)
}
