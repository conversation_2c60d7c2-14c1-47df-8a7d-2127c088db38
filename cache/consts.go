package cache

import (
	"fmt"
	"strings"

	"futures-asset/internal/domain"

	"github.com/shopspring/decimal"
)

type TaskType string

// contract asset redis key
const (
	Prefix             = "fasset:"              // fasset:{uid}
	PosFlag            = "pos"                  // pos flag
	HoldModeSuffix     = "hold:mode"            // 持仓模式 1.双向 2 单向
	JoinMarginSuffix   = "join"                 // 保证金模式 1.单币保证金 2.联合保证金
	OrderConfirmSuffix = "config:order_confirm" // 下单确认模式
	OrderConfigSuffix  = "config:order_config"  // 交易设置
	BalanceSuffix      = "balance"              // swap asset balance suffix
	LeverageSuffix     = "leverage"             // leverage suffix
	FrozenSuffix       = "frozen"               // swap asset frozen suffix
	ConsumeSuffix      = "consume"              // swap asset consume suffix
	LossSuffix         = "loss"                 // swap asset loss suffix
	RecoverySuffix     = "recovery"             // swap asset recovery suffix
	DetailSuffix       = "detail"               // swap asset detail suffix

	// FrozenValueSuffix = "frozen:value" // swap asset frozen suffix
	RealSuffix = ":real" // swap asset real profit suffix
	// pos
	LongPosSuffix    = ":pos:long"    // swap asset long pos profit suffix
	ShortPosSuffix   = ":pos:short"   // swap asset short pos profit suffix
	BothPosSuffix    = ":pos:both"    // swap asset both pos profit suffix
	// 体验金
	TrialAssetPrefix = "trial:" // 体验金Prefix
	// 平台
	PlatformBinance = "binance"
	PlatformHuobi   = "huobi"
	PlatformOkex    = "okex"

	// K线类型
	KLineTypeLastPrice  = 1 // 最新成交价
	KLineTypeIndexPrice = 2 // 指数价格
	KLineTypeMarkPrice  = 3 // 标价价格
)

type (
	SwapBurstTask struct {
		Type     TaskType `json:"type"`
		Data     []byte   `json:"data"`
		FuncName string   `json:"func_name"`
	}
	LiquidationInfo struct {
		BurstId              string                 `json:"burstId"`
		UID                  string                 `json:"uid"`
		PosId                string                 `json:"posId"`
		MarginMode           domain.MarginMode      `json:"marginMode"`
		PosSide              int32                  `json:"posSide"`
		LiquidationType      domain.LiquidationType `json:"liquidationType"`
		CurrentLevel         int                    `json:"currentLevel"`
		TargetLevel          int                    `json:"targetLevel	"`
		TargetLimit          decimal.Decimal        `json:"targetLimit"`
		CancelType           domain.CancelType      `json:"cancelType"`
		CollapsePrice        decimal.Decimal        `json:"collapsePrice"`        // 破产价格
		CollapsePriceFormula string                 `json:"collapsePriceFormula"` // 破产价格公式
		BurstTime            int64                  `json:"burstTime"`
		IsTrialPos           bool                   `json:"isTrialPos"` // 是否体验金仓位
	}
)

func GetContractLastPriceRedisChannel(accountType string, base string, quote string) string {
	return fmt.Sprintf("c_%s:last_price_channel:%v_%v", accountType, base, quote)
}

func GetContractMarkPriceRedisChannel(base, quote string) string {
	return fmt.Sprintf("contract_swap_mark_price_channel_%s_%s", strings.ToLower(base), strings.ToLower(quote))
}

func GetCloseAllUserPosListRedisKey(base, quote string) string {
	return fmt.Sprintf("fasset:burst:%s-%s:close_all_pos_list", base, quote)
}
