# gateway

go layout template 範本，[程式規範](https://docs.google.com/document/d/1w0Ya8YSTDZGo29zS1EKnJInFUdm3BRH6zEFDBv_nKCo/edit)

- [目錄結構](#目錄結構)
- [Error Code](#error-code)
- [API router](#api-router)
- [Time Format](#time-format)
- [Http Code](#http-code)
- [Response](#response)
- [Version](#versiontxt)
- [GitLab Flow](#gitlab-flow)

# 目錄結構

```
.
├── Makefile
├── README.md
├── build
│         └── Dockerfile
├── cmd 
│         └── gateway 服務啟動流程入口點
│            └── main.go
├── configs
│         ├── config-example.yaml
│         └── config.go
├── database
│         └── migrations
│             └── mysql
│                 └── README.md
│             └── seeders
│                 └── README.md
├── docker-compose.yml
├── go.mod
├── go.sum
├── internal
│         ├── delivery
│         │         ├── cron
│         │         │       └── init.go
│         │         └── http
│         │             ├── home_handler.go
│         │             ├── http.go
│         │             ├── route.go
│         │             ├── middleware
│         │             └── response
│         │                 ├── status.go
│         │                 ├── response.go
│         │                 └── errors.go
│         ├── domain
│         │         ├── entity 放置repository model struct
│         │         ├── repository 放置repository interface
│         │         └── usecase 放置usecase類struct/interface
│         ├── libs 基礎包，如mysql連線
│         ├── repository
│         └── usecase
└── logs
    └── gateway.2023-05-31-14-00
```

## build

放置dockerfile or script

## cmd

1. 對外入口點，依照建置服務建立資料夾，內部應只有 `main.go` 檔案與 main func 做啟動邏輯。
2. 不放任何`http` `redis` `mysql`的`init` or `start`邏輯

## configs

設定檔和讀取設定檔邏輯，包含重新讀取 config 邏輯。

## database

放置 migrations跟seeders，檔名規則是timestamp_action_tableName_table

```
create roles table => 20230531013052_create_roles_table

update roles table user_name => 20230531013052_update_roles_user_name_table
```

## internal

go 導入特殊限制，只允許父級別(layout)與父級別底下子包(build、cmd 等)導入，可限制其它專案不可導入

### delivery

該層將充當演示者。決定數據的呈現方式。
可以是 REST API、HTML 文件或 GRPC，無論交付類型如何。
該層也將處理來自用戶的輸入並將其發送到用例層。

#### http

1. gin router 註冊 API，包含處理 API request 參數取值與 userCase 回傳值到 response
2. http middleware同一放在`middleware`目錄中
3. handler走鋪平模式，不開目錄分類，比如role跟member邏輯都放在同層目錄而不另外開目錄分類
4. route.go放主要的dig注入跟route handler，不走分散
5. response
    - response.go json response 基本格式
    - status.go error code

### domain

存放所有層，會使用到的對象及方法，提供給 delivery、repository、usecase 呼叫，
這樣所有的依賴關係，都是單向的連結 domain，各個實作邏輯不會有 import 關係，
如 usecase 不會 import repository 取對象(model) 或是實作程式。

### repository

處理不含業務邏輯的redis/mysql/mongodb基礎操作邏輯，`.go`走鋪平模式

### usecase

處理核心業務邏輯處理，不能耦合 gin.Context 參數，需轉換內部使用 struct，後續可給 GRPC、WebSocket 等 delivery 複用邏輯。
`.go`走鋪平模式

# Error Code
1. 由後端定義 （廣泛定義即可），總共六碼，前二碼 服務，後四碼自定義
2. Error Code專門給前端做處理，message只是給後端自己看，前端會自己轉成對應多國語系

# API router

path命名方式一律才用不可數，member而非members

```
/v1/member/uuid
/v1/member
```

如果命名中有類似roleList請使用`-`當間隔變成`/role-list`

註冊route時有兩種寫法，請使用第一種

```go
// 第一種直接將path展開，優點是可以使用full path全局搜尋，缺點是每次都要重覆寫前綴path，降低代碼可讀性
route.GET("/user/:uid/role", role.get)
route.GET("/user/role", role.all)
route.POST("/user/role", role.create)
```

```go
// 第二種使用Group把前綴path拉出來，優點是解決重覆前綴path，提高代碼可讀性，缺點是不好使用full path全局搜尋
group := route.Group("/user")
{
   group.GET("/:uid/role", role.get)
   group.GET("/role", role.all)
   group.POST("/role", role.create)
}
```

> 如果是v1這種版控可以使用Group

# Time Format

請使用RFC3339，統一回 UTC +0 時區

```
2023-06-02T10:35:33Z
```

# Http Code

| 事件                                     | http code | 
|----------------------------------------|-----------|
| Request body參數類型有誤                     | 400       |
| Request body參數內容有誤                     | 400       |
| Request body參數正確，但業務邏輯有錯               | 200       |
| Request body參數正確，業務邏輯正確                | 200       |
| Request body參數正確，業務邏輯正確，但發生預期外事故(db掛掉) | 200       |

當發生error時統一由error struct所定義http code來回傳

# Response

當`新增某筆資料`時，請將新增完的資料當作Response body一併回傳

當`更新某筆資料`時，請將新增完的資料當作Response body一併回傳

Request 新增某筆role權限資料

```json
{
  "name": "test",
  "status": 1,
  "gateways": [
    "admin.account.management.page",
    "admin.account.management.select"
  ]
}
```

Response body內就要有該筆role資料

```json
{
  "status": {
    "code": 102000,
    "messages": "success"
  },
  "data": {
    "name": "test",
    "uid": "AM000019",
    "gateways": [
      "admin.account.management.page",
      "admin.account.management.select"
    ],
    "status": 1,
    "create_time": "2023-06-13T06:39:19Z",
    "update_time": "2023-06-13T06:39:19Z"
  }
}
```

# version.txt

放置release版號給CICD build docker image tag使用

# Gitlab flow

![gitlab-flow](gitlab-flow.png)

| 環境     | 說明     | 誰merge |
|--------|--------|--------|
| master | dev環境  | RD     |
| test   | test環境 | CI     |
| pre    | pre環境  | CI     |
| ol     | ol環境   | CI     |

## 開發流程

1. 從master拉出`branch(feature)`命名成jira單號
2. 當時做完`feature-A`時發`PR` merge 進`master`
3. 當`master`開發到一定階段後透過`CI` merge進`test`
4. 當`test`開發到一定階段後透過`CI` merge進`pre`
5. 當`pre`開發到一定階段後透過`CI` merge進`ol`
6. 需要`hotfix`時從`pre`拉出`branch(fix)`命名成jira單號，完成後發`PR` merge 進`master`與`pre`