# 结算业务实现说明

## 概述

本次实现完善了期货资产系统的结算业务，主要参考了原有的 `Trade.Trade()` 和 `Trade.Process()` 函数，但只处理单个账户的结算，不涉及两个账户之间的业务逻辑。

## 实现的功能

### 1. 结算用例 (SettleUseCase)

位置：`internal/usecase/settle.go`

主要功能：
- 处理账户结算业务 (`ProcessAccountSettle`)
- 计算盈亏 (`calculateProfitLoss`)
- 更新资产余额 (`updateAssetBalance`)
- 更新仓位信息 (`updatePositionInfo`)
- 生成结算日志 (`generateSettlementLogs`)
- 保存用户资产 (`saveUserAsset`)

### 2. 消息队列处理

位置：`handler/handler.go`

功能：
- 根据topic名称解析不同类型的消息
- 当topic为 `futures-settle-trade-account` 时，调用结算业务处理
- 记录处理结果和错误日志

## 核心业务逻辑

### 结算处理流程

1. **消息解析**：从 `AccountSettleEngine` 中提取用户ID、交易对、数量、价格等信息
2. **资产加载**：通过缓存仓库加载用户资产信息
3. **价格获取**：获取标记价格用于计算
4. **结算处理**：
   - 计算盈亏
   - 更新资产余额
   - 更新仓位信息
   - 生成结算日志
5. **数据保存**：保存更新后的用户资产

### 盈亏计算逻辑

支持三种仓位模式的盈亏计算：
- **多仓**：盈亏 = (当前价格 - 开仓均价) × 数量
- **空仓**：盈亏 = (开仓均价 - 当前价格) × 数量  
- **单向持仓**：根据仓位方向计算盈亏

### 不包含的业务逻辑

根据要求，以下涉及两个账户的业务逻辑未实现：
- 自成交判断和处理
- 备份Taker资产
- 合并交易日志
- 异步更新用户持仓信息（双账户）
- 异步处理其他操作（双账户相关）

## 技术特点

### 1. 依赖注入
使用 `go.uber.org/dig` 进行依赖注入，便于测试和维护。

### 2. 错误处理
完善的错误处理和日志记录，便于问题排查。

### 3. 模块化设计
将不同的功能拆分为独立的方法，提高代码可读性和可维护性。

### 4. 占位符实现
由于 `AccountSettleEngine` 的具体字段定义不明确，使用了占位符实现，实际使用时需要根据protobuf定义调整。

## 使用方法

### 1. 初始化结算用例

```go
// 在依赖注入容器中注册
settleUseCase := usecase.NewSettleUseCase(usecase.SettleUseCaseParam{
    CacheRepo: cacheRepo,
    PriceRepo: priceRepo,
})

// 设置到handler中
handler.SetSettleUseCase(settleUseCase)
```

### 2. 消息队列处理

当接收到topic为 `futures-settle-trade-account` 的消息时，系统会自动调用结算业务进行处理。

## 待完善的部分

### 1. protobuf字段映射
需要根据实际的 `AccountSettleEngine` protobuf定义，完善以下方法：
- `getUidFromAccountSettle`
- `getBaseQuoteFromAccountSettle`
- `getAmountFromAccountSettle`
- `getPriceFromAccountSettle`

### 2. 资产保存实现
`saveUserAsset` 方法需要根据实际的 `CacheRepository` 接口实现具体的保存逻辑。

### 3. 日志记录完善
`generateSettlementLogs` 方法需要实现具体的日志记录和消息队列发送逻辑。

## 测试建议

1. **单元测试**：为每个方法编写单元测试，特别是盈亏计算逻辑
2. **集成测试**：测试完整的结算流程
3. **性能测试**：测试高并发场景下的性能表现
4. **错误场景测试**：测试各种异常情况的处理

## 总结

本次实现提供了一个完整的结算业务框架，参考了原有的Trade函数设计，但简化了双账户相关的复杂逻辑。代码结构清晰，易于扩展和维护。在实际部署前，需要根据具体的protobuf定义和业务需求进行相应的调整。
